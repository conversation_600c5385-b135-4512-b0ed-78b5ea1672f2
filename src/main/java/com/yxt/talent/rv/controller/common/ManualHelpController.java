package com.yxt.talent.rv.controller.common;

import com.alibaba.fastjson2.JSON;
import com.xxl.job.core.util.DateUtil;
import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.util.Validate;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.rv.application.xpd.xpd.XpdInitDataService;
import com.yxt.talent.rv.application.xpd.result.XpdResultCalcService;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Date;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "手动辅助接口", description = "手动辅助接口")
@RequestMapping(value = "/manual/help")
public class ManualHelpController {
    @Value("${base.skmap.sptalentrvapi:}")
    private String skVal;
    private final XpdMapper xpdMapper;
    private final XpdInitDataService xpdInitDataService;
    private final XpdResultCalcService xpdResultCalcService;

    @Operation(summary = "初始化机构数据")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/initXpdInnerData")
    public String initXpdInnerData(@RequestParam String orgId) {
        normalCheckApi();
        xpdInitDataService.initGridInnerData(orgId, false);
        return "ok";
    }

    @Operation(summary = "导出机构初始化数据")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/exportXpdInitData", produces = "application/octet-stream")
    public void exportXpdInitData(@RequestParam String orgId, HttpServletResponse response) {
        normalCheckApi();
        try (OutputStream output = response.getOutputStream()) {
            String fileTime = DateUtil.format(new Date(), "yyMMdd");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileTime + "@xpdInitData.json");
            output.write(JSON.toJSONString(xpdInitDataService.exportInitData(orgId)).getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            throw new ApiException(ExceptionKey.SERVICE_INTERNAL_ERROR);
        }
    }

    @Operation(summary = "手动计算-按维度分层规则")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/calcXpdResultOnlyByDim")
    public String calcXpdResultOnlyByDim(@RequestParam String xpdId) {
        normalCheckApi();
        XpdPO xpdPO = xpdMapper.selectById(xpdId);
        Validate.isNotNull(xpdPO, "无效的xpdId");
        xpdResultCalcService.calcXpdResultOnlyByDim(xpdPO.getOrgId(), xpdPO.getId());
        return "ok";
    }

    @Operation(summary = "手动计算-盘点结果")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/calcXpdResult")
    public String calcXpdResult(@RequestParam String xpdId) {
        normalCheckApi();
        XpdPO xpdPO = xpdMapper.selectById(xpdId);
        Validate.isNotNull(xpdPO, "无效的xpdId");
        xpdResultCalcService.calcResult(xpdPO.getOrgId(), xpdPO.getId());
        return "ok";
    }

    private void normalCheckApi() {
        CommonUtils.normalCheckApi(skVal);
    }
}
