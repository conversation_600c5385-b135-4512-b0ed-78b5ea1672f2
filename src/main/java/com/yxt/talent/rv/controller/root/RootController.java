package com.yxt.talent.rv.controller.root;

import com.yxt.common.annotation.Auth;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.Validate;
import com.yxt.sptalentapifacade.bean.spjq.SpJqDetail4Get;
import com.yxt.sptalentapifacade.bean.spjq.SpJqItem4Get;
import com.yxt.talent.rv.application.democopy.DemoCopyService;
import com.yxt.talent.rv.application.dmp.calc.DmpCalculator;
import com.yxt.talent.rv.application.dmp.legacy.DmpAppManage;
import com.yxt.talent.rv.application.dmp.task.legacy.DmpTaskAppService;
import com.yxt.talent.rv.application.prj.calc.PrjCalculator;
import com.yxt.talent.rv.application.prj.prj.legacy.PrjAppService;
import com.yxt.talent.rv.application.prj.user.legacy.UserPrjSummaryAppManage;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.jwt.JwtUserInfo;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.DmpMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.DmpPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjPO;
import com.yxt.talent.rv.infrastructure.service.auth.AuthenticateService;
import com.yxt.talent.rv.infrastructure.service.remote.SptalentAclService;
import com.yxt.talent.rv.infrastructure.service.remote.impl.SptalentAclServiceImpl;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.Constants.SEPARATOR_CHAR_ROLE_ALL;
import static com.yxt.common.enums.AuthType.CUSTOM;
import static com.yxt.common.util.BeanHelper.bean2Json;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/root")
public class RootController {
    private final DemoCopyService demoCopyService;
    private final AuthenticateService authenticateService;

    @Operation(summary = "机构复制手动补数据")
    @ResponseStatus(OK)
    @PostMapping(value = "/demo/copy", produces = MEDIATYPE)
    @Auth(type = {CUSTOM}, codes = {SEPARATOR_CHAR_ROLE_ALL})
    public void copyOrg(@RequestBody OrgInit4Mq orgInit) {
        log.info("LOG10082:{}", bean2Json(orgInit, ALWAYS));
        demoCopyService.preGenIdMap(orgInit);
        demoCopyService.demoCopy(orgInit);
    }

    @Operation(summary = "手动生成jwt")
    @ResponseStatus(OK)
    @PostMapping(value = "/jwt", produces = MEDIATYPE, consumes = MEDIATYPE)
    @Auth(type = {CUSTOM})
    public String genJwt(@RequestBody JwtUserInfo claim) {
        return authenticateService.generateToken(claim);
    }

//    @Auth(type = CUSTOM)
//    @ResponseStatus(OK)
//    @GetMapping(value = "/{orgId}/dmp/{dmpId}/manual", produces = MEDIATYPE)
//    public void manualCalculateDmp(
//            @PathVariable String orgId, @PathVariable String dmpId,
//            @RequestParam(defaultValue = "false") boolean isForce) {
//        log.info("LOG61070:manualCalculateDmp orgId={},dmpId={},isForce={}", orgId, dmpId, isForce);
//        DmpPO dmp = dmpMapper.selectByOrgIdAndId(orgId, dmpId);
//        Validate.isNotNull(dmp, ExceptionKeys.DMP_NOT_EXISTED);
//        if (dmp == null) {
//            throw new ApiException(ExceptionKeys.DMP_NOT_EXISTED);
//        }
//        dmpCalculator.manualCalculateDmp(dmp, isForce, "system");
//    }
//
//    @Auth(type = CUSTOM)
//    @ResponseStatus(OK)
//    @GetMapping(value = "/{orgId}/dmp/{dmpId}/manual/userresult", produces = MEDIATYPE)
//    public void triggerCalculateDmpUserResult(
//            @PathVariable String orgId, @PathVariable String dmpId) {
//        DmpPO dmp = dmpMapper.selectByOrgIdAndId(orgId, dmpId);
//        Validate.isNotNull(dmp, ExceptionKeys.DMP_NOT_EXISTED);
//        log.info("LOG61060:triggerCalculateDmpUserResult orgId={},dmpId={}", orgId, dmpId);
//        dmpCalculator.triggerCalculateDmpUserResult(orgId, dmpId);
//    }
//
//    @Auth(type = CUSTOM)
//    @ResponseStatus(OK)
//    @GetMapping(value = "/{orgId}/dmp/{dmpId}/task/{taskId}/manual", produces = MEDIATYPE)
//    public void triggerCalculateDmpTask(
//            @PathVariable String orgId, @PathVariable String dmpId, @PathVariable String taskId,
//            @RequestParam(defaultValue = "false") boolean isForce) {
//        DmpPO dmp = dmpMapper.selectByOrgIdAndId(orgId, dmpId);
//        Validate.isNotNull(dmp, ExceptionKeys.DMP_NOT_EXISTED);
//        log.info("LOG61080:triggerCalculateDmpTask orgId={},dmpId={}", orgId, dmpId);
//        dmpCalculator.triggerCalculateDmpTask(orgId, dmpId, taskId, isForce, "root");
//    }
//
//    @Auth(type = CUSTOM)
//    @ResponseStatus(OK)
//    @Operation(summary = "初始化mapPos表")
//    @PostMapping(value = "/mgr/dmp/project/init/dmppos", produces = MEDIATYPE)
//    public void initDmpPos() {
//        dmpAppManage.initDmpPos();
//    }


    // 清洗rv_user_data表中学员的完成维度数
//    @Auth(type = CUSTOM)
//    @ResponseStatus(OK)
//    @GetMapping(value = "/userdata/refresh")
//    public void refreshUserData(HttpServletRequest request) {
//        List<PrjPO> prjs = prjAppService.findRefreshableProjects();
//        for (PrjPO prj : prjs) {
//            try {
//                boolean complete = prjCalculator.isPrjCalcFinished(prj.getOrgId(), prj.getId());
//                // 盘点计算完成人才重新定义
//                if (complete) {
//                    userPrjSummaryAppManage.talentDefined(
//                            prj.getOrgId(), prj.getId(), "root", new ArrayList<>());
//                }
//            } catch (Exception e) {
//                log.error("LOG64740:{}", prj.getId(), e);
//            }
//        }
//    }
//
//    // 清洗rv_dmp_task_dim表中jqTplCatId和jqTplCatName字段
//    @Auth(type = CUSTOM)
//    @ResponseStatus(OK)
//    @GetMapping(value = "/dmptaskdim/refresh")
//    public void refreshDmpTaskDimCat(
//            @RequestParam(defaultValue = "") String orgId,
//            @RequestParam(defaultValue = "") String dmpId) {
//        List<DmpTaskDimPO> dmpTaskDimEntities =
//                dmpTaskAppService.findRefreshableDmpTaskDim(orgId, dmpId);
//        Map<Pair<String, String>, List<DmpTaskDimPO>> orgDmpIdMap = dmpTaskDimEntities.stream()
//                .collect(Collectors.groupingBy(d -> Pair.of(d.getOrgId(), d.getDmpId())));
//        // 遍历orgDmpIdMap,根据orgId和dmpId查询出jqTplCatId和jqTplCatName
//        for (Map.Entry<Pair<String, String>, List<DmpTaskDimPO>> entry : orgDmpIdMap.entrySet()) {
//            String innerOrgId = null;
//            String innerDmpId = null;
//            try {
//                innerOrgId = entry.getKey().getLeft();
//                innerDmpId = entry.getKey().getRight();
//                log.info("LOG65250:refreshDmpTaskDim orgId={},dmpId={}", innerOrgId, innerDmpId);
//                List<DmpTaskDimPO> dmpTaskDims = entry.getValue();
//                DmpPO dmp = dmpMapper.selectByOrgIdAndId(innerOrgId, innerDmpId);
//                if (dmp == null) {
//                    continue;
//                }
//                SpJqDetail4Get spJqDetail4Get =
//                        sptalentAclService.getSpJqDetail4Get(dmpTaskDims, innerOrgId, dmp);
//                if (spJqDetail4Get == null) {
//                    continue;
//                }
//                List<SpJqItem4Get> jqSetting4GetList = spJqDetail4Get.getJqSetting4GetList();
//                for (DmpTaskDimPO dmpTaskDim : dmpTaskDims) {
//                    for (SpJqItem4Get jqSetting4Get : jqSetting4GetList) {
//                        if (Objects.equals(dmpTaskDim.getJqDimId(), jqSetting4Get.getDimId())) {
//                            dmpTaskDim.setJqTplCatId(
//                                    String.valueOf(jqSetting4Get.getTplCatalogId()));
//                            dmpTaskDim.setJqTplCatName(jqSetting4Get.getTplCatalogName());
//                            EntityUtil.setUpdate(dmpTaskDim, "root");
//                            dmpTaskAppService.updateDmpTaskDim(dmpTaskDim);
//                        }
//                    }
//                }
//            } catch (Exception e) {
//                log.error("LOG65260:orgId={}, dmpId={}", innerOrgId, innerDmpId, e);
//            }
//        }
//    }

}
