package com.yxt.talent.rv.controller.manage.xpd.result.viewobj;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.talent.rv.application.common.dto.UserBaseInfoDTO;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(description = "表格视图(落位结果)")
public class XpdDimCombTableResultVO extends UserBaseInfoDTO {

    @Schema(description = "xpdId")
    private String xpdId;

    @Schema(description = "所属宫格id")
    private String cellId;

    @I18nTranslate(codeField = "cellNameI18n")
    @Schema(description = "所属宫格名称")
    private String cellName;

    @Schema(description = "所属宫格名称国际化", hidden = true)
    private String cellNameI18n;

    @JsonProperty(value = "xSdDimId")
    @Schema(description = "x轴维度Id")
    private String xSdDimId;

    @JsonProperty(value = "xSdDimName")
    @Schema(description = "x轴维度名称")
    private String xSdDimName;

    @JsonProperty(value = "xDimLevelId")
    @Schema(description = "x轴维度分层id")
    private String xDimLevelId;

    @JsonProperty(value = "xDimLevelIndex")
    @Schema(description = "x轴维度分层序号")
    private Integer xDimLevelIndex;

    @JsonProperty(value = "xDimLevelName")
    @Schema(description = "x轴维度分层名称")
    private String xDimLevelName;

    @JsonProperty(value = "xDimLevelNameI18n")
    @Schema(description = "x轴维度分层名称国际化", hidden = true)
    private String xDimLevelNameI18n;

    @JsonProperty(value = "ySdDimId")
    @Schema(description = "y轴维度id")
    private String ySdDimId;

    @JsonProperty(value = "ySdDimName")
    @Schema(description = "y轴维度名称")
    private String ySdDimName;

    @JsonProperty(value = "yDimLevelId")
    @Schema(description = "y轴维度分层id")
    private String yDimLevelId;

    @JsonProperty(value = "yDimLevelIndex")
    @Schema(description = "y轴维度分层序号")
    private Integer yDimLevelIndex;

    @JsonProperty(value = "yDimLevelName")
    @Schema(description = "y轴维度分层名称")
    private String yDimLevelName;

    @JsonProperty(value = "yDimLevelNameI18n")
    @Schema(description = "y轴维度分层名称国际化", hidden = true)
    private String yDimLevelNameI18n;

}
