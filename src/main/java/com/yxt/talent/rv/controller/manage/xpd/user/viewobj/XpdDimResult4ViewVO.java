package com.yxt.talent.rv.controller.manage.xpd.user.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 *
 * @date 2024/12/17
 */
@Data
public class XpdDimResult4ViewVO {

    @Schema(description = "维度id")
    private String dimId;

    @Schema(description = "维度名称")
    private String dimName;

    @Schema(description = "维度计算结果")
    private String result;

    @Schema(description = "分层结果")
    private String gridLevelName;

    @Schema(description = "所处位置")
    private BigDecimal posPercent;
}
