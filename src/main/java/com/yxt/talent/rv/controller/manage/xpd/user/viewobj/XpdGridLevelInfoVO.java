package com.yxt.talent.rv.controller.manage.xpd.user.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 *
 * @date 2024/12/17
 */
@Data
public class XpdGridLevelInfoVO {

    @Schema(description = "层级id")
    private String gridLevelId;

    @Schema(description = "层级名称")
    private String gridLevelName;

    @Schema(description = "排序")
    private Integer orderIndex;

}
