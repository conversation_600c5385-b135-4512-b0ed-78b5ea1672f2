package com.yxt.talent.rv.controller.common;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.spsdk.common.bean.RuleMainBase;
import com.yxt.spsdk.common.bean.SpRuleBean;
import com.yxt.spsdk.common.bean.SpRuleColumnMetaDto;
import com.yxt.spsdk.common.bean.UserBasicInfo;
import com.yxt.spsdk.common.component.SpRuleService;
import com.yxt.spsdk.common.utils.YxtBasicUtils;
import com.yxt.talent.rv.application.xpd.grid.XpdGridAppManage;
import com.yxt.talent.rv.application.xpd.grid.XpdGridAppService;
import com.yxt.talent.rv.controller.common.viewobj.PrjLabelVO;
import com.yxt.talent.rv.controller.common.viewobj.XpdGridCellVO;
import com.yxt.talent.rv.controller.common.viewobj.XpdGridVO;
import com.yxt.talent.rv.controller.manage.prj.dim.viewobj.PrjDimVO;
import com.yxt.talent.rv.controller.manage.prj.dim.viewobj.PrjUserDimConfVO;
import com.yxt.talent.rv.controller.manage.xpd.grid.viewobj.XpdGridLevelVO;
import com.yxt.talent.rv.controller.manage.xpd.result.query.RuleColumnReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequestMapping("")
@RequiredArgsConstructor
@Tag(name = "管理端-新盘点公用接口", description = "管理端-新盘点公用接口")
public class XpdCommonController {
    private final SpRuleService spRuleService;
    private final AuthService authService;
    private final XpdGridAppService xpdGridAppService;
    private final XpdGridAppManage xpdGridAppManage;

    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    @Operation(summary = "获取盘点项目宫格(包括宫格分层和维度组)", description = "如果获取的是机构模板宫格，xpdId传00000000-0000-0000-0000-000000000000")
    @Parameters({
        @Parameter(name = "xpdId", description = "盘点id,如果要获取模型宫格中的格子信息，传00000000-0000-0000-0000-000000000000", in = ParameterIn.QUERY),
        @Parameter(name = "sourceType", description = "来源,0-内置,1-自建; 默认内置", in = ParameterIn.QUERY),
        @Parameter(name = "gridType", description = "宫格类型,0-四宫格,1-九宫格,2-十六宫格；默认九宫格", in = ParameterIn.QUERY)})
    @GetMapping(value = "/client/xpd/grids", produces = MEDIATYPE)
    public XpdGridVO getXpdGrids(
        @RequestParam(defaultValue = "00000000-0000-0000-0000-000000000000") String xpdId,
        @RequestParam(required = false, defaultValue = "0") int sourceType,
        @RequestParam(required = false, defaultValue = "1") int gridType) {
        UserCacheBasic userCache = authService.getUserCacheBasic();
        String orgId = userCache.getOrgId();
        return xpdGridAppManage.getXpdGrid(orgId, xpdId, sourceType, gridType);
    }

    // 获取盘点的格子信息，根据维度组的不同获取不同的格子信息
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    @Operation(summary = "获取盘点的格子信息")
    @Parameters({
        @Parameter(name = "dimCombId", description = "维度组id, 如果宫格是统一配置则无需传; 如果是按维度组配置，则必传", in = ParameterIn.QUERY),
        @Parameter(name = "xpdId", description = "盘点id, 如果要获取模型宫格中的格子信息，传00000000-0000-0000-0000-000000000000", in = ParameterIn.QUERY)})
    @GetMapping(value = "/client/grid/{gridId}/cell", produces = MEDIATYPE)
    public CommonList<XpdGridCellVO> getXpdGridCells(
        @RequestParam(defaultValue = "00000000-0000-0000-0000-000000000000") String xpdId, @PathVariable String gridId,
        @RequestParam(defaultValue = "") String dimCombId) {
        UserCacheBasic userCache = authService.getUserCacheBasic();
        String orgId = userCache.getOrgId();
        return new CommonList<>(xpdGridAppService.getXpdGridCells(orgId, gridId, xpdId, dimCombId));
    }

    @Operation(summary = "获取盘点项目关联的维度信息")
    @GetMapping(value = {
        "/client/dimensioncfg/dimension/{projectId}",
        "/mgr/dimensionconfig/dimension/{projectId}"}, produces = Constants.MEDIATYPE)
    @Auth(type = AuthType.TOKEN)
    public List<PrjDimVO> getXpdDimList(HttpServletRequest request, @PathVariable String projectId) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return xpdGridAppService.getXpdDimList(userCache.getOrgId(), projectId);
    }

    @Operation(summary = "新盘点-查询盘点九宫格标签")
    @Parameters(value = {
        @Parameter(name = "projectId", description = "项目id", in = ParameterIn.QUERY),
        @Parameter(name = "dimCombId", description = "维度组合id", in = ParameterIn.QUERY)})
    @GetMapping(value = "/mgr/project/project/labels/{projectId}/{dimCombId}", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public CommonList<PrjLabelVO> getGridLabels(
        HttpServletRequest request, @PathVariable String projectId,
        @PathVariable String dimCombId) {
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        return new CommonList<>(xpdGridAppService.getGridLabels(operator.getOrgId(), projectId, dimCombId));
    }

    @Operation(summary = "项目维度动态表头")
    @Parameter(name = "projectId", description = "盘点项目id", required = true, in = ParameterIn.PATH)
    @GetMapping(value = "/client/dimensioncfg/{projectId}", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public CommonList<PrjUserDimConfVO> getProjectDimensionConfig(
        HttpServletRequest request,
        @PathVariable String projectId) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return new CommonList<>(xpdGridAppManage.findDimensionHeader(userCache.getOrgId(), projectId));
    }

    @Operation(summary = "分层标准列表")
    @GetMapping(value = "/client/dimensioncfg/level/{projectId}")
    @Auth(codes = {AUTH_CODE_ALL})
    @ResponseStatus(OK)
    public List<XpdGridLevelVO> getGridLevel(HttpServletRequest request, @PathVariable String projectId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return xpdGridAppManage.getPrjGridLevel(userCache.getOrgId(), projectId);
    }

    @Nullable
    @Operation(summary = "规则配置数据")
    @PostMapping(value = "mgr/xpd/common/rule/config")
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public List<SpRuleColumnMetaDto> ruleConfig(@RequestBody List<RuleColumnReq> columnList) {
        UserBasicInfo userCache = YxtBasicUtils.userInfo();
        List<SpRuleColumnMetaDto> retList = new ArrayList<>();
        for (RuleColumnReq ruleCol : columnList) {
            RuleMainBase mainData = new RuleMainBase();
            mainData.setOrgId(userCache.getOrgId());
            mainData.setLocale(userCache.getLocale());
            mainData.setBizId(ruleCol.getBizId());
            SpRuleColumnMetaDto colMeta = spRuleService.ruleMetaInfo(mainData, ruleCol.getColumnType());
            if (colMeta != null) {
                retList.add(colMeta);
            }
        }
        return retList;
    }

    @Operation(summary = "规则数据结构")
    @GetMapping(value = "mgr/xpd/common/rule/submit/struct")
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public SpRuleBean ruleSubmitStruct() {
        return null;
    }
}
