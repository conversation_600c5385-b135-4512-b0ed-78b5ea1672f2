package com.yxt.talent.rv.controller.manage.calimeet;

import com.alibaba.fastjson2.JSONObject;
import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.GenericCommonData;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4ReqDTO;
import com.yxt.modelhub.api.utils.QueryUtil;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.rv.application.calimeet.legacy.CaliMeetAppManage;
import com.yxt.talent.rv.application.calimeet.legacy.CaliMeetAppService;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetCreateCmd;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetQuery;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetCreateResultVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetListVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetQtyVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetUserSimpleVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.RvCalibration4Create;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.RvCalibration4Get;
import com.yxt.talent.rv.infrastructure.common.annotation.ValidateGroup;
import com.yxt.talent.rv.infrastructure.common.constant.RvAuditLogConstants;
import com.yxt.talent.rv.infrastructure.common.utilities.util.ApassEntityUtils;
import com.yxt.talent.rv.infrastructure.config.swagger.SwaggerPageQuery;
import com.yxt.talent.rv.infrastructure.service.audit.AuditingPlus;
import com.yxt.talent.rv.infrastructure.service.audit.calimeet.CaliMeetUpdateAuditLogStrategy;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import com.yxt.modelhub.api.bean.dto.SearchDTO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.CALI_MEET_ADD;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.CALI_MEET_DEL;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.CALI_MEET_END;
import static org.springframework.http.HttpStatus.CREATED;
import static org.springframework.http.HttpStatus.NO_CONTENT;
import static org.springframework.http.HttpStatus.OK;

/**
 * 盘点校准会
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "盘点校准会", description = "盘点校准会")
@RequestMapping(value = "/mgr/calibration")
public class CaliMeetManageController {
    private final CaliMeetAppService caliMeetAppService;
    private final CaliMeetAppManage caliMeetAppManage;
    private final AuthService authService;

    @Operation(summary = "新增校准会基础信息")
    @PostMapping(value = "", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(CREATED)
    @Auth(codes = CALI_MEET_ADD)
    @Auditing
    //@AuditingPlus(strategyClass = CaliMeetCreateAuditLogStrategy.class)
    @EasyAuditLog(value = RvAuditLogConstants.CALI_MEET_ADD, paramExp = "#command")
    public CaliMeetCreateResultVO createBasic(
            HttpServletRequest request,
            @Validated(ValidateGroup.WhenCreate.class) @RequestBody CaliMeetCreateCmd command) {
        UserCacheDetail operator = authService.getUserCacheDetail(request);
        // 会议组织者, 如果为空、默认为当前用户
        if (command.getOrganizerList().isEmpty()) {
            command.setOrganizerList(Collections.singletonList(operator.getUserId()));
        }
        return caliMeetAppManage.saveCaliMeet(operator, command, ApiUtil.getToken(request));
    }

    @Operation(summary = "编辑校准会基础信息")
    @PutMapping(value = "", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(codes = CALI_MEET_ADD)
    @Auditing
    @AuditingPlus(strategyClass = CaliMeetUpdateAuditLogStrategy.class)
    //@EasyAuditLog(value = RvAuditLogConstants.CALI_MEET_UPDATE, paramExp = "#caliMeetCreateCmd")
    public CaliMeetCreateResultVO updateBasic(
            HttpServletRequest request, @RequestBody @Validated CaliMeetCreateCmd caliMeetCreateCmd) {
        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
        // 会议组织者, 如果为空、默认为当前用户
        if (caliMeetCreateCmd.getOrganizerList().isEmpty()) {
            caliMeetCreateCmd.setOrganizerList(Collections.singletonList(currentUser.getUserId()));
        }
        return caliMeetAppManage.saveCaliMeet(
                currentUser, caliMeetCreateCmd, ApiUtil.getToken(request));
    }

    @Operation(summary = "浏览|跟踪会议基础信息")
    @GetMapping(value = "/basic/{id}")
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public CaliMeetVO getMeetingDetail(HttpServletRequest request, @PathVariable String id) {
        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
        return caliMeetAppService.getMeeting4GetById(
                id, currentUser.getOrgId(), currentUser.getLocale());
    }

    @SwaggerPageQuery
    @Operation(summary = "盘点校准会列表")
    @PostMapping(value = "/list", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public PagingList<CaliMeetListVO> list(
            HttpServletRequest request,
            @RequestParam(required = false, defaultValue = "") String keyword,
            @RequestBody CaliMeetQuery search) {
        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
        return caliMeetAppService.findPage(ApiUtil.getPageRequest(request), currentUser, search, true);
    }

    @Operation(summary = "删除会议")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(NO_CONTENT)
    @Auth(codes = CALI_MEET_DEL)
    @Auditing
    //@AuditingPlus(strategyClass = CaliMeetDeleteAuditLogStrategy.class)
    @EasyAuditLog(value = RvAuditLogConstants.CALI_DELETE, paramExp = "#id")
    public void deleteMeeting(HttpServletRequest request, @PathVariable String id) {
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        caliMeetAppService.deleteMeeting(id, operator.getUserId(), operator.getOrgId());
    }

    @Operation(summary = "结束校准会")
    @PutMapping(value = "/close/{id}")
    @ResponseStatus(NO_CONTENT)
    @Auth(codes = CALI_MEET_END)
    @Auditing
    //@AuditingPlus(strategyClass = CaliMeetCloseAuditLogStrategy.class)
    @EasyAuditLog(value = RvAuditLogConstants.CALI_CLOSE, paramExp = "#id")
    public void closeMeeting(HttpServletRequest request, @PathVariable String id) {
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        caliMeetAppService.closeMeeting(id, operator.getUserId(), operator.getOrgId());
    }

    @Operation(summary = "返回进行中的校准会数量")
    @Parameters({@Parameter(name = "projectId", description = "盘点项目Id", in = ParameterIn.PATH)})
    @GetMapping(value = "/count/underway/{projectId}")
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public CaliMeetQtyVO countUnderwayMeeting(
            HttpServletRequest request, @PathVariable String projectId) {
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        return caliMeetAppService.checkResultBy(operator.getOrgId(), projectId);
    }

    @Operation(summary = "校准会设置显示路径")
    @PutMapping(value = "/route/{meetingId}/{routeStatus}")
    @Parameters({
            @Parameter(name = "meetingId", description = "校准会id", in = ParameterIn.PATH),
            @Parameter(name = "routeStatus", description = "校准会路径是否显示 0 关闭 1 显示.", in = ParameterIn.QUERY)})
    @ResponseStatus(NO_CONTENT)
    @Auth(type = TOKEN)
    public void routeMeeting(
            HttpServletRequest request, @PathVariable String meetingId,
            @PathVariable Integer routeStatus) {
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        caliMeetAppService.routeMeeting(
                meetingId, operator.getOrgId(), operator.getUserId(), routeStatus);
    }

    @Operation(summary = "开启校准会")
    @PutMapping(value = "/open/{id}")
    @ResponseStatus(NO_CONTENT)
    @Auth(codes = CALI_MEET_END)
    @Auditing
    //@AuditingPlus(strategyClass = CaliMeetCloseAuditLogStrategy.class)
    //@EasyAuditLog(value = RvAuditLogConstants.CALI_CLOSE, paramExp = "#id")
    public void openMeeting(HttpServletRequest request, @PathVariable String id) {
        UserCacheDetail operator = authService.getUserCacheDetail(request);
        String token = ApiUtil.getToken(request);
        caliMeetAppService.openMeeting(id, operator.getUserId(), operator.getOrgId(), operator, token);
    }

    @SwaggerPageQuery
    @Operation(summary = "盘点校准会列表-apass")
    @PostMapping(value = "/pagelist", produces = MEDIATYPE)
    @Parameters(value = {
            @Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
            @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public PagingList<RvCalibration4Get>  pagelist( HttpServletRequest request,
            @RequestParam(required = false, defaultValue = "") String keyword,
            @RequestBody SearchDTO bean) {
        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
        CaliMeetQuery search = convertPageDto(bean);
        PagingList<CaliMeetListVO> pageList = caliMeetAppService.findPage(ApiUtil.getPageRequest(request), currentUser, search, false);

        PagingList<RvCalibration4Get> result = new PagingList<>();
        BeanCopierUtil.copy(pageList, result);
        if (CollectionUtils.isEmpty(pageList.getDatas())) {
            return result;
        }
        List<RvCalibration4Get> datas = new ArrayList<>();
        pageList.getDatas().forEach(item -> {
            RvCalibration4Get vo = convertGetBean(item);
            datas.add(vo);
        });
        result.setDatas(datas);
        return result;
    }

    @Operation(summary = "新增校准会基础信息-apass")
    @PostMapping(value = "/create", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(CREATED)
    @Auth(codes = CALI_MEET_ADD)
    @Auditing
    //@AuditingPlus(strategyClass = CaliMeetCreateAuditLogStrategy.class)
    //@EasyAuditLog(value = RvAuditLogConstants.CALI_MEET_ADD, paramExp = "#command")
    public GenericCommonData<String> create(
            HttpServletRequest request,
            @Validated(ValidateGroup.WhenCreate.class) @RequestBody RvCalibration4Create bean) {
        UserCacheDetail operator = authService.getUserCacheDetail(request);
        CaliMeetCreateCmd command = convertCreateBean(bean);
        // 会议组织者, 如果为空、默认为当前用户
        if (command.getOrganizerList().isEmpty()) {
            command.setOrganizerList(Collections.singletonList(operator.getUserId()));
        }
        caliMeetAppManage.saveCaliMeet(operator, command, ApiUtil.getToken(request));
        return new GenericCommonData();
    }

    private CaliMeetCreateCmd convertCreateBean(RvCalibration4Create bean) {
        CaliMeetCreateCmd createBean = new CaliMeetCreateCmd();
        BeanHelper.copyProperties(bean, createBean);
        createBean.setMeetName(bean.getName());
        createBean.setMeetTime(bean.getMeettime());
        if (bean.getMeetorganizator() != null && bean.getMeetorganizator().size() > 0) {
            createBean.setOrganizerList(
                    bean.getMeetorganizator().stream()
                            .map(AmSlDrawer4ReqDTO::getId)
                            .collect(Collectors.toList()));
        }
        if (bean.getMeetcalibrator() != null && bean.getMeetcalibrator().size() > 0) {
            createBean.setTalentCommitteeList(
                    bean.getMeetcalibrator().stream()
                            .map(AmSlDrawer4ReqDTO::getId)
                            .collect(Collectors.toList()));
        }
        return createBean;
    }

    private RvCalibration4Get convertGetBean(CaliMeetListVO vo) {
        RvCalibration4Get result = new RvCalibration4Get();
        BeanHelper.copyProperties(vo, result);
        result.setName(vo.getMeetName());
        result.setMeetstatus(vo.getMeetStatus().toString());
        result.setMeettime(vo.getMeetTime());
        result.setCalibrationnumbers(vo.getUserCount());
        result.setCreateUserId(ApassEntityUtils.createDrawer4UserRespDTO(vo.getCreateUserName(), vo.getCreateUserId()));

        // 处理组织者
        result.setMeetorganizator(ApassEntityUtils.createDrawer4UserRespDTO(vo.getOrganizerList().get(0).getUserName(),
                vo.getOrganizerList().get(0).getUserId()));

        // 处理人才委员会
        List<CaliMeetUserSimpleVO> meetUsers = vo.getTalentCommitteeList();
        List<Object> objs = new ArrayList<>();
        meetUsers.forEach(item -> {
            JSONObject obj = new JSONObject();
            obj.put("id", item.getUserId());
            obj.put("name", item.getUserName());
            objs.add(obj);
        });
        result.setMeetcalibrator(ApassEntityUtils.createAmSlDrawer4RespDTOList(objs));

        return result;
    }

    private CaliMeetQuery convertPageDto(SearchDTO bean) {
        CaliMeetQuery pageParam = new CaliMeetQuery();
        QueryUtil.Search search = QueryUtil.parse(bean);
        Map<String, String> filterEqMap = search.getFilterEq();
        Map<String, List<String>> filterInMap = search.getFilterIn();
        QueryUtil.SearchQuery searchQuery = search.getSearchLike();
        pageParam.setKeyword(searchQuery.getValue());

        pageParam.setProjectId(StringUtils.isBlank(filterEqMap.get("projectId")) ?
                null : filterEqMap.get("projectId"));
        return pageParam;
    }



}
