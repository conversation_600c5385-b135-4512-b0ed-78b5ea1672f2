package com.yxt.talent.rv.controller.manage.xpd.result.query;

import com.yxt.criteria.Query;
import com.yxt.talent.rv.controller.common.query.SearchUdpScopeAuthQuery;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "新盘点-落位结果-搜索")
public class XpdResultQuery extends SearchUdpScopeAuthQuery implements Query {

    @NotNull(message = ExceptionKeys.XPD_RESULT_QUERY_TYPE_INVALID)
    @Min(value = 1, message = ExceptionKeys.XPD_RESULT_QUERY_TYPE_INVALID)
    @Max(value = 3, message = ExceptionKeys.XPD_RESULT_QUERY_TYPE_INVALID)
    @Schema(description = "查询类型，1-维度分层(按单维度查询), 2-落位结果(按维度组查询), 3-人才分层(按项目结果查询)")
    private Integer queryType;

    @Schema(description = "维度/维度组ID, 根据queryType判断不同的含义")
    private String targetId;

    @Schema(description = "维度/人才分层ID")
    private List<String> levelIds;

    @Schema(description = "格子id，特应用于维度落位结果列表的搜索")
    private List<String> cellIds;

    @Schema(description = "是否返回子条目，特指；当查询人员项目结果时是否返回维度明细；当查询人员维度结果时是否返回指标明细")
    private boolean includeSub;

}
