package com.yxt.talent.rv.controller.common.query;

import com.yxt.criteria.AdminScopeQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;

import java.util.ArrayList;
import java.util.List;

import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.distinct;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.filterNullAndBlank;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class ScopeAuthQuery implements AdminScopeQuery {

    @Schema(description = "所管理的用户id")
    private List<String> scopeUserIds = new ArrayList<>();

    @Schema(description = "所管理的部门及其后代部门id")
    private List<String> scopeDeptIds = new ArrayList<>();

    @Schema(description = "是否管理员,如果是管理员默认最高权限, 如果不是管理员，但是core返回的权限数据中isAllOrgPermission=1时，此值也会被设置为1，等同于admin", hidden = true)
    private boolean admin;

    @Schema(description = "是否开启鉴权,用于判断是否使用权限, 有些场景下这里的字段并不会参与权限判断,但是又使用了这个bean,那么可以将开关关闭", hidden = true)
    private boolean openAuth = true;

    /**
     * 用于短路中断查询
     * @return
     */
    public boolean isEmptyAuth() {
        return openAuth && !admin && (scopeUserIds == null || scopeUserIds.isEmpty()) &&
               (scopeDeptIds == null || scopeDeptIds.isEmpty());
    }

    @Override
    public List<String> getScopeDeptIds() {
        return new ArrayList<>(distinct(filterNullAndBlank(scopeDeptIds)));
    }

    @Override
    public List<String> getScopeUserIds() {
        return new ArrayList<>(distinct(filterNullAndBlank(scopeUserIds)));
    }

    public static void main(String[] args) {
        boolean openAuth = false;
        boolean admin = true;
        List<String> scopeUserIds = new ArrayList<>();
        List<String> scopeDeptIds = new ArrayList<>();
        boolean b = openAuth && !admin && (scopeUserIds == null || scopeUserIds.isEmpty()) &&
                    (scopeDeptIds == null || scopeDeptIds.isEmpty());
        System.out.println(b);
    }
}
