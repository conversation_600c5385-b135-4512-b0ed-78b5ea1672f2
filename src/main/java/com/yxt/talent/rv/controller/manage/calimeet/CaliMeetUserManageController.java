package com.yxt.talent.rv.controller.manage.calimeet;

import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.spsdk.audit.AuditLogHooker;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.rv.application.calimeet.expt.CaliMeetFileExporter;
import com.yxt.talent.rv.application.calimeet.impt.CaliMeetResultFileImporter;
import com.yxt.talent.rv.application.calimeet.impt.CaliMeetUserImporter;
import com.yxt.talent.rv.application.calimeet.legacy.CaliMeetAppManage;
import com.yxt.talent.rv.application.calimeet.legacy.CaliMeetAppService;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetUserBatchAddCmd;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetUserUpdateCmd;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetUserQuery;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetUserVO;
import com.yxt.talent.rv.infrastructure.common.constant.AuthCodes;
import com.yxt.talent.rv.infrastructure.common.constant.RvAuditLogConstants;
import com.yxt.talent.rv.infrastructure.config.swagger.SwaggerPageQuery;
import com.yxt.talent.rv.infrastructure.service.audit.AuditingPlus;
import com.yxt.talent.rv.infrastructure.service.audit.calimeet.CaliMeetUserAddAuditLogStrategy;
import com.yxt.talent.rv.infrastructure.service.audit.calimeet.CaliMeetUserDelAuditLogStrategy;
import com.yxt.talent.rv.infrastructure.service.file.command.FileImportCmd;
import com.yxt.talent.rv.infrastructure.service.file.dto.FileImportResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.CALI_MEET_USER_DEL;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.CALI_MEET_USER_EXPORT;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.CALI_MEET_USER_IMPORT;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.CALI_MEET_USER_SET;
import static io.swagger.v3.oas.annotations.enums.ParameterIn.QUERY;
import static org.springframework.http.HttpStatus.CREATED;
import static org.springframework.http.HttpStatus.OK;

/**
 * 校准会人员列表
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "盘点校准会-校准人员", description = "盘点校准会-校准人员")
@RequestMapping(value = "/mgr/meeting/user")
public class CaliMeetUserManageController {
    private final AuthService authService;
    private final CaliMeetAppService caliMeetAppService;
    private final CaliMeetResultFileImporter caliMeetResultFileImporter;
    private final CaliMeetAppManage caliMeetAppManage;
    private final CaliMeetFileExporter caliMeetFileExporter;

    private final CaliMeetUserImporter caliMeetUserImporter;

    @SwaggerPageQuery
    @Operation(summary = "人员列表-查询时部门多选")
    @PostMapping(value = "/list/{meetingId}", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(codes = AUTH_CODE_ALL)
    public PagingList<CaliMeetUserVO> list(
            HttpServletRequest request, @PathVariable String meetingId,
            @RequestBody CaliMeetUserQuery search) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return caliMeetAppService.findPage(
                ApiUtil.getPageRequest(request), meetingId, search, userCache);
    }

    @Operation(summary = "添加人员")
    @PostMapping(value = "/batch/add", consumes = Constants.MEDIATYPE, produces = Constants.MEDIATYPE)
    @ResponseStatus(CREATED)
    @Auth(codes = AuthCodes.AUTH_CODE_ALL)
    @Auditing
    //@AuditingPlus(strategyClass = CaliMeetUserAddAuditLogStrategy.class)
    @EasyAuditLog(value = RvAuditLogConstants.CALI_ADD_USER, paramExp = "#meetingUser4BatchAdd")
    public void batchAdd(
            HttpServletRequest request,
            @RequestBody @Valid CaliMeetUserBatchAddCmd meetingUser4BatchAdd) {
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        caliMeetAppService.batchAddUser(
                meetingUser4BatchAdd, operator.getUserId(), operator.getOrgId());
    }

    @Operation(summary = "获取详情")
    @GetMapping(value = "/detail/{id}")
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetUserResultVO getMeetingDetail(
            HttpServletRequest request, @PathVariable String id) {
        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
        return caliMeetAppManage.getMeetingUser4GetById(
                id, currentUser.getOrgId(), currentUser.getLocale());
    }

    @Operation(summary = "校准人员")
    @PutMapping(value = "", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(OK)
    //@EasyAuditLog(value = RvAuditLogConstants.CAlI_USER_RESULT_UPDATE, paramExp = "#caliMeetUserUpdateCmd")
    @Auth(codes = CALI_MEET_USER_SET)
    //@Auditing
    public void update(
            HttpServletRequest request,
            @RequestBody @Valid CaliMeetUserUpdateCmd caliMeetUserUpdateCmd) {
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        caliMeetAppManage.calibration(
                caliMeetUserUpdateCmd, operator.getUserId(), operator.getOrgId());
    }

    @Operation(summary = "删除人员")
    @Parameter(name = "ids", description = "id列表")
    @DeleteMapping(value = "/batch/del/{meetingId}")
    @ResponseStatus(OK)
    @Auth(codes = CALI_MEET_USER_DEL)
    @Auditing
    //@AuditingPlus(strategyClass = CaliMeetUserDelAuditLogStrategy.class)
    @EasyAuditLog(value = RvAuditLogConstants.CALI_DELETE_USER)
    public void batchRemove(
            HttpServletRequest request, @PathVariable String meetingId,
            @RequestBody List<String> ids) {
        CaliMeetUserBatchAddCmd delete = new CaliMeetUserBatchAddCmd();
        delete.setMeetingId(meetingId);
        delete.setUserIdList(ids);
        AuditLogHooker.setLogParam(delete);
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        caliMeetAppService.batchRemove(ids, meetingId, operator.getOrgId());
    }

    @Operation(summary = "导出校准结果")
    @PostMapping(value = "/export/result/{meetingId}", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(codes = CALI_MEET_USER_EXPORT)
    public Map<String, String> exportResult(
            HttpServletRequest request, @PathVariable String meetingId,
            @RequestBody CaliMeetUserQuery search) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return caliMeetFileExporter.exportCaliMeetResult(meetingId, search, userCache);
    }

    @Operation(summary = "导入校准结果-模板导出")
    @Parameter(name = "type", description = "数据类型：1-维度等级，2-维度评分", required = true, in = QUERY)
    @GetMapping(value = "/export/template/{meetingId}", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(codes = CALI_MEET_USER_IMPORT)
    public Map<String, String> exportTemplate(
            HttpServletRequest request, @PathVariable String meetingId, @RequestParam int type) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return caliMeetFileExporter.exportCaliMeetResultTpl(meetingId, type, userCache);
    }

    @Nullable
    @Operation(summary = "导入校准结果-结果导入")
    @Parameters(value = {
            @Parameter(name = "type", description = "数据类型：1-维度等级，2-维度评分", required = true, in = QUERY),
            @Parameter(name = "file", description = "文件流")})
    @PostMapping(value = "/import/result/{meetingId}")
    @ResponseStatus(OK)
    @Auth(codes = CALI_MEET_USER_IMPORT)
    public FileImportResult resultImport(
            HttpServletRequest request, @PathVariable String meetingId, @RequestParam int type,
            FileImportCmd requestBean,
            @RequestParam(value = "file", required = false) MultipartFile file) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return caliMeetResultFileImporter.toImport(meetingId, type, requestBean, file, userCache);
    }

    @Operation(summary = "导入校准人员")
    @Parameters(value = {
            @Parameter(name = "file", description = "文件流")})
    @PostMapping(value = "/import/{meetingId}")
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public FileImportResult meetingUserImport(
            HttpServletRequest request, @PathVariable String meetingId,
            FileImportCmd requestBean,
            @RequestParam(value = "file", required = false) MultipartFile file) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return caliMeetUserImporter.toImport(meetingId, requestBean, file, userCache);
    }
}
