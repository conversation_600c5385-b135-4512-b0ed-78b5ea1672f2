package com.yxt.talent.rv.controller.client.bizmgr.team;

import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.talent.rv.application.xpd.user.XpdUserAppService;
import com.yxt.talent.rv.controller.client.bizmgr.team.query.TeamPrjGridScopeAuthClientQuery;
import com.yxt.talent.rv.controller.client.bizmgr.team.query.TeamPrjResultScopeAuthClientQuery;
import com.yxt.talent.rv.controller.client.bizmgr.team.query.TeamPrjUserResultScopeAuthClientQuery;
import com.yxt.talent.rv.controller.client.bizmgr.team.viewobj.TeamPrjUserResultClientVO;
import com.yxt.talent.rv.controller.client.general.prj.viewobj.PrjClientVO;
import com.yxt.talent.rv.controller.client.general.prj.viewobj.PrjSubUserClientVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdDimCombGridResultVO;
import com.yxt.talent.rv.infrastructure.config.swagger.SwaggerPageQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "")
@Tag(name = "学员端-我的团队-人才盘点-结果查询类接口", description = "学员端-我的团队-人才盘点-结果查询类接口")
public class TeamPrjResultClientController {

    private final AuthService authService;
    private final XpdUserAppService xpdUserAppService;

//    @Deprecated(since = "5.8")
//    @Operation(summary = "个人维度-盘点结果列表-已废弃", hidden = true)
//    @PostMapping(value = "/client/team/prj/result/users/test", consumes = MEDIATYPE, produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(type = {TOKEN})
//    public PagingList<TeamPrjUserResultClientVO> searchProjectResultUserListTest(HttpServletRequest request, @RequestBody TeamPrjUserResultScopeAuthClientQuery search) {
//        UserCacheDetail operator = authService.getUserCacheDetail(request);
//        PageRequest pageRequest = ApiUtil.getPageRequest(request);
//        return prjUserResultAppManage.searchProjectResultUserList(pageRequest, operator, search);
//    }

    @Operation(summary = "个人维度-盘点结果列表[新盘点]")
    @PostMapping(value = "/client/team/prj/result/users", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = {TOKEN})
    public PagingList<TeamPrjUserResultClientVO> searchProjectResultUserList(HttpServletRequest request, @RequestBody TeamPrjUserResultScopeAuthClientQuery search) {
        UserCacheDetail operator = authService.getUserCacheDetail(request);
        PageRequest pageRequest = ApiUtil.getPageRequest(request);
        return xpdUserAppService.searchProjectResultUserList(pageRequest, operator, search);
    }

//    @Deprecated(since = "5.8")
//    @Operation(summary = "项目维度-盘点结果列表-已废弃", hidden = true)
//    @PostMapping(value = "/client/team/prj/search/test", consumes = MEDIATYPE, produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(type = {TOKEN})
//    public PagingList<PrjClientVO> searchProjectClientListTest(HttpServletRequest request, @RequestBody TeamPrjResultScopeAuthClientQuery search) {
//        UserCacheDetail operator = authService.getUserCacheDetail(request);
//        PageRequest pageRequest = ApiUtil.getPageRequest(request);
//        return prjUserResultAppManage.searchProjectClientList(pageRequest, operator, search);
//    }

    @SwaggerPageQuery
    @Operation(summary = "项目维度-盘点结果列表[新盘点]")
    @PostMapping(value = "/client/team/prj/search", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = {TOKEN})
    public PagingList<PrjClientVO> searchProjectClientList(HttpServletRequest request, @RequestBody TeamPrjResultScopeAuthClientQuery search) {
        UserCacheDetail operator = authService.getUserCacheDetail(request);
        PageRequest pageRequest = ApiUtil.getPageRequest(request);
        return xpdUserAppService.searchProjectClientList(pageRequest, operator, search);
    }

    /**
     * @deprecated See searchProjectClientUserList
     */
//    @Deprecated(since = "5.8")
//    @Operation(summary = "项目维度-盘点结果列表-查看-参与人员[已废弃]", hidden = true)
//    @PostMapping(value = "/client/team/prj/{projectId}/users/test")
//    @ResponseStatus(OK)
//    @Auth(type = {TOKEN})
//    public PagingList<PrjSubUserClientVO> searchProjectClientUserListTest(HttpServletRequest request, @PathVariable String projectId,
//                                                                          @RequestBody TeamPrjUserResultScopeAuthClientQuery search) {
//        UserCacheDetail operator = authService.getUserCacheDetail(request);
//        PageRequest pageRequest = ApiUtil.getPageRequest(request);
//        return prjUserResultAppManage.searchProjectClientUserList(pageRequest, operator, projectId, search.getKeyword(), search);
//    }

    @SwaggerPageQuery
    @Operation(summary = "项目维度-盘点结果列表-查看-参与人员[新盘点]")
    @PostMapping(value = "/client/team/prj/{projectId}/users")
    @ResponseStatus(OK)
    @Auth(type = {TOKEN})
    public PagingList<PrjSubUserClientVO> searchProjectClientUserList(HttpServletRequest request,
                                                                      @PathVariable String projectId,
                                                                      @RequestBody TeamPrjUserResultScopeAuthClientQuery search) {
        UserCacheDetail operator = authService.getUserCacheDetail(request);
        PageRequest pageRequest = ApiUtil.getPageRequest(request);
        return xpdUserAppService.searchProjectClientUserList(pageRequest, operator, projectId, search.getKeyword(), search);
    }

//    @Deprecated(since = "5.8")
//    @Operation(summary = "项目维度-盘点结果列表-查看-盘点九宫格[已废弃]", hidden = true)
//    @PostMapping(value = "/client/team/prj/result/palaces/test", produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(type = TOKEN)
//    public List<PrjUserResultVO> listPalacesTest(HttpServletRequest request, @RequestBody TeamPrjGridScopeAuthClientQuery search) {
//        PageRequest pageRequest = ApiUtil.getPageRequest(request);
//        UserCacheDetail operator = authService.getUserCacheDetail(request);
//        String orgId = operator.getOrgId();
//        String userId = operator.getUserId();
//        prjUserResultAppManage.fillScopeAuth(search, orgId, userId);
//        search.setStatus(3); // 排除删除状态
//        return prjUserResultAppService.palaces(orgId, search.toPrjGridScopeAuthQuery(), pageRequest, operator.getLocale());
//    }

    @Operation(summary = "项目维度-盘点结果列表-查看-盘点九宫格[新盘点]")
    @PostMapping(value = "/client/team/prj/result/palaces", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public List<XpdDimCombGridResultVO> listPalaces(HttpServletRequest request, @RequestBody TeamPrjGridScopeAuthClientQuery search) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        String orgId = userCache.getOrgId();
        String userId = userCache.getUserId();
        xpdUserAppService.fillScopeAuth(search, orgId, userId);
        search.setStatus(3); // 排除删除状态
        return xpdUserAppService.gridview(userCache, search.toPrjGridScopeAuthQuery());
    }
}
