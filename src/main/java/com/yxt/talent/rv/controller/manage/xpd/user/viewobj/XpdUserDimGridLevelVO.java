package com.yxt.talent.rv.controller.manage.xpd.user.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 *
 * @date 2024/12/16
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class XpdUserDimGridLevelVO {

    private String id;

    /**
     * 维度类型:0-普通/1-能力/2-技能/3-知识/4-任务/5-绩效
     */
    private Integer dimType;

    @Schema(description = "维度id")
    private String dimId;

    @Schema(description = "维度名称")
    private String dimName;

    @Schema(description = "分层id")
    private String gridLevelId;

    @Schema(description = "维度结果")
    private String gridLevelName;

    @Schema(description = "维度分层排序")
    private Integer orderIndex;

    @Schema(description = "宫格类型,0-四宫格,1-九宫格,2-十六宫格")
    private Integer gridType;

    // 得分
    private BigDecimal scoreValue;

    // 达标率
    private BigDecimal qualifiedPtg;



}
