package com.yxt.talent.rv.controller.manage.xpd.grid.viewobj;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "盘点宫格分层信息")
public class XpdGridLevelVO {

    @Schema(description = "分层id")
    private String gridLevelId;

    @I18nTranslate(codeField = "levelNameI18n")
    @Schema(description = "分层名称")
    private String gridLevelName;

    @Schema(description = "分层名称国际化")
    private String levelNameI18n;

    @Schema(description = "分层序号")
    private Integer orderIndex;

    @Mapper
    public interface Assembler {
        XpdGridLevelVO.Assembler INSTANCE = Mappers.getMapper(XpdGridLevelVO.Assembler.class);

        List<XpdGridLevelVO> toXpdGridLevelVos(List<XpdGridLevelPO> xpdGridLevels);

        @Mapping(target = "gridLevelId", source = "id")
        @Mapping(target = "gridLevelName", source = "levelName")
        XpdGridLevelVO toXpdGridLevelVo(XpdGridLevelPO xpdGridLevel);
    }
}
