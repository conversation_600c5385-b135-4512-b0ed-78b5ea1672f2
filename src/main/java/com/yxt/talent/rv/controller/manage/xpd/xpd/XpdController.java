package com.yxt.talent.rv.controller.manage.xpd.xpd;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import com.yxt.aom.base.bean.arrange.ActivityArrangeItem4ActvMgr;
import com.yxt.aom.base.bean.common.*;
import com.yxt.aom.base.bean.control.ProjectStatisticsResp;
import com.yxt.aom.base.bean.md.AomDrawer4RespDTO;
import com.yxt.aom.base.bean.md.AomUserStatisticsBean;
import com.yxt.aom.base.bean.md.Phone;
import com.yxt.aom.base.bean.part.PartMember4List;
import com.yxt.aom.base.bean.part.PartMemberReq;
import com.yxt.aom.base.controller.common.ActivityController;
import com.yxt.aom.base.controller.part.ActivityPartMemberController;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.aom.base.service.control.ControlRptService;
import com.yxt.aom.base.service.part.impl.ActivityParticipationService;
import com.yxt.auditfacade.bean.AuditTmplInfo4Base;
import com.yxt.auditfacade.feignclient.AuditClient;
import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.GenericCommonData;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4ReqDTO;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import com.yxt.modelhub.api.bean.dto.SearchDTO;
import com.yxt.modelhub.api.utils.QueryUtil;
import com.yxt.spsdfacade.bean.spsd.ModelInfo;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.spsdk.common.utils.YxtBasicUtils;
import com.yxt.talent.rv.application.activity.ActivityCalcComponent;
import com.yxt.talent.rv.application.activity.dto.RvActivity4Get;
import com.yxt.talent.rv.application.calimeet.legacy.CaliMeetAppService;
import com.yxt.talent.rv.application.xpd.aom.XpdAomService;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeEnum;
import com.yxt.talent.rv.application.xpd.result.XpdResultCalcService;
import com.yxt.talent.rv.application.xpd.rule.XpdRuleConfAppService;
import com.yxt.talent.rv.application.xpd.xpd.XpdAppService;
import com.yxt.talent.rv.application.xpd.xpd.XpdSceneService;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.controller.manage.activity.viewobj.ActvCalcStatusVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RvAuditLogConstants;
import com.yxt.talent.rv.infrastructure.common.constant.enums.UTreeEnum;
import com.yxt.talent.rv.infrastructure.persistence.cache.RedisRepo;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdScene;
import com.yxt.ubiz.tree.extension.UTreeComponent;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.common.enums.YesOrNo.*;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static org.springframework.http.HttpStatus.OK;

@Tag(name = "项目接口")
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/mgr/rv_project")
public class XpdController {
    private final ActivityController activityController;
    private final XpdService xpdService;
    private final AuthService authService;
    private final XpdSceneService xpdSceneService;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final ActivityPartMemberController activityPartMemberController;
    private final ActivityParticipationService activityParticipationService;
    private final XpdRuleConfAppService xpdRuleConfAppService;
    private final ActivityService activityService;
    private final XpdResultCalcService xpdResultCalcService;
    private final CaliMeetAppService caliMeetAppService;
    private final XpdAomService xpdAomService;
    private final ControlRptService controlRptService;
    private final XpdDimMapper xpdDimMapper;
    private final AuditClient auditClient;

    private final UTreeComponent uTreeComponent;
    private final static String DEFAULT_ID = "00000000-0000-0000-0000-000000000000";
    private final static String AUDIT_TMP_CODE = "sp_interview";
    private final XpdAppService xpdAppService;
    private final ActivityCalcComponent activityCalcComponent;
    private final RedisRepo redisRepo;

    @Parameters(value = {
        @Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
        @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @Operation(summary = "项目列表")
    @PostMapping(value = "/search", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public PagingList<RvProject4Get> searchPrjs(HttpServletRequest request, @RequestBody SearchDTO bean) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        PagingList<Activity4List> pagingList =
            activityController.searchActivity(request, getReq(userCacheDetail.getOrgId(), bean));

        List<Activity4List> pagingListDatas = pagingList.getDatas();
        List<RvProject4Get> resultList = Lists.newArrayListWithCapacity(CollectionUtils.size(pagingListDatas));

        if (CollectionUtils.isNotEmpty(pagingListDatas)) {
            List<String> aomIds = StreamUtil.mapList(pagingListDatas, Activity4List::getId);
            List<XpdPO> xpdPOS = xpdService.findXpdsByAomIds(userCacheDetail.getOrgId(), aomIds);
            Map<String, XpdPO> xpdMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(xpdPOS)) {
                xpdMap = StreamUtil.list2map(xpdPOS, XpdPO::getAomPrjId);
            }

            List<String> createUserIds = StreamUtil.mapList(pagingListDatas, Activity4List::getCreateUserId);
            Set<String> allUserIds = new HashSet<>(createUserIds);
            pagingListDatas.forEach(
                activity4Get -> {
                    if (CollectionUtils.isNotEmpty(activity4Get.getMgrs())) {
                        allUserIds.addAll(activity4Get.getMgrs().stream()
                            .map(IdName::getId)
                            .collect(Collectors.toList()));
                    }
                });

            Map<String, UdpLiteUserPO> userMap = getUserMap(userCacheDetail, new ArrayList<>(allUserIds));

            Iterator var6 = pagingListDatas.iterator();
            while (var6.hasNext()) {
                Activity4List obj = (Activity4List) var6.next();
                RvProject4Get get = new RvProject4Get();
                get.setId(String.valueOf(obj.getId()));
                get.setOrgId(obj.getOrgId());
                get.setName(obj.getActvName());
                get.setCategoryid(obj.getCategoryId());
                get.setProjstatus(obj.getActvStatus().toString());
                get.setStarttime(obj.getStartTime());
                get.setEndtime(obj.getEndTime());
                AmUser4DTO createUserId = new AmUser4DTO();
                List<AmUser4DTO.UserInfo> createUserList = new ArrayList<>();
                if (StringUtils.isNotBlank(obj.getCreateUserId()) && userMap.containsKey(obj.getCreateUserId())) {
                    UdpLiteUserPO user = userMap.get(obj.getCreateUserId());
                    AmUser4DTO.UserInfo userInfo = new AmUser4DTO.UserInfo();
                    userInfo.setId(user.getId());
                    userInfo.setName(user.getFullname());
                    userInfo.setUserName(user.getUsername());
                    userInfo.setUserNo(user.getUserNo());
                    userInfo.setStatus(String.valueOf(user.getStatus()));
                    createUserList.add(userInfo);
                }
                if (xpdMap.containsKey(obj.getId())) {
                    XpdPO xpdPO = xpdMap.get(obj.getId());
                    get.setXpdId(xpdPO.getId());
                }
                AmSlDrawer4RespDTO amSlDrawer4RespDTO = new AmSlDrawer4RespDTO();
                Map<String, Object> data = new HashMap<>();
                data.put("id", obj.getModelId());
                amSlDrawer4RespDTO.setDatas(Lists.newArrayList(data));
                get.setModelid(amSlDrawer4RespDTO);
                createUserId.setDatas(createUserList);
                get.setCreateUserId(createUserId);
                setProjmanager(get, obj.getMgrs(), userMap);
                get.setCreateTime(obj.getCreateTime());

                if (obj.getStartTime() != null && obj.getEndTime() != null) {
                    get.setStartEndTime(DateUtil.formatDate(obj.getStartTime()) + " - " + DateUtil.formatDate(obj.getEndTime()));
                }
                get.setAuditStatus(String.valueOf(obj.getAuditStatus()));
                get.setAuditenabled(obj.getAuditEnabled());

                setCategoryName(get, obj);
                resultList.add(get);
            }
        }

        PagingList<RvProject4Get> getPagingList = new PagingList<>();
        getPagingList.setPaging(pagingList.getPaging());
        getPagingList.setDatas(resultList);
        return getPagingList;
    }



    @Operation(summary = "项目创建")
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_CREATESINGLE, type = {AuthType.TOKEN})
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.XPD_CREATE, paramExp = "#bean")
    @ResponseBody
    public GenericCommonData<String> add(HttpServletRequest request, @Validated @RequestBody RvProject4Create bean) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        validateDate(bean.getStarttime(), bean.getEndtime());
        Activity4Create activity4Create = new Activity4Create();
        activity4Create.setActvName(bean.getName());
        activity4Create.setCategoryId(bean.getCategoryid());
        if (CollectionUtils.isNotEmpty(bean.getProjmanager())) {
            Set<String> mgrUserIds = new HashSet<>(bean.getProjmanager().stream()
                .map(AmSlDrawer4ReqDTO::getId)
                .collect(Collectors.toList()));
            mgrUserIds.add(userCacheDetail.getUserId());
            activity4Create.setMgrUserIds(mgrUserIds);
        }else {
            activity4Create.setMgrUserIds(Set.of(userCacheDetail.getUserId()));
        }
        activity4Create.setActvType(2);
        activity4Create.setTimeModel(0); // 固定时间
        activity4Create.setStartTime(bean.getStarttime());
        activity4Create.setEndTime(bean.getEndtime());
        activity4Create.setDescription(bean.getProjdescription());
        activity4Create.setActvRegId(UacdTypeEnum.PRJ_XPD.getRegId());
        if (CollectionUtils.isNotEmpty(bean.getModelid())) {
            activity4Create.setModelId(bean.getModelid().get(0).getId());
        }
        if (CollectionUtils.isNotEmpty(bean.getSceneid())) {
            activity4Create.setSceneId(bean.getSceneid().get(0).getId());
        }
        activity4Create.setAutoEnd(bean.getAutoend());
        if (CollectionUtils.isEmpty(activity4Create.getMgrUserIds())) {
            throw new ApiException(ExceptionKeys.XPD_PROJECT_MANAGER_NOT_EMPTY);
        }
        if (activity4Create.getMgrUserIds().size() > 30) {
            throw new ApiException(ExceptionKeys.XPD_PROJECT_MANAGER_MAX);
        }
        activity4Create.setAutoRelease(true);
        activity4Create.setAuditEnabled(bean.getAuditenabled());
        return new GenericCommonData(activityController.create(request, activity4Create));
    }

    @Operation(summary = "项目编辑")
    @Parameter(name = "id", description = "项目id", in = ParameterIn.PATH)
    @PutMapping(value = "/{id}", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.XPD_UPDATE, paramExp = "#id")
    @Auth(action = Constants.LOG_TYPE_UPDATESINGLE, type = {AuthType.TOKEN})
    public void edit(
        HttpServletRequest request, @PathVariable String id, @Validated @RequestBody RvProject4Update bean) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        validateDate(bean.getStarttime(), bean.getEndtime());
        Activity4Update activity4Update = new Activity4Update();
        activity4Update.setActvName(bean.getName());
        activity4Update.setCategoryId(bean.getCategoryid());
        if (CollectionUtils.isNotEmpty(bean.getProjmanager())) {
            activity4Update.setMgrUserIds(new HashSet<>(bean.getProjmanager().stream()
                .map(AmSlDrawer4ReqDTO::getId)
                .collect(Collectors.toList())));
        }
        activity4Update.setActvType(2);
        activity4Update.setTimeModel(0); // 固定时间
        activity4Update.setStartTime(bean.getStarttime());
        activity4Update.setEndTime(bean.getEndtime());
        activity4Update.setDescription(bean.getProjdescription());
        activity4Update.setActvRegId(UacdTypeEnum.PRJ_XPD.getRegId());
        if (CollectionUtils.isNotEmpty(bean.getModelid())) {
            activity4Update.setModelId(bean.getModelid().get(0).getId());
        }
        if (CollectionUtils.isNotEmpty(bean.getSceneid())) {
            activity4Update.setSceneId(bean.getSceneid().get(0).getId());
        } else {
            activity4Update.setSceneId("");
        }
        activity4Update.setAutoEnd(bean.getAutoend());
        if (CollectionUtils.isEmpty(activity4Update.getMgrUserIds())) {
            throw new ApiException(ExceptionKeys.XPD_PROJECT_MANAGER_NOT_EMPTY);
        }
        if (activity4Update.getMgrUserIds().size() > 30) {
            throw new ApiException(ExceptionKeys.XPD_PROJECT_MANAGER_MAX);
        }
        activity4Update.setAuditEnabled(bean.getAuditenabled());

        Activity activity = activityService.findById(userCacheDetail.getOrgId(), id);
        if (activity4Update.getAuditEnabled() != null){
            activity4Update.setAuditStatus(ObjectUtils.defaultIfNull(activity.getAuditStatus(), activity.getAuditEnabled() == 1 ? 0 : 2));
        }else {
            activity4Update.setAuditStatus(ObjectUtils.defaultIfNull(activity.getAuditStatus(), 2));
        }

        if (activity4Update.getAuditEnabled() != null && activity4Update.getAuditEnabled() == 1
            && !checkAuditExist(userCacheDetail.getOrgId(), id)) {
            throw new ApiException(ExceptionKeys.APIS_AUDIT_NOT_EXIST);
        }
        activity4Update.setAutoRelease(true);
        activity4Update.setCopyFields(new String[] {
            "actvName","categoryId","actvType","timeModel","startTime","endTime","description",
            "modelId","sceneId","autoEnd","auditEnabled","auditStatus","autoRelease"
        });
        activityController.update(request, id, activity4Update);
    }

    @Operation(summary = "项目编辑审核, 只在创建的时候用")
    @Parameter(name = "id", description = "项目id", in = ParameterIn.PATH)
    @PutMapping(value = "/editaudit/{id}", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_UPDATESINGLE, type = {AuthType.TOKEN})
    public void editEnableAudit(
        HttpServletRequest request, @PathVariable String id, @Validated @RequestBody RvProjectAudit4Update bean) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        Activity activity = activityService.findById(userCacheDetail.getOrgId(), id);
        if (activity == null) {
            throw new ApiException(ExceptionKeys.XPD_NOT_EXIST);
        }
        activity.setAuditEnabled((Integer) ObjectUtils.defaultIfNull(bean.getAuditenabled(), 0));
        activity.setAuditStatus(activity.getAuditEnabled() == 1 ? 0 : 2);

        if (activity.getAuditEnabled() != null && activity.getAuditEnabled() == 1
            && !checkAuditExist(userCacheDetail.getOrgId(), id)) {
            throw new ApiException(ExceptionKeys.APIS_AUDIT_NOT_EXIST);
        }

        activity.setUpdateUserId(userCacheDetail.getUserId());
        activity.setUpdateTime(DateUtil.currentTime());
        this.activityService.update(userCacheDetail, activity, false, false, null, true);
    }



    @Parameter(name = "id", description = "项目id", in = ParameterIn.PATH)
    @Operation(summary = "项目详情")
    @GetMapping(value = "/{id}")
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = {AuthType.TOKEN})
    public RvProject4Get detail(@PathVariable String id) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        Activity4Get activity4Get = activityController.getActivity4Mgr(id, UacdTypeEnum.PRJ_XPD.getRegId());
        if (activity4Get == null || activity4Get.getId() == null) {
            throw new ApiException(ExceptionKeys.XPD_NOT_EXIST);
        }
        XpdPO xpdPO = xpdService.findXpdByAomId(userCacheDetail.getOrgId(), id);
        if (xpdPO == null) {
            throw new ApiException(ExceptionKeys.XPD_NOT_EXIST);
        }
        RvProject4Get rvProject4Get = new RvProject4Get();
        rvProject4Get.setId(activity4Get.getId());
        rvProject4Get.setXpdId(xpdPO.getId());
        rvProject4Get.setOrgId(activity4Get.getOrgId());
        rvProject4Get.setName(activity4Get.getActvName());
        rvProject4Get.setProjdescription(activity4Get.getDescription());
        rvProject4Get.setStarttime(activity4Get.getStartTime());
        rvProject4Get.setEndtime(activity4Get.getEndTime());
        rvProject4Get.setAutoend(activity4Get.getAutoEnd());
        rvProject4Get.setAuditenabled(activity4Get.getAuditEnabled());
        rvProject4Get.setProjstatus(String.valueOf(activity4Get.getActvStatus()));

        if (StringUtils.isNotBlank(activity4Get.getCategoryId())) {
            rvProject4Get.setCategoryid(activity4Get.getCategoryId());
            Map<String, String> categoryMap =
                uTreeComponent.getNodesName(userCacheDetail.getOrgId(), UTreeEnum.XPD_BASE.getTreeId(),
                    Lists.newArrayList(activity4Get.getCategoryId()));
            AmSlDrawer4RespDTO amDrawer4RespDTO = new AmSlDrawer4RespDTO();
            IdName idName = new IdName();
            if (StringUtils.isNotBlank(activity4Get.getCategoryId())) {
                idName.setId(activity4Get.getCategoryId());
                idName.setName(categoryMap.getOrDefault(activity4Get.getCategoryId(), ""));
                amDrawer4RespDTO.setDatas(Lists.newArrayList(idName));
            }
            rvProject4Get.setCategoryid__Record(amDrawer4RespDTO);
        }


        rvProject4Get.setParticipationId(activity4Get.getParticipationId());

        setModelId(userCacheDetail.getOrgId(), rvProject4Get, activity4Get);
        setSceneId(userCacheDetail.getOrgId(), rvProject4Get, activity4Get);
        if (CollectionUtils.isNotEmpty(activity4Get.getMgrs())) {
            List<String> userIds = activity4Get.getMgrs().stream()
                .map(IdName::getId)
                .collect(Collectors.toList());
            Map<String, UdpLiteUserPO> userMap = getUserMap(userCacheDetail, userIds);
            setProjmanager(rvProject4Get, activity4Get.getMgrs(), userMap);
        }
        if (activity4Get.getStartTime() != null && activity4Get.getEndTime() != null) {
            rvProject4Get.setStartEndTime(DateUtil.formatDate(activity4Get.getStartTime()) + " - " + DateUtil.formatDate(activity4Get.getEndTime()));
        }
        rvProject4Get.setAuditStatus(String.valueOf(activity4Get.getAuditStatus()));

        // 是否计算中
        String lockKey = String.format(RedisKeys.LK_XPD_CALC_EXECUTE, xpdPO.getId());
        rvProject4Get.setCalcStatus(redisRepo.getRedisRepository().hasKey(lockKey) ? YES.getValue() : NO.getValue());

        return rvProject4Get;
    }

    @Parameter(name = "id", description = "项目id", in = ParameterIn.PATH)
    @Operation(summary = "项目详情（过滤字段）")
    @PostMapping(value = "/simple/{id}")
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = {AuthType.TOKEN})
    public RvProject4Get infoDetail(@PathVariable String id, @RequestBody SearchDTO bean) {
        return this.detail(id);
    }


    @Parameters(value = {
        @Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
        @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @Operation(summary = "盘点活动列表")
    @PostMapping(value = "/actv/search", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public PagingList<RvActivity4Get> searchActivitys(HttpServletRequest request, @RequestBody SearchDTO bean) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        QueryUtil.Search search = QueryUtil.parse(bean);
        String aomPrjId = search.getFilterEq().get("prj_id");

        // 组件
        int useForComponent = 0;
        if (StringUtils.isNotBlank(search.getFilterEq().get("userforcomponent"))) {
            useForComponent = Integer.parseInt(search.getFilterEq().get("userforcomponent"));
        }

        if (useForComponent == 1) {
            return getActivityListByComponent(bean, userCacheDetail.getOrgId());
        }

        List<ActvCalcStatusVO> actvCalcStatus =
            activityCalcComponent.getActvCalcStatus(userCacheDetail.getOrgId(), aomPrjId);

        List<ActivityArrangeItem4ActvMgr> list = xpdService.getActivityList(aomPrjId, UacdTypeEnum.PRJ_XPD.getRegId());
        log.info("活动列表，原始数据: {}" , BeanHelper.bean2Json(list, JsonInclude.Include.NON_NULL));
        List<RvActivity4Get> resultList = Lists.newArrayListWithCapacity(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            Iterator var6 = list.iterator();
            while (var6.hasNext()) {
                ActivityArrangeItem4ActvMgr obj = (ActivityArrangeItem4ActvMgr) var6.next();
                RvActivity4Get get = new RvActivity4Get();
                get.setId(String.valueOf(obj.getId()));
                get.setOrgId(obj.getOrgId());
                // 有自定义名称，优先显示自定义名称
                get.setName(StringUtils.isNotBlank(obj.getItemName())?obj.getItemName():obj.getRefName());
                get.setCreateTime(obj.getCreateTime());
                get.setUpdateTime(obj.getUpdateTime());
                // 时间处理
                get.setStarttime(obj.getStartTime());
                get.setEndtime(obj.getEndTime());
                get.setActvschedule(Optional.of(obj.getPassRate())
                    .orElse(BigDecimal.ZERO)
                    .setScale(0, BigDecimal.ROUND_HALF_UP)
                    .intValue());
                setActvType(get, obj);
                AmUser4DTO createUserId = new AmUser4DTO();
                AmUser4DTO.UserInfo userInfo = new AmUser4DTO.UserInfo();
                createUserId.setDatas(Lists.newArrayList(userInfo));
                get.setCreateUserId(createUserId);
                get.setRefId(obj.getRefId());

                // 计算状态
                ActvCalcStatusVO actvCalcStatusVO = actvCalcStatus.stream()
                    .filter(actv -> Objects.equals(actv.getActvId(), obj.getRefId()))
                    .findFirst()
                    .orElse(new ActvCalcStatusVO(aomPrjId, NO.getValue()));
                get.setCalcStatus(actvCalcStatusVO.getCalcStatus());
                resultList.add(get);
            }
        }

        PagingList<RvActivity4Get> getPagingList = new PagingList();
        getPagingList.setDatas(resultList);
        Paging pag = new Paging();
        pag.setCount(resultList.size());
        getPagingList.setPaging(pag);
        return getPagingList;
    }

    @Operation(summary = "盘点活动列表(非APAAS接口)，仅供维度规则选数据来源使用，其他情况禁用")
    @PostMapping(value = "/actv/searchsimple/{xpdId}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public List<ActivityInfoDTO> searchSimpleActivitys(@PathVariable String xpdId, @RequestBody ActvReqBean bean) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        return xpdAomService.listActivityByReq(userCacheDetail.getOrgId(), xpdId, bean);
    }

    /**
     * 组件用的活动列表
     *
     * @param bean
     * @return
     */

    private PagingList<RvActivity4Get> getActivityListByComponent(SearchDTO bean, String orgId) {
        QueryUtil.Search search = QueryUtil.parse(bean);
        String xpdId = (String) search.getFilterEq().get("xpd_id");
        //        search.getFilterEq().get("userforcomponent");

        ActvReqBean actvReqBean = new ActvReqBean();
        if (StringUtils.isNotBlank(search.getFilterEq().get("indicatorId"))) {
            actvReqBean.setIndicatorId(search.getFilterEq().get("indicatorId"));
        }

        if (StringUtils.isNotBlank(search.getFilterEq().get("searchPerfActv"))) {
            int searchPerfActv = Integer.parseInt(search.getFilterEq().get("searchPerfActv"));
            actvReqBean.setSearchPerfActv(searchPerfActv == 1);
        }

        if (StringUtils.isNotBlank(search.getFilterEq().get("needImport"))) {
            int needImport = Integer.parseInt(search.getFilterEq().get("needImport"));
            actvReqBean.setSearchPerfActv(needImport == 1);
        }

        if (StringUtils.isNotBlank(search.getFilterEq().get("needArchive"))) {
            int needArchive = Integer.parseInt(search.getFilterEq().get("needArchive"));
            actvReqBean.setSearchPerfActv(needArchive == 1);
        }

        List<ActivityInfoDTO> list = xpdAomService.listActivityByReq(orgId, xpdId, actvReqBean);
        List<RvActivity4Get> resultList = Lists.newArrayListWithCapacity(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            Iterator var6 = list.iterator();
            while (var6.hasNext()) {
                ActivityInfoDTO obj = (ActivityInfoDTO) var6.next();
                RvActivity4Get get = new RvActivity4Get();
                get.setId(String.valueOf(obj.getActvId()));
                get.setName(obj.getActvName());
                get.setRefId(obj.getActvId());
                get.setEvalType(obj.getEvalType());
                get.setCreateTime(obj.getCreateTime());
//                AmSlDrawer4RespDTO amSlDrawer4RespDTO = new AmSlDrawer4RespDTO();
//                Map<String, Object> data = new HashMap<>();
//                data.put("name", UacdTypeEnum.getNameByRegId(obj.getActvId()));
////                data.put("id", obj.getRefRegId());
//                amSlDrawer4RespDTO.setDatas(Lists.newArrayList(data));
//                get.setActivitytype(amSlDrawer4RespDTO);

                resultList.add(get);
            }
        }

        PagingList<RvActivity4Get> getPagingList = new PagingList<>();
        getPagingList.setDatas(resultList);
        Paging pag = new Paging();
        pag.setCount(resultList.size());
        getPagingList.setPaging(pag);
        return getPagingList;
    }

    private void setActvType(RvActivity4Get get, ActivityArrangeItem4ActvMgr obj) {
//        AmSlDrawer4RespDTO amSlDrawer4RespDTO = new AmSlDrawer4RespDTO();
//        Map<String, Object> data = new HashMap<>();
//        data.put("name", StringUtils.isNotBlank(obj.getActvAlias())?obj.getActvAlias():UacdTypeEnum.getNameByRegId(obj.getRefRegId()));
//        data.put("id", obj.getRefRegId());
//        amSlDrawer4RespDTO.setDatas(Lists.newArrayList(data));
        get.setActivitytype(obj.getRefRegId());
    }

    @Parameters(value = {
        @Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
        @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @Operation(summary = "盘点场景列表")
    @PostMapping(value = "/scenelist", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public PagingList<RvScene4Get> search(HttpServletRequest request, @RequestBody SearchDTO bean) {
        PageRequest pageRequest = ApiUtil.getPageRequest(request);
        PagingList<XpdScene> pagingList = xpdSceneService.listByOrg(DEFAULT_ID, pageRequest);

        List<RvScene4Get> resultList = Lists.newArrayListWithCapacity(CollectionUtils.size(pagingList.getDatas()));
        if (pagingList != null && CollectionUtils.isNotEmpty(pagingList.getDatas())) {
            Iterator var6 = pagingList.getDatas().iterator();
            while (var6.hasNext()) {
                XpdScene obj = (XpdScene) var6.next();
                RvScene4Get get = new RvScene4Get();
                get.setId(obj.getId());
                get.setOrgId(obj.getOrgId());
                get.setName(obj.getSceneName());
                resultList.add(get);
            }
        }

        PagingList<RvScene4Get> getPagingList = new PagingList();
        getPagingList.setDatas(resultList);
        getPagingList.setPaging(pagingList.getPaging());
        return getPagingList;
    }

    @Parameters(value = {
        @Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
        @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @Operation(summary = "盘点人员列表")
    @PostMapping(value = "/user/search", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public PagingList<RvUsers4Get> userList(HttpServletRequest request, @RequestBody SearchDTO bean) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        PagingList<PartMember4List> pagingList =
            this.activityPartMemberController.groupMembers(request, getUserReq(userCacheDetail.getOrgId(), bean));
        List<PartMember4List> pagingListDatas = pagingList.getDatas();
        List<RvUsers4Get> resultList = Lists.newArrayListWithCapacity(CollectionUtils.size(pagingListDatas));
        if (CollectionUtils.isNotEmpty(pagingListDatas)) {
            Iterator var6 = pagingListDatas.iterator();

            while (var6.hasNext()) {
                PartMember4List obj = (PartMember4List) var6.next();
                RvUsers4Get get = new RvUsers4Get();
                get.setId(String.valueOf(obj.getId()));
                get.setOrgId(obj.getOrgId());
                get.setUserids(this.generateUserId(obj));
                //                get.setMemberStatistics(this.generateMemberStatistics(obj));
                resultList.add(get);
            }
        }

        PagingList<RvUsers4Get> getPagingList = new PagingList();
        getPagingList.setPaging(pagingList.getPaging());
        getPagingList.setDatas(resultList);
        return getPagingList;
    }


    @Operation(summary = "盘点发布check")
    @GetMapping(value = "/xpd/publishcheck/{id}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public void checkPub(@PathVariable String id) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        XpdPO xpdPO = xpdService.findXpdByAomId(userCacheDetail.getOrgId(), id);
        if (xpdPO == null) {
            throw new ApiException(ExceptionKeys.XPD_NOT_EXIST);
        }
        ErrorInfo errorInfo =
            xpdRuleConfAppService.checkXpdRuleConf4Common(userCacheDetail.getOrgId(), xpdPO.getId(), false);
        if (errorInfo != null && errorInfo.getCode() != 0) {
            log.info("盘点规则校验失败,{}", BeanHelper.bean2Json(errorInfo, JsonInclude.Include.NON_NULL));
            throw new ApiException(ExceptionKeys.XPD_RULE_CONF_CHECK_FAIL);
        }
    }

    @Operation(summary = "盘点结果计算")
    @GetMapping(value = "/xpd/calcresult/{id}")
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.XPD_CALC, paramExp = "#id")
    @Auth(action = Constants.LOG_TYPE_UPDATEMULTIPLE, type = {AuthType.TOKEN})
    public void calcResult(@PathVariable String id) {
        String orgId = YxtBasicUtils.userInfo().getOrgId();
        xpdResultCalcService.calcResult(orgId, id);
    }

    @Operation(summary = "导出项目报告日志生成接口")
    @GetMapping(value = "/xpd/exportlog/{id}")
    @EasyAuditLog(value = RvAuditLogConstants.XPD_EXPORT, paramExp = "#id")
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = {AuthType.TOKEN})
    public void exportlog(@PathVariable String id) {
        // DONOTHING
    }

    @Operation(summary = "盘点项目删除前校验是否存在校准后")
    @GetMapping(value = "/xpd/checkmeet/{id}")
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = {AuthType.TOKEN})
    public boolean exiteMeet(@PathVariable String id) {
        String orgId = YxtBasicUtils.userInfo().getOrgId();
        return caliMeetAppService.getMeetingCount(orgId, id) > 0;
    }


    private PartMemberReq getUserReq(String orgId, SearchDTO bean) {
        PartMemberReq req = new PartMemberReq();
        QueryUtil.Search search = QueryUtil.parse(bean);
        req.setKeyword(search.getSearch().getValue());
        req.setRegId(UacdTypeEnum.PRJ_XPD.getRegId());

        String actvId = (String) search.getFilterEq().get("actvId");
        req.setActvId(actvId);

        Long participationId = this.activityParticipationService.getParticipationId(orgId, actvId);
        req.setParticipationId(participationId);

        req.setDeptIds((List) search.getFilterIn().get("userId.deptId"));
        req.setPositionIds((List) search.getFilterIn().get("userId.positionId"));

        return req;
    }

    private AmUser4DTO generateUserId(PartMember4List obj) {
        AmUser4DTO userId = new AmUser4DTO();
        List<AmUser4DTO.UserInfo> datas = Lists.newArrayList();
        userId.setDatas(datas);
        AmUser4DTO.UserInfo userInfo = new AmUser4DTO.UserInfo();
        userInfo.setId(obj.getUserId());
        userInfo.setUserName(obj.getUsername());
        userInfo.setName(obj.getFullname());
        userInfo.setImgUrl(obj.getAvatarUrl());
        userInfo.setMobile(JSON.toJSONString(Phone.builder().phone(obj.getMobile()).build()));
        userInfo.setUserNo(obj.getUserNo());
        userInfo.setHireDate(obj.getHireDate());
//        userInfo.setStatus(String.valueOf(obj.getUserStatus()));
        String status = obj.getDeleted() == 1 ? "2" : String.valueOf(obj.getUserStatus());
        // 设置用户信息状态
        userInfo.setStatus(status);
        AmUser4DTO.AmDept deptList = new AmUser4DTO.AmDept();
        List<AmUser4DTO.DeptInfo> deptInfoDatas = Lists.newArrayList();
        deptList.setDatas(deptInfoDatas);
        AmUser4DTO.DeptInfo deptInfo = new AmUser4DTO.DeptInfo();
        deptInfo.setId(obj.getDeptId());
        deptInfo.setName(obj.getDeptName());
        deptInfoDatas.add(deptInfo);
        userInfo.setDeptList(deptList);
        AmUser4DTO.AmPosition positionList = new AmUser4DTO.AmPosition();
        List<AmUser4DTO.PositionInfo> positionInfoDatas = Lists.newArrayList();
        positionList.setDatas(positionInfoDatas);
        AmUser4DTO.PositionInfo positionInfo = new AmUser4DTO.PositionInfo();
        positionInfo.setId(obj.getPositionId());
        positionInfo.setName(obj.getPositionList());
        positionInfoDatas.add(positionInfo);
        userInfo.setPositionList(positionList);
        datas.add(userInfo);
        return userId;
    }

    private AomDrawer4RespDTO generateMemberStatistics(PartMember4List obj) {
        AomDrawer4RespDTO memberStatistics = new AomDrawer4RespDTO();
        List<Object> datas = Lists.newArrayList();
        memberStatistics.setDatas(datas);
        AomUserStatisticsBean bean = new AomUserStatisticsBean();
        datas.add(bean);
        bean.setActvCompletedStatus(String.valueOf(obj.getCompleteStatus()));
        bean.setActCompletedRate(obj.getActCompletedRate());
        bean.setRequiredTaskCompletedRate(obj.getRequiredTaskCompletedRate());
        bean.setElectiveTaskCompletedRate(obj.getElectiveTaskCompletedRate());
        bean.setFirstStudyTime(obj.getStartTime());
        bean.setLastStudyTime(obj.getLastStudyTime());
        bean.setEndStudyTime(obj.getEndTime());
        return memberStatistics;
    }

    @Parameter(name = "id", description = "项目id", in = ParameterIn.PATH)
    @Operation(summary = "项目概览")
    @PostMapping(value = "/statistic/{id}")
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = {AuthType.TOKEN})
    public RvProjectStatistic4Get getStatistics(@PathVariable String id) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        String orgId = userCacheDetail.getOrgId();
        XpdPO xpdPO = xpdService.findXpdByAomId(orgId, id);
        if (xpdPO == null) {
            throw new ApiException(ExceptionKeys.XPD_NOT_EXIST);
        }
        ProjectStatisticsResp
            projectStatisticsResp = controlRptService.getProjectStatistics(userCacheDetail.getOrgId(), id);

        RvProjectStatistic4Get rvProjectStatistic4Get = new RvProjectStatistic4Get();
        if (projectStatisticsResp != null) {
            if (projectStatisticsResp.getProjectCompleteRate() != null) {
                rvProjectStatistic4Get.setProgress(projectStatisticsResp.getProjectCompleteRate().multiply(BigDecimal.valueOf(100)));
            }
            rvProjectStatistic4Get.setTaskNum(projectStatisticsResp.getTaskNum());
            rvProjectStatistic4Get.setUserNum(projectStatisticsResp.getMemberNum());
        }
        rvProjectStatistic4Get.setDmpNum(xpdDimMapper.countByOrgIdAndXpdId(userCacheDetail.getOrgId(), xpdPO.getId()));
        return rvProjectStatistic4Get;
    }


    public void validateDate(Date start, Date end){
        if (end.compareTo(start) <= 0) {
            throw new ApiException(ExceptionKeys.XPD_DATE_RANGE_ERROR);
        }
    }

    private Map<String, UdpLiteUserPO> getUserMap(
        UserCacheDetail userCacheDetail, List<String> createUserIds) {
        List<UdpLiteUserPO> udpLiteUserPOS = udpLiteUserMapper.selectByUserIds(
            userCacheDetail.getOrgId(), createUserIds);
        Map<String, UdpLiteUserPO> userMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(udpLiteUserPOS)) {
            userMap = StreamUtil.list2map(udpLiteUserPOS, UdpLiteUserPO::getId);
        }
        return userMap;
    }

    private ActivitySearchReq getReq(String orgId, SearchDTO bean) {
        ActivitySearchReq activitySearchReq = new ActivitySearchReq();
        activitySearchReq.setActvRegId(UacdTypeEnum.PRJ_XPD.getRegId());
        activitySearchReq.setActvType(2);
        QueryUtil.Search search = QueryUtil.parse(bean);
        if (StringUtils.isNotBlank(search.getFilterEq().get("projstatus"))) {
            Integer status = Integer.parseInt(search.getFilterEq().get("projstatus"));
            activitySearchReq.setActvStatusSet(Set.of(status));
        }
        String searchKey = ApiUtil.getFiltedLikeString(search.getSearch().getValue());
        activitySearchReq.setActvName(searchKey);

        if (StringUtils.isNotBlank(search.getFilterEq().get("_dataScope"))) {
            int range = Integer.parseInt(search.getFilterEq().get("_dataScope"));
            range = convertRange(range);
            activitySearchReq.setRange(range);
        }

        if (StringUtils.isNotBlank(search.getFilterEq().get("auditStatus"))) {
            Integer auditStatus = Integer.parseInt(search.getFilterEq().get("auditStatus"));
            activitySearchReq.setAuditStatus(auditStatus);
        }

        if (StringUtils.isNotBlank(search.getFilterEq().get("categoryId"))) {
            String categoryId = search.getFilterEq().get("categoryId");
            List<String> categoryIds =
                uTreeComponent.getChildrenNodeIds(orgId, UTreeEnum.XPD_BASE.getTreeId(), categoryId);
            activitySearchReq.setCategoryIds(new HashSet<>(categoryIds));
        }

        return activitySearchReq;
    }

    private int convertRange(int range) {
        //项目/活动查看范围(1-我负责的[项目负责人有我], 2-我创建的[项目创建人是我], 3-我管辖的[所辖员工负责的])
        if (range == 1) {
            return 2;
        }
        if (range == 2) {
            return 3;
        }
        if (range == 3) {
            return 1;
        }
        return 1;
    }


    private void setSceneId(String orgId, RvProject4Get rvProject4Get, Activity4Get activity4Get) {
        if (StringUtils.isNotBlank(activity4Get.getSceneId())) {
            XpdScene xpdScene = xpdSceneService.findById(DEFAULT_ID, activity4Get.getSceneId());
            if (xpdScene != null) {
                AmSlDrawer4RespDTO amSlDrawer4RespDTO = new AmSlDrawer4RespDTO();
                Map<String, Object> data = new HashMap<>();
                data.put("name", xpdScene.getSceneName());
                data.put("id", xpdScene.getId());
                amSlDrawer4RespDTO.setDatas(Lists.newArrayList(data));
                rvProject4Get.setSceneid(amSlDrawer4RespDTO);
            }
        }
    }

    private void setModelId(String orgId, RvProject4Get rvProject4Get, Activity4Get activity4Get) {
        if (StringUtils.isNotBlank(activity4Get.getModelId())) {
            ModelInfo modelInfo = xpdService.getModel(orgId, activity4Get.getModelId());
            AmSlDrawer4RespDTO amSlDrawer4RespDTO = new AmSlDrawer4RespDTO();
            Map<String, Object> data = new HashMap<>();
            // 名称拼接，名称加版本号
            String name = modelInfo.getTitle() + "(" + modelInfo.getVersionNumber() + ")";
            data.put("name", name);
            data.put("id", modelInfo.getId());
            amSlDrawer4RespDTO.setDatas(Lists.newArrayList(data));
            rvProject4Get.setModelid(amSlDrawer4RespDTO);
        }
    }

    private void setProjmanager(RvProject4Get rvProject4Get, List<IdName> mgrs, Map<String, UdpLiteUserPO> userPOMap) {
        if (CollectionUtils.isNotEmpty(mgrs)) {
            AmSlDrawer4RespDTO amDrawer4RespDTO = new AmSlDrawer4RespDTO();
            List<Object> list = new ArrayList<>(mgrs.size());
            for (IdName mgr : mgrs) {
                if (userPOMap.containsKey(mgr.getId())) {
                    UdpLiteUserPO userPO = userPOMap.get(mgr.getId());
                    RvUserInfo rvUserInfo = new RvUserInfo();
                    rvUserInfo.setName(userPO.getFullname());
                    rvUserInfo.setUserName(userPO.getUsername());
                    rvUserInfo.setUserNo(userPO.getUserNo());
                    rvUserInfo.setId(mgr.getId());
                    rvUserInfo.setValue(userPO.getId());
                    rvUserInfo.setLable(userPO.getFullname());
                    rvUserInfo.setUserNo(userPO.getUserNo());
                    rvUserInfo.setStatus(userPO.getStatus());
                    list.add(rvUserInfo);
                }
            }
            amDrawer4RespDTO.setDatas(list);
            rvProject4Get.setProjmanager(amDrawer4RespDTO);
        }

    }

    private void setCategoryName(RvProject4Get rvProject4Get, Activity4List obj) {
        AmSlDrawer4RespDTO amDrawer4RespDTO = new AmSlDrawer4RespDTO();
        IdName idName = new IdName();
        if (StringUtils.isNotBlank(obj.getCategoryId())) {
            idName.setId(obj.getCategoryId());
            idName.setName(obj.getCategoryName());
            amDrawer4RespDTO.setDatas(Lists.newArrayList(idName));
            rvProject4Get.setCategoryid(obj.getCategoryId());
        }
        rvProject4Get.setCategoryid__Record(amDrawer4RespDTO);
    }

    /**
     * 判断某个配置标识对应的审核流程是否存在
     *
     * @param orgId      机构ID
     * @param auditCfgId 配置标识 如项目ID、表单ID等等
     */
    public boolean checkAuditExist(String orgId, String auditCfgId) {
        AuditTmplInfo4Base info4Base = auditClient.getAuditProjTmplBaseInfo(AUDIT_TMP_CODE, orgId, auditCfgId);
        return info4Base != null;
    }

    @Operation(summary = "检查项目中是否添加盘点活动")
    @GetMapping(value = "/{xpdId}/check/act", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public Boolean isActvExists(HttpServletRequest request, @PathVariable String xpdId) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return xpdAppService.isActvExists(userCache.getOrgId(), xpdId);
    }

}
