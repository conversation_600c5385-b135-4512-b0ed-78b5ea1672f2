package com.yxt.talent.rv.controller.manage.calimeet.query;

import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 查询盘点人员入参
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class CaliMeetUserQuery {

    @Schema(description = "部门id")
    private List<String> deptIds;

    @Schema(description = "姓名/账号")
    private String keyword;

    private List<String> authUserIds;

    public String getEscapedKeyword() {
        return SqlUtil.escapeSql(keyword);
    }
}
