package com.yxt.talent.rv.controller.openapi;

import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.Validate;
import com.yxt.talent.rv.application.perf.PerfCmdAppService;
import com.yxt.talent.rv.application.perf.PerfQryAppService;
import com.yxt.talent.rv.application.perf.lagecy.PerfAppManage;
import com.yxt.talent.rv.application.perf.lagecy.PerfPeriodAppService;
import com.yxt.talent.rv.controller.manage.perf.command.PerfPeriodCmd;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfImportResultVO;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfPeriodIdNameVO;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfPeriodVO;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfSettingVO;
import com.yxt.talent.rv.controller.openapi.command.PerfSyncOpenCmd;
import com.yxt.talent.rv.controller.openapi.command.PerfSyncV2OpenCmdList;
import com.yxt.talent.rv.controller.openapi.viewobj.PerfGradeVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.CUSTOM;
import static com.yxt.common.enums.AuthType.OAUTH;
import static com.yxt.common.enums.AuthType.TOKEN;
import static org.springframework.http.HttpStatus.CREATED;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "开放平台-绩效管理", description = "开放平台-绩效管理")
@RequestMapping(value = "/open/perf")
public class PerfOpenController {
    private final AuthService authService;
    private final PerfPeriodAppService perfPeriodAppService;
    private final PerfAppManage perfAppManage;
    private final PerfQryAppService perfQryAppService;
    private final PerfCmdAppService perfCmdAppService;

    /**
     * 绩效数据导入(新版本,供奇点同步绩效数据)
     *
     * @param commands
     */
    @Operation(summary = "同步员工绩效数据")
    @PostMapping(value = "/sync", consumes = Constants.MEDIATYPE, produces = Constants.MEDIATYPE)
    @ResponseStatus(CREATED)
    @Auth(type = {OAUTH, CUSTOM})
    public void syncUserPerf(@Validated @RequestBody PerfSyncV2OpenCmdList commands) {
        String orgId = authService.getOrgIdByOauthToken(ApiUtil.getRequestByContext());
        log.info("LOG62040:第三方请求，导入绩效数据，请求来源机构id={}", orgId);
        perfCmdAppService.syncUserPerf(orgId, commands.getDatas());
    }

    /**
     * @param request
     * @return
     * @deprecated 老人发绩效开放接口，不适用于奇点
     */
    @Deprecated(since = "5.4")
    @Operation(summary = "绩效周期列表")
    @GetMapping(value = "/getPeriods")
    @ResponseStatus(OK)
    @Auth(type = {OAUTH, CUSTOM})
    public List<PerfPeriodVO> list(HttpServletRequest request) {
        String orgId = authService.getOrgIdByOauthToken(request);
        log.info("LOG62010:第三方请求，查询绩效周期列表，请求来源机构id={} ", orgId);
        return perfPeriodAppService.findList(orgId);
    }

    /**
     * @param request
     * @return
     * @deprecated 老人发绩效开放接口，不适用于奇点
     */
    @Deprecated(since = "5.4")
    @Operation(summary = "绩效周期新增")
    @Parameters({@Parameter(name = "performancePeriod4Create", description = "绩效周期传值对象")})
    @PostMapping(value = "/createPeriod", consumes = Constants.MEDIATYPE, produces = Constants.MEDIATYPE)
    @ResponseStatus(CREATED)
    @Auth(type = AuthType.OAUTH)
    public PerfPeriodIdNameVO add(
            HttpServletRequest request, @Valid @RequestBody PerfPeriodCmd perfPeriodCmd) {
        String orgId = authService.getOrgIdByOauthToken(request);
        log.info("LOG62020:第三方请求，新增绩效周期列表，请求来源机构id={} ，请求参数={}", orgId,
                BeanHelper.bean2Json(perfPeriodCmd, ALWAYS));
        return perfPeriodAppService.addPerfPeriod(
                orgId, perfPeriodCmd.getThirdUserId(), perfPeriodCmd);
    }

    /**
     * @param request
     * @return
     * @deprecated 老人发绩效开放接口，不适用于奇点
     */
    @Deprecated(since = "5.4")
    @Operation(summary = "绩效周期编辑")
    @Parameters({@Parameter(name = "performancePeriod4Create", description = "绩效周期传值对象")})
    @PostMapping(value = "/editPeriod", consumes = MEDIATYPE, produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = {OAUTH, CUSTOM})
    public void edit(
            HttpServletRequest request, @Valid @RequestBody PerfPeriodCmd perfPeriodCmd) {
        String orgId = authService.getOrgIdByOauthToken(request);
        log.info("LOG62030:第三方请求，编辑绩效周期列表，请求来源机构id={}，请求参数={}", orgId,
                BeanHelper.bean2Json(perfPeriodCmd, ALWAYS));
        perfPeriodAppService.editPerfPeriod(orgId, perfPeriodCmd.getThirdUserId(), perfPeriodCmd);
    }

    /**
     * @param request
     * @return
     * @deprecated 老人发绩效开放接口，不适用于奇点
     */
    @Deprecated(since = "5.4")
    @Operation(summary = "绩效数据导入")
    @Parameters({@Parameter(name = "perfImportBean", description = "绩效数据导入传值对象")})
    @PostMapping(value = "/importPerf", consumes = Constants.MEDIATYPE, produces = Constants.MEDIATYPE)
    @ResponseStatus(CREATED)
    @Auth(type = AuthType.OAUTH)
    public PerfImportResultVO syncUserPerf(
            HttpServletRequest request, @Valid @RequestBody PerfSyncOpenCmd perfSyncOpenCmd) {
        String orgId = authService.getOrgIdByOauthToken(request);
        log.info("LOG14675:第三方请求，导入绩效数据，请求来源机构id={}，请求参数={}", orgId,
                BeanHelper.bean2Json(perfSyncOpenCmd, ALWAYS));
        Validate.isTrue(
                CollectionUtils.isNotEmpty(perfSyncOpenCmd.getPerfs()) &&
                perfSyncOpenCmd.getPerfs().size() <= 1000,
                "apis.sptalentrv.transmit.import.data.out.limit");
        return perfAppManage.processPerfImport(
                perfSyncOpenCmd.getPerfs(), orgId, perfSyncOpenCmd.getThirdUserId());
    }

    /**
     * @param request
     * @return
     * @deprecated 老人发绩效开放接口，不适用于奇点
     */
    @Deprecated(since = "5.4")
    @Operation(summary = "获取机构的绩效等级信息")
    @GetMapping(value = "/perf/setting", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = {TOKEN})
    public PerfSettingVO getOrgPerfInfo(HttpServletRequest request) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return perfAppManage.getOrgPerfInfo(userCache.getOrgId(), userCache.getUserId(), null);
    }

    /**
     * @param request
     * @return
     * @deprecated 老人发绩效开放接口，不适用于奇点
     */
    @Deprecated(since = "5.4")
    @ResponseStatus(OK)
    @Operation(summary = "获取绩效等级列表")
    @GetMapping(value = "/grades")
    @Auth(type = {OAUTH, CUSTOM})
    public Collection<PerfGradeVO> findPerfGrades(HttpServletRequest request) {
        String orgId = authService.getOrgIdByOauthToken(request);
        if (StringUtils.isBlank(orgId)) {
            orgId = request.getParameter("orgId");
        }
        Validate.isNotBlank(orgId, ExceptionKeys.ORG_ID_BLANK);
        log.info("LOG13475:开放平台-绩效相关接口,orgId={} ", orgId);
        return perfQryAppService.findPerfGrades(orgId);
    }

}
