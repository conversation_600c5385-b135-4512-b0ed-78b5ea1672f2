package com.yxt.talent.rv.controller.manage.xpd.rule.viewobj;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(description = "xpd项目绑定的维度列表")
public class XpdDimVo {

    @Schema(description = "维度类型:0-普通/1-能力/2-技能/3-知识/4-任务/5-绩效")
    private Integer dimType;

    @Schema(description = "冗余人才标准的维度id")
    private String sdDimId;

    @Schema(description = "维度名称")
    private String sdDimName;

    @Schema(description = "维度名称国际化code", hidden = true)
    private String sdDimNameI18n;

    @Schema(description = "维度规则描述")
    private String sdDimRuleDesc;
}
