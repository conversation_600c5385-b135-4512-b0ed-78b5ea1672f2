package com.yxt.talent.rv.controller.manage.xpd.result.viewobj;

import com.yxt.talent.rv.application.common.dto.UserBaseInfoDTO;
import com.yxt.talent.rv.controller.manage.xpd.grid.viewobj.XpdGridLevelVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(description = "人员各维度所属等级详情")
public class XpdResultUserDimDetailVO extends UserBaseInfoDTO {

    @Schema(description = "人员各维度所属等级详情")
    private List<XpdResultUserDimLevelVO> userDimLevels;

    @Schema(description = "宫格层级信息")
    private List<XpdGridLevelVO> gridLevels;

    public XpdResultUserDimDetailVO(UdpLiteUserPO user) {
        super(user);
    }

}
