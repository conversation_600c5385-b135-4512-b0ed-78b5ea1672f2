package com.yxt.talent.rv.controller.manage.xpd.result.viewobj;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.talent.rv.application.common.dto.UserBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(description = "宫格视图(落位结果)")
public class XpdDimCombGridResultVO {

    @Schema(description = "格子id")
    private String cellId;

    @Schema(description = "格子编号")
    private Integer cellIndex;

    @JsonProperty("xIndex")
    @Schema(description = "格子横坐标")
    private Integer xIndex;

    @JsonProperty("yIndex")
    @Schema(description = "格子纵坐标")
    private Integer yIndex;

    @Schema(description = "格子人数")
    private Integer userCount;

    @Schema(description = "格子人数占比")
    private BigDecimal userRatio;

    @Schema(description = "每个格子中的人员信息")
    private List<UserBaseInfoDTO> userList;

    public long getUserCount() {
        if (userCount != null) {
            return userCount;
        }
        if (userList != null) {
            return userList.size();
        }
        return 0;
    }
}
