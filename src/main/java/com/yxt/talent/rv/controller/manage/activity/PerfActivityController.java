package com.yxt.talent.rv.controller.manage.activity;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.aom.base.util.AomNumberUtils;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import com.yxt.modelhub.api.bean.dto.SearchDTO;
import com.yxt.modelhub.api.utils.QueryUtil;
import com.yxt.talent.rv.application.activity.PerfActivityService;
import com.yxt.talent.rv.application.activity.component.PerfActivityComponent;
import com.yxt.talent.rv.application.activity.dto.*;
import com.yxt.talent.rv.application.activity.expt.PerfActivityExportService;
import com.yxt.talent.rv.application.activity.impt.PerfActivityImporter;
import com.yxt.talent.rv.application.xpd.common.dto.AomActvExtDto;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfPeriodVO;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.service.file.command.FileImportCmd;
import com.yxt.talent.rv.infrastructure.service.file.dto.FileImportResult;
import com.yxt.talent.rv.infrastructure.service.file.viewobj.GenericApaasFileExportVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequestMapping("/mgr/perfactivity")
@RequiredArgsConstructor
@Tag(name = "绩效活动", description = "绩效活动")
public class PerfActivityController {

    private final PerfActivityService perfActivityService;
    private final AuthService authService;
    private final PerfActivityExportService perfActivityImportService;
    private final PerfActivityImporter perfActivityImporter;
    private final PerfActivityComponent perfActivityComponent;

    @Operation(summary = "绩效周期列表")
    @GetMapping(value = "")
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public List<PerfPeriodVO> list(HttpServletRequest request) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return perfActivityService.queryPeriods(userCache.getOrgId());
    }

    @Operation(summary = "创建绩效活动接口")
    @PostMapping(value = "/")
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public void create(HttpServletRequest request, @Validated @RequestBody PerfActivityDTO perfActivityDTO) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        perfActivityService.createPerfActivity(userCache.getOrgId(), userCache.getUserId(),
            perfActivityDTO);
    }

    @Operation(summary = "获取绩效活动的ext字段")
    @PostMapping(value = "/ext")
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public AomActvExtDto getPerfActivityExt(
        HttpServletRequest request, @Validated @RequestBody PerfActivityExtDTO perfActivityDTO) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return perfActivityService.getPerfActivityExtInfo(
            userCache.getOrgId(),
            perfActivityDTO);
    }


    @Operation(summary = "更新绩效活动接口")
    @PutMapping(value = "/")
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public void update(HttpServletRequest request, @RequestBody PerfActivityDTO perfActivityDTO) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        perfActivityService.updatePerfActivity(
            userCache.getOrgId(), userCache.getUserId(), perfActivityDTO);
    }

    @Operation(summary = "删除绩效活动接口")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public void update(HttpServletRequest request, @PathVariable String id) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        perfActivityService.deletePerfActivity(
            userCache.getOrgId(), userCache.getUserId(), id);
    }

    @Parameter(name = "id", description = "绩效评估活动id", in = ParameterIn.PATH)
    @Operation(summary = "绩效评估活动详情（过滤字段）")
    @PostMapping(value = "/{id}/detail")
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = {AuthType.TOKEN})
    public RvPerfAssessment4Get infoDetail(@PathVariable String id, @RequestBody SearchDTO bean) {
        UserCacheBasic userCacheBasic = authService.getUserCacheBasic();
        return perfActivityService.getPerfActivitySimpleDetail(userCacheBasic.getOrgId(), id);
    }

    @Operation(summary = "获取绩效指标")
    @GetMapping(value = "/inds/{modelId}")
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public List<ActIndicator> getModelInds(HttpServletRequest request, @PathVariable String modelId) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        return perfActivityService.getPerfIndicators(userCache.getOrgId(), modelId);
    }

    @Operation(summary = "导入绩效数据模板下载")
    @PostMapping(value = "/import/template/{id}", produces = MEDIATYPE)
    @ResponseBody
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public GenericApaasFileExportVO getImportTemplate(
        HttpServletRequest request, @PathVariable String id) {
        UserCacheDetail operator = authService.getUserCacheDetail(request);
        return perfActivityImportService.getTemplate(id, operator);
    }

    @Nullable
    @Operation(summary = "导入绩效数据")
    @PostMapping(value = "/import")
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    @ResponseStatus(OK)
    public FileImportResult importPerf(
        HttpServletRequest request, @RequestBody FileImportCmd bean,
        @RequestParam(value = "file", required = false) MultipartFile file) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return perfActivityImporter.toImport(bean, file, userCache);
    }


    @ApiOperation("绩效活动人员列表")
    @PostMapping(value = "/users")
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public PagingList<PerfUserPageUserVO> getUserList(
        HttpServletRequest request, @RequestBody ActMemberUserCriteria searchParam) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        PageRequest pageRequest = ApiUtil.getPageRequest(request);
        searchParam.setOrgId(userCache.getOrgId());
        searchParam.setUserId(userCache.getUserId());
        return perfActivityService.userList(pageRequest, searchParam);
    }

    @ApiOperation("计算绩效结果")
    @GetMapping(value = "/cal/{id}")
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public void calResult(HttpServletRequest request, @PathVariable String id) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        perfActivityService.sendCalcMq(userCache.getOrgId(), id, userCache.getUserId());
    }

    @Parameters(value = {
        @Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
        @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @Operation(summary = "绩效评估人员列表")
    @PostMapping(value = "/search", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public PagingList<PerfUsers4Get> pageList(HttpServletRequest request, @RequestBody SearchDTO bean) {
        UserCacheBasic userCacheBasic = authService.getUserCacheBasic();
        PageRequest pageRequest = ApiUtil.getPageRequest(request);
        ActMemberUserCriteria searchParam = getSeachParam(bean, userCacheBasic);
        PagingList<PerfUserPageUserVO> pagingList = perfActivityService.userList(pageRequest, searchParam);

        List<PerfUserPageUserVO> pagingListDatas = pagingList.getDatas();
        List<PerfUsers4Get> resultList = Lists.newArrayListWithCapacity(CollectionUtils.size(pagingListDatas));
        if (CollectionUtils.isNotEmpty(pagingListDatas)) {
            Iterator var6 = pagingListDatas.iterator();
            while (var6.hasNext()) {
                PerfUserPageUserVO obj = (PerfUserPageUserVO) var6.next();
                PerfUsers4Get get = new PerfUsers4Get();
                get.setUserid(generateUserId(obj));
                get.setStatus(String.valueOf(obj.getCompleteStatus()));
                get.setQualified(String.valueOf(obj.getQuilified()));
                AmSlDrawer4RespDTO amSlDrawer4RespDTO = new AmSlDrawer4RespDTO();
                List<Object> resultMap = Lists.newArrayList();
                Map<String, Object> resultMapData = Maps.newHashMap();
                resultMapData.put("id", obj.getResultConfId());
                resultMapData.put("name", obj.getEvalResult());
                resultMap.add(resultMapData);
                amSlDrawer4RespDTO.setDatas(resultMap);
                get.setResultconfid(amSlDrawer4RespDTO);
                get.setNumberstate(String.valueOf(obj.getUserStatus()));
                resultList.add(get);
            }
        }

        PagingList<PerfUsers4Get> getPagingList = new PagingList();
        getPagingList.setPaging(pagingList.getPaging());
        getPagingList.setDatas(resultList);
        return getPagingList;
    }

    private AmUser4DTO generateUserId(PerfUserPageUserVO obj) {
        AmUser4DTO userId = new AmUser4DTO();
        List<AmUser4DTO.UserInfo> datas = Lists.newArrayList();
        userId.setDatas(datas);
        AmUser4DTO.UserInfo userInfo = new AmUser4DTO.UserInfo();
        userInfo.setId(obj.getUserId());
        userInfo.setUserName(obj.getUsername());
        userInfo.setName(obj.getFullname());
        userInfo.setStatus(String.valueOf(obj.getUserStatus()));
        AmUser4DTO.AmDept deptList = new AmUser4DTO.AmDept();
        List<AmUser4DTO.DeptInfo> deptInfoDatas = Lists.newArrayList();
        deptList.setDatas(deptInfoDatas);
        AmUser4DTO.DeptInfo deptInfo = new AmUser4DTO.DeptInfo();
        deptInfo.setId(obj.getDeptId());
        deptInfo.setName(obj.getDeptName());
        deptInfoDatas.add(deptInfo);
        userInfo.setDeptList(deptList);
        AmUser4DTO.AmPosition positionList = new AmUser4DTO.AmPosition();
        List<AmUser4DTO.PositionInfo> positionInfoDatas = Lists.newArrayList();
        positionList.setDatas(positionInfoDatas);
        AmUser4DTO.PositionInfo positionInfo = new AmUser4DTO.PositionInfo();
        positionInfo.setId(obj.getPositionId());
        positionInfo.setName(obj.getPositionName());
        positionInfoDatas.add(positionInfo);
        userInfo.setPositionList(positionList);
        //        AmUser4DTO.AmGrade gradeList = new AmUser4DTO.AmGrade();
        //        List<AmUser4DTO.GradeInfo> gradeInfoDatas = Lists.newArrayList();
        //        gradeList.setDatas(gradeInfoDatas);
        //        AmUser4DTO.GradeInfo gradeInfo = new AmUser4DTO.GradeInfo();
        //        gradeInfo.setName(obj.getGradeName());
        //        gradeInfoDatas.add(gradeInfo);
        //        userInfo.setGradeList(gradeList);
        datas.add(userInfo);
        return userId;
    }

    @Parameters(value = {
        @Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
        @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @Operation(summary = "绩效评估人员导出")
    @PostMapping(value = "/{actvId}/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public void export(HttpServletRequest request, @PathVariable String actvId) {
        UserCacheBasic userCacheBasic = authService.getUserCacheBasic();
        ActMemberUserCriteria bean = new ActMemberUserCriteria();
        bean.setActvId(actvId);
        bean.setOrgId(userCacheBasic.getOrgId());
        bean.setUserId(userCacheBasic.getUserId());
        perfActivityComponent.exportPerfUserInfo(userCacheBasic.getOrgId(), userCacheBasic.getUserId(), bean);
    }

    private ActMemberUserCriteria getSeachParam(SearchDTO bean, UserCacheBasic userCacheBasic) {
        ActMemberUserCriteria searchParam = new ActMemberUserCriteria();
        searchParam.setOrgId(userCacheBasic.getOrgId());
        searchParam.setUserId(userCacheBasic.getUserId());
        QueryUtil.Search search = QueryUtil.parse(bean);
        if (StringUtils.isNotBlank(search.getFilterEq().get("actvId"))) {
            searchParam.setActvId(search.getFilterEq().get("actvId"));
        }

        if (StringUtils.isNotBlank(search.getSearch().getValue())) {
            String searchKey = ApiUtil.getFiltedLikeString(search.getSearch().getValue());
            searchParam.setKeyword(searchKey);
            searchParam.setKwType(CommonUtil.getKeywordType(search));
        }
        String statusStr = (String) search.getFilterEq().get("numberstate");
        if (StringUtils.isNotBlank(statusStr) && AomNumberUtils.isNumber(statusStr)) {
            searchParam.setStatus(Integer.valueOf(statusStr));
        }

        String positionId = (String) search.getFilterEq().get("userid.positionId");
        if (StringUtils.isNotBlank(positionId)) {
            searchParam.setPositionIds(Lists.newArrayList(positionId));
        }

        String deptId = (String) search.getFilterEq().get("userid.deptId");
        if (StringUtils.isNotBlank(deptId)) {
            searchParam.setDeptIds(Lists.newArrayList(deptId));
        }

        return searchParam;
    }

}
