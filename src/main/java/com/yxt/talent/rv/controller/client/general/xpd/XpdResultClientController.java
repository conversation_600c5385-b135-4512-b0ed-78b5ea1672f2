package com.yxt.talent.rv.controller.client.general.xpd;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.annotation.Auth;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.talent.rv.application.prj.user.legacy.PrjUserResultAppManage;
import com.yxt.talent.rv.application.prj.user.legacy.PrjUserResultAppService;
import com.yxt.talent.rv.application.xpd.user.XpdUserAppService;
import com.yxt.talent.rv.controller.client.bizmgr.team.query.TeamPrjGridScopeAuthClientQuery;
import com.yxt.talent.rv.controller.manage.prj.user.command.PrjUserResultSuggestionAddCmd;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjResultSubUserVO;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjUserResultVO;
import com.yxt.talent.rv.controller.manage.xpd.user.command.XpdUserLatestResultCmd;
import com.yxt.talent.rv.controller.manage.xpd.user.command.XpdUserResultSubCmd;
import com.yxt.talent.rv.infrastructure.config.swagger.SwaggerPageQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "")
@Tag(name = "学员端-盘点项目-盘点结果", description = "学员端-盘点项目-盘点结果")
public class XpdResultClientController {
    private final AuthService authService;
    private final PrjUserResultAppService prjUserResultAppService;
    private final PrjUserResultAppManage prjUserResultAppManage;
    private final XpdUserAppService xpdUserAppService;

//    @Deprecated(since = "5.8")
//    @Operation(summary = "[5.8已废弃]查询项目下人员的在各个维度下盘点等级结果")
//    @GetMapping(value = "/client/prjresult/{projectId}/{userId}/result", produces = MEDIATYPE)
//    @ResponseStatus(OK)
//    @Auth(type = TOKEN)
//    public PrjUserDimResultVO getUserResult(
//            HttpServletRequest request,
//            @PathVariable String projectId,
//            @PathVariable String userId) {
//        // 当前用户信息
//        UserCacheDetail operator = authService.getUserCacheDetail(request);
//        return prjUserResultAppService.getUserResult(operator.getOrgId(), projectId, userId, operator.getLocale());
//    }

    @Operation(summary = "[新盘点]查询项目下人员的在各个维度下盘点等级结果")
    @GetMapping(value = "/client/xpd/{actvId}/{userId}/result", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public XpdUserResultSubCmd getXpdUserResult(HttpServletRequest request,
                                                @PathVariable String actvId,
                                                @PathVariable String userId) {
        // 当前用户信息
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return xpdUserAppService.getXpdUserResult(userCache, actvId, userId);
    }

    @SwaggerPageQuery
    @Operation(summary = "盘点结果查询（九宫格坐标）")
    @PostMapping(value = "/client/prjresult/page", produces = MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public PagingList<PrjUserResultVO> findPage(
            HttpServletRequest request,
            @RequestBody TeamPrjGridScopeAuthClientQuery search) {
        PageRequest pageRequest = ApiUtil.getPageRequest(request);
        UserCacheDetail operator = authService.getUserCacheDetail(request);
        Page<PrjUserResultVO> requestPage =
                new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        prjUserResultAppManage.fillDeptAuthForPalaces(operator, search);
        return prjUserResultAppService.findByXyAxis(
                operator.getOrgId(), search.toPrjGridScopeAuthQuery(), requestPage, operator.getLocale());
    }

//    @Deprecated(since = "5.8")
//    @Operation(summary = "[5.8已废弃]人员维度-人员查看界面列表")
//    @GetMapping(value = "/client/project/result/user/sub/{userId}/list/test")
//    @ResponseStatus(OK)
//    @Auth(type = {TOKEN})
//    public PagingList<PrjResultSubUserVO> searchProjectResultSubUserListTest(
//            HttpServletRequest request, @PathVariable String userId) {
//        UserCacheDetail operator = authService.getUserCacheDetail(request);
//        PageRequest pageRequest = ApiUtil.getPageRequest(request);
//        return prjUserResultAppManage.searchProjectResultSubUserList(
//                pageRequest, operator.getOrgId(), userId, operator.getLocale());
//    }

    @SwaggerPageQuery
    @Operation(summary = "[新盘点]人员维度-人员查看界面列表")
    @GetMapping(value = "/client/project/result/user/sub/{userId}/list")
    @ResponseStatus(OK)
    @Auth(type = {TOKEN})
    public PagingList<PrjResultSubUserVO> searchProjectResultSubUserList(
            HttpServletRequest request, @PathVariable String userId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        PageRequest pageRequest = ApiUtil.getPageRequest(request);
        return xpdUserAppService.searchProjectResultSubUserList(pageRequest, userCache, userId);
    }

//    @Deprecated(since = "5.8")
//    @Operation(summary = "[5.8已废弃]人员维度-保存发展建议")
//    @PutMapping(value = "/client/project/result/user/suggestion")
//    @ResponseStatus(OK)
//    @Auth(type = TOKEN)
//    public void saveSuggestionTest(
//            HttpServletRequest request,
//            @RequestBody @Valid PrjUserResultSuggestionAddCmd bean) {
//        UserCacheBasic operator = authService.getUserCacheBasic(request);
//        prjUserResultAppManage.saveSuggestion(
//                operator.getOrgId(), operator.getUserId(), bean);
//    }

    @Operation(summary = "[新盘点]人员维度-保存发展建议")
    @PutMapping(value = "/client/xpd/result/user/suggestion")
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public void saveSuggestion(HttpServletRequest request, @RequestBody @Valid PrjUserResultSuggestionAddCmd bean) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        xpdUserAppService.saveSuggestion(userCache, bean);
    }

//    @Operation(summary = "[5.8已废弃]人员维度-获取人员基本信息")
//    @GetMapping(value = "/client/project/result/user/{userId}")
//    @ResponseStatus(OK)
//    @Auth(type = TOKEN)
//    public PrjUserLatestRvVO getUserInfo(HttpServletRequest request, @PathVariable String userId) {
//        UserCacheDetail operator = authService.getUserCacheDetail(request);
//        return prjUserResultAppManage.getUserLatestRvInfo(operator.getOrgId(), userId, operator.getLocale());
//    }

    @Operation(summary = "[新盘点]人员维度-获取最新盘点结果")
    @GetMapping(value = "/client/xpd/result/user/{userId}")
    @ResponseStatus(OK)
    @Auth(type = TOKEN)
    public XpdUserLatestResultCmd getLatestResult(HttpServletRequest request, @PathVariable String userId) {
        UserCacheDetail userCache = authService.getUserCacheDetail(request);
        return xpdUserAppService.getUserLatestXpdResult(userCache, userId);
    }
}
