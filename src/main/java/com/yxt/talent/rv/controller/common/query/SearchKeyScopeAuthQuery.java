package com.yxt.talent.rv.controller.common.query;

import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.distinct;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.filterNullAndBlank;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class SearchKeyScopeAuthQuery extends ScopeAuthQuery {

    @Schema(description = "搜索关键字")
    private String searchKey;

    @Schema(description = "关键字类型 1-姓名，2-账号，-1-账号或姓名")
    private Integer kwType;

    @Schema(description = "通过用户搜索关键字换取的用户userIds", hidden = true)
    private List<String> searchUserIds = new ArrayList<>();

    public String getEscapedSearchKey() {
        return SqlUtil.escapeSql(searchKey);
    }

    public List<String> getSearchUserIds() {
        return new ArrayList<>(distinct(filterNullAndBlank(searchUserIds)));
    }
}
