package com.yxt.talent.rv.controller.manage.xpd.user.viewobj;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.talent.rv.application.common.dto.UserBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 *
 * @date 2025/1/3
 */
@Data
public class XpdUserCellVO {

    @Schema(description = "宫格列表")
    private List<XpdGridCellInfoVO> gridCellInfoList;

    @Schema(description = "编号")
    private Integer cellIndex;

    @JsonProperty("xIndex")
    @Schema(description = "格子横坐标")
    private Integer xIndex;

    @JsonProperty("yIndex")
    @Schema(description = "格子纵坐标")
    private Integer yIndex;

    @Schema(description = "每个格子中的人员信息")
    private List<UserBaseInfoDTO> userList;

}
