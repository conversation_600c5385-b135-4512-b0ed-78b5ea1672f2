
package com.yxt.talent.rv.controller.manage.prj.user.query;

import com.yxt.talent.rv.controller.common.query.ScopeAuthQuery;
import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
@EqualsAndHashCode(callSuper = true)
@Schema(name = "管理端-盘点项目-盘点九宫格")
public class PrjGridScopeAuthQuery extends ScopeAuthQuery {
    @Schema(description = "盘点项目id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String projectId;

    @Schema(description = "x轴维度id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String axisX;

    @Schema(description = "y轴维度id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String axisY;

    @Schema(description = "部门ids")
    private List<String> deptIds;

    @Schema(description = "岗位ids")
    private List<String> positionIds;

    @Schema(description = "职级ids")
    private List<String> gradeIds;

    @Schema(description = "员工状态 0：禁用 1：启用，2:删除，3:排除删除状态, -1：查询所有（默认）")
    private int status = -1;

    @Schema(description = "x轴维度值（1-3对应低至高）")
    private Integer valueX;

    @Schema(description = "y轴维度值（1-3对应低至高）")
    private Integer valueY;

    @Schema(description = "维度组合ID since 5.8")
    private String dimCombId;

    public String getEscapedSearchKey() {
        return SqlUtil.escapeSql(searchKey);
    }

    @Schema(description = "查询参数")
    private String searchKey;

    private List<String> userIds;

}
