package com.yxt.talent.rv.application.xpd.common.enums;

import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.spsdfacade.bean.spsd.DimensionList4Get;
import com.yxt.spsdk.common.bean.*;
import com.yxt.spsdk.common.enums.*;
import com.yxt.talent.rv.application.activity.PerfActivityService;
import com.yxt.talent.rv.application.perf.lagecy.PerfPeriodAppService;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.rule.RvRuleComponent;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfPeriodVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

public enum RvRuleTypeEnum implements RuleColTypeBase {
    //1:等级维度数量 2:具体维度
    /**
     * mainData.bizId 为xpd项目id
     */
    XPD_DIM_COUNT(1, SpConditionTypeEnum.INT, SpRuleValueTypeEnum.INPUT_AND_OPTION.getCode(), SpRuleInputValueTypeEnum.INT.getCode(),
        queryDto -> {
            List<XpdGridLevelOptionDto> options = RvRuleComponent.self()
                .gridLevelOptions(queryDto.getMainData().getOrgId(), queryDto.getMainData().getBizId());
            String leveCountLabel = CommonUtil.getI18nComponent().getI18nValue(ExceptionKeys.GRID_RULE_LEVEL_COUNT,
                queryDto.getMainData().getLocale());
            return BeanCopierUtil.convertList(options, gridLevel -> {
                RuleOptionBean option = new RuleOptionBean();
                option.setId(gridLevel.getId());
                option.setName(gridLevel.getLevelName() + leveCountLabel);
                return option;
            });
        },
        new SpCompareOperator[]{SpCompareOperator.EQ,
                                SpCompareOperator.NE,
                                SpCompareOperator.GT,
                                SpCompareOperator.LT,
                                SpCompareOperator.GTE,
                                SpCompareOperator.LTE},
        queryDto -> {
            List<XpdUserLevelCountDto> countList = RvRuleComponent.self().userLevelCount(
                queryDto.getMainData().getOrgId(),
                queryDto.getMainData().getBizId(),
                queryDto.getColumnIds(), queryDto.getObjectIds());
            return BeanCopierUtil.convertList(countList, levelCount -> {
                RuleColumnValueBean valueBean = new RuleColumnValueBean();
                valueBean.setColumnId(levelCount.getGridLevelId());
                valueBean.setObjectId(levelCount.getUserId());
                valueBean.setValue(levelCount.getUserLevelCount());
                return valueBean;
            });
        },0,
        null),
    /**
     * mainData.bizId 为xpd项目id
     */
    XPD_DIM_LEVEL(2, SpConditionTypeEnum.INT, SpRuleValueTypeEnum.ENUM.getCode(), SpRuleInputValueTypeEnum.INT.getCode(),
        queryDto -> {
            return BeanCopierUtil.convertList(RvRuleComponent.self().xpdDimList(queryDto.getMainData().getOrgId(), queryDto.getMainData().getBizId()),
                dim -> {
                    RuleOptionBean option = new RuleOptionBean();
                    option.setId(dim.getDmId());
                    option.setName(dim.getDmName());
                    return option;
                });
        },
        new SpCompareOperator[]{SpCompareOperator.EQ,
                                SpCompareOperator.NE,
                                SpCompareOperator.GT,
                                SpCompareOperator.LT,
                                SpCompareOperator.GTE,
                                SpCompareOperator.LTE},
        queryDto -> {
            List<XpdUserDimLevelDto> levelList = RvRuleComponent.self().userDimLevel(
                queryDto.getMainData().getOrgId(),
                queryDto.getMainData().getBizId(),
                queryDto.getColumnIds(), queryDto.getObjectIds());
            return BeanCopierUtil.convertList(levelList, dimLevel -> {
                RuleColumnValueBean valueBean = new RuleColumnValueBean();
                valueBean.setColumnId(dimLevel.getSdDimId());
                valueBean.setObjectId(dimLevel.getUserId());
                valueBean.setEnumId(dimLevel.getGridLevelId());
                return valueBean;
            });
        },null,
        mainData -> {
            List<XpdGridLevelOptionDto> options = RvRuleComponent.self()
                .gridLevelOptions(mainData.getOrgId(), mainData.getBizId());
            return BeanCopierUtil.convertList(options, gridLevel -> {
                RuleEnumValueBean option = new RuleEnumValueBean();
                option.setId(gridLevel.getId());
                option.setName(gridLevel.getLevelName());
                //等级从高到低的排序号是从高到低
                option.setValue(gridLevel.getOrderIndex());
                return option;
            });
        }),
    /**
     * bizId是rv_activity_perf.id
     */
    PERF(3, SpConditionTypeEnum.INT, SpRuleValueTypeEnum.ENUM.getCode(), SpRuleInputValueTypeEnum.STRING.getCode(),
        queryDto -> {
            // 查询所有绩效周期
            List<PerfPeriodPO> allPeriodList = PerfActivityService.self()
                .getPeriodList(queryDto.getMainData().getOrgId(), queryDto.getMainData().getBizId());
            List<RuleOptionBean> options = BeanCopierUtil.convertList(allPeriodList, periodPO -> {
                RuleOptionBean option = new RuleOptionBean();
                option.setId(periodPO.getId());
                option.setName(periodPO.getPeriodName());
                return option;
            });
            RuleOptionBean allPeriod = new RuleOptionBean();
            allPeriod.setId(SpLogicColumnIdEnum.ALL_COLUMN_ENUM.getLogicId());
            //全部绩效周期
            allPeriod.setName(CommonUtil.getI18nComponent().getI18nValue(ExceptionKeys.PERF_RULE_ALL_PERIOD,
                queryDto.getMainData().getLocale()));
            RuleOptionBean onePeriod = new RuleOptionBean();
            onePeriod.setId(SpLogicColumnIdEnum.ONE_COLUMN_ENUM.getLogicId());
            //任意绩效周期
            onePeriod.setName(CommonUtil.getI18nComponent().getI18nValue(ExceptionKeys.PERF_RULE_ANY_PERIOD,
                queryDto.getMainData().getLocale()));
            RuleOptionBean onePeriodPerfQty = new RuleOptionBean();
            onePeriodPerfQty.setId(SpLogicColumnIdEnum.COLUMN_ENUM_QTY.getLogicId());
            //任意x个绩效周期
            onePeriodPerfQty.setName(CommonUtil.getI18nComponent().getI18nValue(ExceptionKeys.PERF_RULE_PERIOD_QTY,
                queryDto.getMainData().getLocale()));
            for (RuleOptionBean optionBean : new RuleOptionBean[]{allPeriod, onePeriod, onePeriodPerfQty}) {
                optionBean.setLogicFlag(true);
            }
            options.add(allPeriod);
            options.add(onePeriod);
            options.add(onePeriodPerfQty);
            return options;
        },
        new SpCompareOperator[]{SpCompareOperator.EQ,
                                SpCompareOperator.NE,
                                SpCompareOperator.GT,
                                SpCompareOperator.LT,
                                SpCompareOperator.GTE,
                                SpCompareOperator.LTE,
                                SpCompareOperator.IN_LIST,
                                SpCompareOperator.NOT_IN_LIST},
        queryDto -> {
            // 查询所有学员绩效结果
            List<PerfPO> userPeriods = PerfActivityService.self().getUserPeriod(
                queryDto.getMainData().getOrgId(),
                queryDto.getColumnIds(),queryDto.getObjectIds());
            return BeanCopierUtil.convertList(userPeriods, dimLevel -> {
                RuleColumnValueBean valueBean = new RuleColumnValueBean();
                valueBean.setColumnId(dimLevel.getPeriodId());
                valueBean.setObjectId(dimLevel.getUserId());
                valueBean.setEnumId(String.valueOf(dimLevel.getPeriodLevel()));
                return valueBean;
            });
        },null,
        mainData -> {
            // 查询机构绩效等级
            List<PerfGradePO> options = PerfActivityService.self()
                .getPerfGrades(mainData.getOrgId());
            return BeanCopierUtil.convertList(options, gridLevel -> {
                RuleEnumValueBean option = new RuleEnumValueBean();
                option.setId(String.valueOf(gridLevel.getGradeValue()));
                option.setName(gridLevel.getGradeName());
                option.setDisabled(1 - gridLevel.getState());
                //等级从高到低的排序号是从低到高
                option.setValue(-gridLevel.getOrderIndex());
                return option;
            });
        }),
    /**
     * bizId是gridId
     */
    XPD_TEMP_DIM_COUNT(4, SpConditionTypeEnum.INT, SpRuleValueTypeEnum.INPUT_AND_OPTION.getCode(), SpRuleInputValueTypeEnum.INT.getCode(),
        queryDto -> {
            List<XpdGridLevelOptionDto> options = RvRuleComponent.self()
                .gridLevelOptionsByGridId(queryDto.getMainData().getOrgId(), queryDto.getMainData().getBizId());
            String leveCountLabel = CommonUtil.getI18nComponent().getI18nValue(ExceptionKeys.GRID_RULE_LEVEL_COUNT,
                queryDto.getMainData().getLocale());
            return BeanCopierUtil.convertList(options, gridLevel -> {
                RuleOptionBean option = new RuleOptionBean();
                option.setId(gridLevel.getId());
                option.setName(gridLevel.getLevelName() + leveCountLabel);
                return option;
            });
        },
        new SpCompareOperator[]{SpCompareOperator.EQ,
                                SpCompareOperator.NE,
                                SpCompareOperator.GT,
                                SpCompareOperator.LT,
                                SpCompareOperator.GTE,
                                SpCompareOperator.LTE},
        queryDto -> null,0,
        null),
    XPD_TEMP_DIM_LEVEL(5, SpConditionTypeEnum.INT, SpRuleValueTypeEnum.ENUM.getCode(), SpRuleInputValueTypeEnum.INT.getCode(),
        queryDto -> {
            List<DimensionList4Get> options = SpringContextHolder.getBean(SpsdAclService.class)
                .allEnabledBaseDims(queryDto.getMainData().getOrgId());
            return BeanCopierUtil.convertList(options, sdDim -> {
                RuleOptionBean option = new RuleOptionBean();
                option.setId(sdDim.getId());
                option.setName(sdDim.getDmName());
                return option;
            });
        },
        new SpCompareOperator[]{SpCompareOperator.EQ,
                                SpCompareOperator.NE,
                                SpCompareOperator.GT,
                                SpCompareOperator.LT,
                                SpCompareOperator.GTE,
                                SpCompareOperator.LTE},
        queryDto -> null,null,
        mainData -> {
            List<XpdGridLevelOptionDto> options = RvRuleComponent.self()
                .gridLevelOptionsByGridId(mainData.getOrgId(), mainData.getBizId());
            return BeanCopierUtil.convertList(options, gridLevel -> {
                RuleEnumValueBean option = new RuleEnumValueBean();
                option.setId(gridLevel.getId());
                option.setName(gridLevel.getLevelName());
                //等级从高到低的排序号是从高到低
                option.setValue(gridLevel.getOrderIndex());
                return option;
            });
        }),

    /**
     * 证书校验绩效规则
     */
    PERF_FACADE(6, SpConditionTypeEnum.INT, SpRuleValueTypeEnum.ENUM.getCode(), SpRuleInputValueTypeEnum.STRING.getCode(),
        queryDto -> {
            String orgId = queryDto.getMainData().getOrgId();
            Object bizData = queryDto.getMainData().getBizData();
            List<RuleOptionBean> options = new ArrayList<>();

            // 指定绩效周期
            if (bizData != null) {
                //noinspection unchecked
                List<String> periodIds = (List<String>) bizData;
                List<PerfPeriodPO> periodList = PerfPeriodAppService.self().listByIds(orgId, periodIds);
                options.addAll(BeanCopierUtil.convertList(periodList, periodPO -> {
                    RuleOptionBean option = new RuleOptionBean();
                    option.setId(periodPO.getId());
                    option.setName(periodPO.getPeriodName());
                    return option;
                }));
            }

            //全部绩效周期
            RuleOptionBean allPeriod = new RuleOptionBean();
            allPeriod.setId(SpLogicColumnIdEnum.ALL_COLUMN_ENUM.getLogicId());
            allPeriod.setName(CommonUtil.getI18nComponent().getI18nValue(ExceptionKeys.PERF_RULE_ALL_PERIOD, queryDto.getMainData().getLocale()));

            //任意绩效周期
            RuleOptionBean onePeriod = new RuleOptionBean();
            onePeriod.setId(SpLogicColumnIdEnum.ONE_COLUMN_ENUM.getLogicId());
            onePeriod.setName(CommonUtil.getI18nComponent().getI18nValue(ExceptionKeys.PERF_RULE_ANY_PERIOD, queryDto.getMainData().getLocale()));

            //任意x个绩效周期
            RuleOptionBean onePeriodPerfQty = new RuleOptionBean();
            onePeriodPerfQty.setId(SpLogicColumnIdEnum.COLUMN_ENUM_QTY.getLogicId());
            onePeriodPerfQty.setName(CommonUtil.getI18nComponent().getI18nValue(ExceptionKeys.PERF_RULE_PERIOD_QTY, queryDto.getMainData().getLocale()));

            for (RuleOptionBean optionBean : new RuleOptionBean[]{allPeriod, onePeriod, onePeriodPerfQty}) {
                optionBean.setLogicFlag(true);
            }
            options.add(allPeriod);
            options.add(onePeriod);
            options.add(onePeriodPerfQty);
            return options;
        },

        new SpCompareOperator[]{SpCompareOperator.EQ,
                                SpCompareOperator.NE,
                                SpCompareOperator.GT,
                                SpCompareOperator.LT,
                                SpCompareOperator.GTE,
                                SpCompareOperator.LTE,
                                SpCompareOperator.IN_LIST,
                                SpCompareOperator.NOT_IN_LIST},

        queryDto -> {
            // 查询所有学员绩效结果
            List<PerfPO> userPeriods = PerfActivityService.self().getUserPeriod(
                queryDto.getMainData().getOrgId(),
                queryDto.getColumnIds(),queryDto.getObjectIds());
            return BeanCopierUtil.convertList(userPeriods, dimLevel -> {
                RuleColumnValueBean valueBean = new RuleColumnValueBean();
                valueBean.setColumnId(dimLevel.getPeriodId());
                valueBean.setObjectId(dimLevel.getUserId());
                valueBean.setEnumId(String.valueOf(dimLevel.getPeriodLevel()));
                return valueBean;
            });
        },null,

        mainData -> {
            // 查询机构绩效等级
            List<PerfGradePO> options = PerfActivityService.self()
                .getPerfGrades(mainData.getOrgId());
            return BeanCopierUtil.convertList(options, gridLevel -> {
                RuleEnumValueBean option = new RuleEnumValueBean();
                option.setId(String.valueOf(gridLevel.getGradeValue()));
                option.setName(gridLevel.getGradeName());
                option.setDisabled(1 - gridLevel.getState());
                //等级从高到低的排序号是从低到高
                option.setValue(-gridLevel.getOrderIndex());
                return option;
            });
        }),

    ;
    private int columnType;
    private SpConditionTypeEnum conditionType;
    private int valueType;
    private int inputValueType;
    private Function<QueryRuleColumnDto, List<RuleOptionBean>> columnFunc;
    private SpCompareOperator[] compareOperators;
    private Function<QueryRuleColumnValDto, List<RuleColumnValueBean>> columnValFunc;
    private Object defaultColumnVal;
    private Function<RuleMainBase, List<RuleEnumValueBean>> enumValFunc;

    RvRuleTypeEnum(
        int columnType, SpConditionTypeEnum conditionType, int valueType, int inputValueType,
        Function<QueryRuleColumnDto, List<RuleOptionBean>> columnFunc, SpCompareOperator[] compareOperators,
        Function<QueryRuleColumnValDto, List<RuleColumnValueBean>> columnValFunc, Object defaultColumnVal,
        Function<RuleMainBase, List<RuleEnumValueBean>> enumValFunc) {
        this.columnType = columnType;
        this.conditionType = conditionType;
        this.valueType = valueType;
        this.inputValueType = inputValueType;
        this.columnFunc = columnFunc;
        this.compareOperators = compareOperators;
        this.columnValFunc = columnValFunc;
        this.defaultColumnVal = defaultColumnVal;
        this.enumValFunc = enumValFunc;
    }

    @Override
    public int columnType() {
        return columnType;
    }

    @Override
    public SpConditionTypeEnum conditionType() {
        return conditionType;
    }

    @Override
    public int valueType() {
        return valueType;
    }

    @Override
    public int inputValueType() {
        return inputValueType;
    }

    @Override
    public List<RuleOptionBean> allColumns(RuleMainBase mainData) {
        QueryRuleColumnDto queryDto = new QueryRuleColumnDto();
        queryDto.setMainData(mainData);
        queryDto.setQueryAll(true);
        return columnFunc.apply(queryDto);
    }

    @Override
    public List<RuleOptionBean> columns(RuleMainBase mainData, List<String> columnIds) {
        QueryRuleColumnDto queryDto = new QueryRuleColumnDto();
        queryDto.setMainData(mainData);
        queryDto.setQueryAll(true);
        queryDto.setColumnIds(columnIds);
        return columnFunc.apply(queryDto);
    }

    @Override
    public SpCompareOperator[] acceptOperator() {
        if (compareOperators != null && compareOperators.length > 0) {
            return compareOperators;
        }
        return RuleColTypeBase.super.acceptOperator();
    }

    @Override
    public Object defaultValue(String columnId) {
        return defaultColumnVal;
    }

    @Override
    public List<RuleColumnValueBean> columnValue(RuleMainBase mainData, List<String> columnIds, List objectIds) {
        QueryRuleColumnValDto queryDto = new QueryRuleColumnValDto();
        queryDto.setMainData(mainData);
        queryDto.setColumnIds(columnIds);
        queryDto.setObjectIds(objectIds);
        return columnValFunc.apply(queryDto);
    }

    @Override
    public List<RuleEnumValueBean> enumValues(RuleMainBase mainData) {
        return enumValFunc != null ? enumValFunc.apply(mainData) : null;
    }


}
