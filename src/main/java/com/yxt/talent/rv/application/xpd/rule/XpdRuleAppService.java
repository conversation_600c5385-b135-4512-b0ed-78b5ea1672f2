package com.yxt.talent.rv.application.xpd.rule;

import com.alibaba.fastjson.JSON;
import com.vip.vjtools.vjkit.collection.ListUtil;
import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.enums.DeleteEnum;
import com.yxt.spsdfacade.bean.spsd.IndicatorDto;
import com.yxt.spsdfacade.bean.spsd.ModelBase4Facade;
import com.yxt.spsdk.common.bean.RuleMainBase;
import com.yxt.spsdk.common.component.SpRuleService;
import com.yxt.talent.rv.application.xpd.common.CommonValidator;
import com.yxt.talent.rv.application.xpd.common.XpdPOFactory;
import com.yxt.talent.rv.application.xpd.common.dto.ActivityInfoDTO;
import com.yxt.talent.rv.application.xpd.common.dto.AomActvExtBO;
import com.yxt.talent.rv.application.xpd.common.dto.ErrorInfo;
import com.yxt.talent.rv.application.xpd.common.dto.FormulaExpression;
import com.yxt.talent.rv.application.xpd.common.dto.IndicatorInfoDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimCalcDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimLevelRuleDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimPerfResultDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimRuleBaseInDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimRuleCalcInDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimRuleCalcRefDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimRuleCombDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimRuleContextDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimRuleInfoDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimRuleOtherInDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimRulePerfInDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdIndicatorCalcBean;
import com.yxt.talent.rv.application.xpd.common.dto.XpdLevelRuleDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdRuleCalcDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdRuleCalcSdDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdRuleCombDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdRuleInDto;
import com.yxt.talent.rv.application.xpd.common.dto.XpdRuleOutDto;
import com.yxt.talent.rv.application.xpd.common.enums.XpdImportTypeEnum;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.*;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.PerfEvalTypeEnum;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridLevelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRuleCalcPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRulePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdLevelPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleCalcDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleCalcIndicatorPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRulePO;
import com.yxt.talent.rv.infrastructure.repository.xpd.DimGridLevelRuleDTO;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.controller.manage.xpd.rule.enums.DimCalcRuleEnum.*;
import static com.yxt.talent.rv.controller.manage.xpd.rule.enums.DimLevelTypeEnum.*;
import static com.yxt.talent.rv.controller.manage.xpd.rule.enums.DimResultTypeEnum.*;

/**
 * 新盘点-项目规则数据
 *
 * <AUTHOR>
 * @date 2024/12/6 11:23
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class XpdRuleAppService extends XpdRuleBaseService<XpdRuleAppService> {

    private final XpdGridLevelMapper xpdGridLevelMapper;

    private final SpsdAclService spsdAclService;
    private final XpdRuleValidator xpdRuleValidator;
    private final XpdDimRuleValidator xpdDimRuleValidator;
    private final RuleConfComponent ruleConfComponent;
    private final SpRuleService spRuleService;

    public List<IndicatorDto> getLastIndicators(String orgId, String xpdId, String dimId) {
        XpdPO xpdPO = xpdMapper.selectById(xpdId);
        return spsdAclService.getLastIndicators(orgId, xpdPO.getModelId(), Lists.newArrayList(dimId));
    }

    public Map<String, List<IndicatorDto>> getLastIndicators(String orgId, String xpdId) {
        XpdPO xpdPO = xpdMapper.selectById(xpdId);
        List<IndicatorDto> lastIndicators = spsdAclService.getLastIndicators(orgId, xpdPO.getModelId());
        return lastIndicators.stream().collect(Collectors.groupingBy(IndicatorDto::getDmId));
    }

    /**
     * 获取单个维度规则的数据
     * 包括绩效维度和其他维度
     *
     * @param orgId   机构ID
     * @param sdDimId 维度ID
     * @return 维度规则数据
     */
    public XpdDimRuleInfoDto getDimRuleInfo(String orgId, String xpdId, String sdDimId) {

        XpdPO xpd = checkXpdExist(xpdId);
        XpdRuleConfPO xpdRuleConf = checkXpdRuleConfExistByXpdId(orgId, xpdId);

        List<String> allSdDimIds = StreamUtil.mapList(xpdDimMapper.listByXpdId(orgId, xpdId), XpdDimPO::getSdDimId);

        boolean isXpdDim = allSdDimIds.contains(sdDimId);
        if (isXpdDim && dimIsImport(orgId, xpdId, sdDimId)) {
            throw new ApiException(ExceptionKeys.XPD_RULE_CALC_DIM_IMPORT_ERROR);
        }

        // 当前维度及其子维度信息
        List<ModelBase4Facade> modelBases = spsdAclService.getDimInfoList(orgId, xpd.getModelId(), Collections.singletonList(sdDimId));
        Map<String, ModelBase4Facade> modelBaseMap = StreamUtil.list2map(modelBases, ModelBase4Facade::getDmId);
        Map<String, List<ModelBase4Facade>> modelBaseParentIdMap = modelBases.stream().collect(Collectors.groupingBy(ModelBase4Facade::getParentId));

        // 维度在标准那边不存在
        if (!modelBaseMap.containsKey(sdDimId)) {
            throw new ApiException(ExceptionKeys.XPD_DIM_NOT_EXIST);
        }

        List<XpdDimRulePO> dimRuleList = xpdDimRuleMapper.listByXpdId(orgId, xpdId);
        Map<String, XpdDimRulePO> dimRuleMap = getDimRuleMap(dimRuleList);

        // 维度规则未配置,给个默认值
        if (!dimRuleMap.containsKey(sdDimId)) {
            return getDefault(orgId, sdDimId, xpd, xpdRuleConf, modelBaseMap, modelBaseParentIdMap);
        }

        Map<String, List<XpdDimRulePO>> dimRuleParentIdMap = getDimRuleParentIdMap(dimRuleList);
        Map<String, List<XpdDimRuleCalcPO>> calcsMap = xpdDimRuleCalcMapper.listByXpdId(orgId, xpdId).stream().collect(Collectors.groupingBy(XpdDimRuleCalcPO::getSdDimId));
        // 维度的分层规则,为什么要查?因为要获取等级的名称
        Map<String, XpdGridLevelPO> gridLevelMap = StreamUtil.list2map(xpdGridLevelMapper.listByXpdId(orgId, xpdId),
                XpdGridLevelPO::getId);

        XpdDimRulePO xpdDimRule = dimRuleMap.get(sdDimId);
        return fillDimRuleInfo(orgId, isXpdDim, xpdDimRule, xpd, xpdRuleConf, modelBaseMap, modelBaseParentIdMap, dimRuleParentIdMap, calcsMap, gridLevelMap);
    }

    /**
     * 维度没有配置规则时
     * 初始化维度信息
     * 计算方式：当维度下包含子维度时，默认选中按子维度结果计算；当维度下不包含子维度时，默认为按指标结果计算
     * 结果类型：默认选中全局规则中配置的结果类型
     */
    private XpdDimRuleInfoDto getDefault(String orgId, String sdDimId,
                                         XpdPO xpd,
                                         XpdRuleConfPO xpdRuleConf,
                                         Map<String, ModelBase4Facade> modelBaseMap,
                                         Map<String, List<ModelBase4Facade>> modelBaseParentIdMap) {
        XpdDimRuleInfoDto dimRuleInfoDto = new XpdDimRuleInfoDto();

        dimRuleInfoDto.setSdDimId(sdDimId);
        dimRuleInfoDto.setScoreSystem(xpdRuleConf.getScoreSystem());
        if (modelBaseMap.containsKey(sdDimId)) {
            ModelBase4Facade modelBase = modelBaseMap.get(sdDimId);
            // 绩效维度
            if (DimTypeEnum.isPerfName(modelBase.getDmName())) {
                initDimRuleInfo4Perf(dimRuleInfoDto);
            } else {
                // 非绩效维度
                initDimRuleInfo4Other(orgId, xpd, xpdRuleConf, modelBase, modelBaseParentIdMap, dimRuleInfoDto);
            }
        }

        // 分层规则
        dimRuleInfoDto.setLevelList(BeanCopierUtil.convertList(xpdGridLevelMapper.listByGridIdReverse(orgId, xpdRuleConf.getGridId()),
                level -> {
                    XpdDimLevelRuleDto levelDto = new XpdDimLevelRuleDto();
                    levelDto.setGridLevelId(level.getId());
                    levelDto.setLevelName(level.getLevelName());
                    levelDto.setOrderIndex(level.getOrderIndex());
                    return levelDto;
                }));
        return dimRuleInfoDto;
    }

    /**
     * 初始化绩效维度规则信息
     *
     * @param dimRuleInfo 返回值
     */
    private void initDimRuleInfo4Perf(XpdDimRuleInfoDto dimRuleInfo) {
        dimRuleInfo.setCalcType(DimCalcTypeEnum.PERF_RESULT.getCode());
    }

    /**
     * 初始化其他维度规则信息
     *
     * @param dimRuleInfo 返回值
     */
    private void initDimRuleInfo4Other(String orgId,
                                       XpdPO xpd,
                                       XpdRuleConfPO xpdRuleConf,
                                       ModelBase4Facade modelBase,
                                       Map<String, List<ModelBase4Facade>> modelBaseParentIdMap,
                                       XpdDimRuleInfoDto dimRuleInfo) {
        // 非绩效维度
        int calcType = DimCalcTypeEnum.INDICATOR.getCode();
        Map<String, BigDecimal> totalScoreMap = new HashMap<>();
        // 维度下包含子维度时：按子维度结果计算
        if (modelBaseParentIdMap.containsKey(modelBase.getId())) {
            dimRuleInfo.setHasSubDims(true);
            calcType = DimCalcTypeEnum.SUB_DIMENSION.getCode();
            if (XpdConfResultTypeEnum.byScore(xpdRuleConf.getResultType())) {
                // 计算规则
                List<ModelBase4Facade> children = modelBaseParentIdMap.get(modelBase.getId());
                BigDecimal[] weightArray = XpdPOFactory.getEqualWeightArray(children.size());
                BigDecimal totalScore = null;
                List<XpdDimRuleInfoDto> subDims = Lists.newArrayList();
                for (int i = 0; i < children.size(); i++) {
                    ModelBase4Facade subModelBase = children.get(i);
                    String subSdDimId = subModelBase.getDmId();
                    XpdDimCalcDto dimCalcDto = new XpdDimCalcDto();
                    dimCalcDto.setSdDimId(subSdDimId);
                    dimCalcDto.setWeight(weightArray[i]);
                    calcDimScoreInit(orgId, dimCalcDto, xpd, xpdRuleConf, modelBase, modelBaseParentIdMap, totalScoreMap);
                    if (dimCalcDto.getTotalScore() != null) {
                        if (totalScore == null) {
                            totalScore = BigDecimal.ZERO;
                        }
                        totalScore = totalScore.add(calcWeightedTotalScoreByDim(dimCalcDto));
                    }

                    XpdDimRuleInfoDto subDim = new XpdDimRuleInfoDto();
                    subDim.setSdDimId(subSdDimId);
                    subDim.setDimName(subModelBase.getDmName());
                    subDim.setWeight(weightArray[i]);
                    subDim.setTotalScore(dimCalcDto.getTotalScore());
                    subDims.add(subDim);
                }
                dimRuleInfo.setChildren(subDims);
                dimRuleInfo.setTotalScore(totalScore);
            }
        } else {
            // 维度下不包含子维度时:按指标结果计算
            dimRuleInfo.setHasSubDims(false);
            Map<String, List<IndicatorDto>> indicatorsMap = spsdAclService.getLastIndicators(orgId, xpd.getModelId())
                    .stream().collect(Collectors.groupingBy(IndicatorDto::getRequireBaseId));
            if (indicatorsMap.containsKey(modelBase.getId())) {
                // 维度下所有的指标
                List<XpdIndicatorCalcBean> ruleCalcList = getIndicatorCalcs(orgId, xpdRuleConf.getResultType(), xpd, indicatorsMap.get(modelBase.getId()));
                dimRuleInfo.setIndicatorCalcs(ruleCalcList);
                // 求维度分
                BigDecimal totalScore = BigDecimal.ZERO;
                for (XpdIndicatorCalcBean indicatorCalc : ruleCalcList) {
                    if (indicatorCalc.getTotalScore() != null) {
                        totalScore = totalScore.add(indicatorCalc.getTotalScore());
                    }
                }
            }
        }
        dimRuleInfo.setCalcRule(NORMAL.getCode());
        dimRuleInfo.setDimName(modelBase.getDmName());
        // 计算方式
        dimRuleInfo.setCalcType(calcType);
        // 结果类型
        dimRuleInfo.setResultType(xpdRuleConf.getResultType());
        // 分层方式
        dimRuleInfo.setLevelType(RATIO_VALUE.getCode());
        // 分层优先级
        dimRuleInfo.setLevelPriority(DimLevelPriorityEnum.getDefault().getCode());
    }

    /**
     * 如果维度没有规则，初始化维度的维度分（从标准中获取维度的子维度和指标）
     *
     * @param dimCalcDto           维度计算Bean
     * @param modelBase            维度的基础信息
     * @param modelBaseParentIdMap 维度的树结构
     * @param totalScoreMap        返回值
     */
    private void calcDimScoreInit(String orgId,
                                  XpdDimCalcDto dimCalcDto,
                                  XpdPO xpd,
                                  XpdRuleConfPO xpdRuleConf,
                                  ModelBase4Facade modelBase,
                                  Map<String, List<ModelBase4Facade>> modelBaseParentIdMap,
                                  Map<String, BigDecimal> totalScoreMap) {

        BigDecimal totalScore = null;
        // 有子维度
        if (modelBaseParentIdMap.containsKey(modelBase.getId())) {
            List<ModelBase4Facade> modelBases = modelBaseParentIdMap.get(modelBase.getId());
            int size = modelBases.size();
            BigDecimal[] weightArray = XpdPOFactory.getEqualWeightArray(size);
            for (int i = 0; i < modelBases.size(); i++) {
                XpdDimCalcDto subDimCalcDto = new XpdDimCalcDto();
                subDimCalcDto.setSdDimId(modelBases.get(i).getDmId());
                subDimCalcDto.setWeight(weightArray[i]);
                calcDimScoreInit(orgId, subDimCalcDto, xpd, xpdRuleConf, modelBases.get(i), modelBaseParentIdMap, totalScoreMap);
                if (subDimCalcDto.getTotalScore() != null) {
                    if (totalScore == null) {
                        totalScore = BigDecimal.ZERO;
                    }
                    totalScore = totalScore.add(calcWeightedTotalScoreByDim(subDimCalcDto));
                }
            }
        } else {
            // 无子维度
            // 维度的所有指标
            List<IndicatorDto> indicators = spsdAclService.getLastIndicatorByDims(orgId, xpd.getId(), modelBase.getDmId());
            List<XpdIndicatorCalcBean> indicatorCalcs = getIndicatorCalcs(orgId, xpdRuleConf.getResultType(), xpd, indicators);
            totalScore = calcTotalValueByIndicator(indicatorCalcs);
        }
        dimCalcDto.setTotalScore(totalScore);
        totalScoreMap.put(modelBase.getDmId(), totalScore);
    }

    private List<XpdIndicatorCalcBean> getIndicatorCalcs(String orgId, Integer confResultType, XpdPO xpd, List<IndicatorDto> indicators) {
        List<XpdIndicatorCalcBean> ruleCalcList = Lists.newArrayList();
        BigDecimal[] weightArray = XpdPOFactory.getEqualWeightArray(indicators.size());

        List<String> indicatorIds = StreamUtil.mapList(indicators, IndicatorDto::getItemId);
        Map<String, List<ActivityInfoDTO>> indicatorActvs = xpdAomService.listActivityByIndicators(orgId, xpd.getId(), indicatorIds);

        for (int i = 0; i < indicators.size(); i++) {
            XpdIndicatorCalcBean calcDto = new XpdIndicatorCalcBean();
            String indicatorId = indicators.get(i).getItemId();
            calcDto.setSdIndicatorId(indicatorId);
            calcDto.setSdIndicatorName(indicators.get(i).getIndicatorName());
            calcDto.setWeight(weightArray[i]);
            // 数据来源
            if (indicatorActvs.containsKey(indicatorId)) {
                List<ActivityInfoDTO> actvList = indicatorActvs.get(indicatorId);
                List<XpdDimRuleCalcRefDto> refList = BeanCopierUtil.convertList(actvList, actv -> {
                    XpdDimRuleCalcRefDto refDto = new XpdDimRuleCalcRefDto();
                    refDto.setRefId(actv.getActvId());
                    refDto.setRefType(actv.getActvType());
                    refDto.setRefName(actv.getActvName());
                    return refDto;
                });
                if (refList.size() == 1) {
                    calcDto.setCalcMethod(DimCalcMethodEnum.NONE.getCode());
                } else {
                    if (XpdConfResultTypeEnum.byScore(confResultType)) {
                        calcDto.setCalcMethod(DimCalcMethodEnum.AVG.getCode());
                    } else {
                        calcDto.setCalcMethod(DimCalcMethodEnum.QUALIFIED_ONE.getCode());
                    }
                }
                calcDto.setRefList(refList);
            } else {
                XpdDimRuleCalcRefDto refDto = new XpdDimRuleCalcRefDto();
                refDto.setRefType(DimRuleCalcRefEnum.PRI_PROFILE.getCode());
                refDto.setRefName(DimRuleCalcRefEnum.PRI_PROFILE.getName());
                refDto.setRefId(String.valueOf(DimRuleCalcRefEnum.PRI_PROFILE.getCode()));
                calcDto.setRefList(ListUtil.singletonList(refDto));
            }
            calcDto.setRefList(Lists.newArrayList());
            ruleCalcList.add(calcDto);
        }
        if (XpdConfResultTypeEnum.byScore(confResultType)) {
            calcIndicatorWeightedTotalScoreByScoreSystem(xpd, ruleCalcList);
        }

        return ruleCalcList;
    }

    /**
     * 填充维度的信息
     *
     * @param orgId              机构ID
     * @param isXpdDim           当前维度是否为盘点维度
     * @param curDimRule         当前的维度规则
     * @param xpdRuleConf
     * @param modelBaseMap       维度的基础信息
     * @param dimRuleParentIdMap 维度子规则map key:父规则ID value:子维度规则列表
     * @param gridLevelMap       宫格的层级信息Map
     * @return XpdDimRuleInfoDto
     */
    private XpdDimRuleInfoDto fillDimRuleInfo(String orgId,
                                              boolean isXpdDim,
                                              XpdDimRulePO curDimRule,
                                              XpdPO xpd,
                                              XpdRuleConfPO xpdRuleConf,
                                              Map<String, ModelBase4Facade> modelBaseMap,
                                              Map<String, List<ModelBase4Facade>> modelBaseParentIdMap,
                                              Map<String, List<XpdDimRulePO>> dimRuleParentIdMap,
                                              Map<String, List<XpdDimRuleCalcPO>> calcsMap,
                                              Map<String, XpdGridLevelPO> gridLevelMap) {

        if (!modelBaseMap.containsKey(curDimRule.getSdDimId())) {
            return new XpdDimRuleInfoDto();
        }

        XpdDimRuleInfoDto curDimRuleOut = new XpdDimRuleInfoDto();
        BeanCopierUtil.copy(curDimRule, curDimRuleOut);
        ModelBase4Facade modelBase = modelBaseMap.get(curDimRule.getSdDimId());
        curDimRuleOut.setSdDimId(curDimRule.getSdDimId());
        curDimRuleOut.setDimRuleId(curDimRule.getId());
        curDimRuleOut.setDimName(modelBase.getDmName());
        curDimRuleOut.setScoreSystem(xpdRuleConf.getScoreSystem());
        curDimRuleOut.setFormulaExpressions(JSON.parseArray(curDimRule.getFormulaExpression(), FormulaExpression.class));
        curDimRuleOut.setFormulaExpCodes(JSON.parseArray(curDimRule.getFormulaExpCode(), String.class));
        // 维度的分层规则
        curDimRuleOut.setLevelList(convert2LevelRuleList(curDimRule, gridLevelMap));

        // 绩效维度 && 绩效结果计算, 需要转换分层规则的绩效结果
        if (DimTypeEnum.isPerfName(curDimRuleOut.getDimName())) {
            curDimRuleOut.setHasSubDims(false);
            if (StringUtils.isEmpty(curDimRule.getAomActId())) {
                curDimRuleOut.setError(new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_REF_NOTEXIST, "数据来源的绩效活动不存在"));
                return curDimRuleOut;
            }

            List<AomActvExtBO> activities = xpdAomService.activityIndicatorList(orgId, xpd.getAomPrjId(), Lists.newArrayList(curDimRule.getAomActId()));
            if (CollectionUtils.isEmpty(activities)) {
                curDimRuleOut.setError(new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_REF_NOTEXIST, "数据来源的绩效活动不存在"));
                return curDimRuleOut;
            }

            AomActvExtBO activity = activities.get(0);
            curDimRuleOut.setAomActvName(activity.getActvRefName());
            // 按绩效得分计算
            if (DimCalcTypeEnum.byPerfScore(curDimRule.getCalcType())) {
                // 校验维度规则
                BigDecimal totalScore = activity.getIndicators().get(0).getTotalScore();
                ErrorInfo errorInfo =
                    XpdDimRuleValidator.validateLevelValue(
                        curDimRule.getLevelType(), curDimRuleOut.getLevelList(), true, totalScore,
                        xpdRuleConf.getScoreSystem());
                if (Objects.nonNull(errorInfo)) {
                    curDimRuleOut.setError(errorInfo);
                }
            } else if (DimCalcTypeEnum.byPerfResult(curDimRule.getCalcType())) {
                // 填充绩效为维度的绩效结果
                fillDimRuleMatchValues4Perf(orgId, xpd, curDimRule.getAomActId(), curDimRuleOut);
            }
        } else {
            curDimRuleOut.setHasSubDims(modelBaseParentIdMap.containsKey(modelBase.getId()));

            // 校验维度分层
            if (isXpdDim && Objects.isNull(curDimRuleOut.getError())) {
                ErrorInfo error = validateLevelValueOther(
                    orgId, xpd, curDimRule, dimRuleParentIdMap, calcsMap,
                    curDimRuleOut.getLevelList(), xpdRuleConf.getScoreSystem());
                if (Objects.isNull(curDimRuleOut.getError())) {
                    curDimRuleOut.setError(error);
                }
            }

            // 非绩效维度，填充计算规则
            fillCalcList(orgId, xpd, curDimRule, modelBaseMap, dimRuleParentIdMap, calcsMap, curDimRuleOut);
        }
        return curDimRuleOut;
    }

    private ErrorInfo validateLevelValueOther(String orgId, XpdPO xpd, XpdDimRulePO curDimRule,
                                              Map<String, List<XpdDimRulePO>> dimRuleParentIdMap,
                                              Map<String, List<XpdDimRuleCalcPO>> calcsMap,
                                              List<XpdDimLevelRuleDto> levelList, Integer scoreSystem) {
        // 校验维度分层
        boolean checkLevelValue = false;
        Map<String, BigDecimal> totalScoreMap = new HashMap<>();
        // 结果类型：分值 && 分层规则：按固定值 && 计算规则：快捷配置
        if (byScore(curDimRule.getResultType()) && byFixedValue(curDimRule.getLevelType()) && byNormal(curDimRule.getCalcRule())) {
            checkLevelValue = true;
            Map<String, XpdDimRulePO> dimRuleMap = getDimRuleMap(orgId, xpd.getId());
            XpdDimCalcDto dimCalcDto = new XpdDimCalcDto();
            dimCalcDto.setSdDimId(curDimRule.getSdDimId());
            dimCalcDto.setWeight(curDimRule.getWeight());
            //            totalScoreMap = getTotalScore4Dims(xpd, ListUtil.singletonList(dimCalcDto), dimRuleMap, dimRuleParentIdMap, calcsMap);
            calcDimValue(dimCalcDto, xpd, dimRuleMap, dimRuleParentIdMap, calcsMap, totalScoreMap);
        }
        BigDecimal maxValue = totalScoreMap.get(curDimRule.getSdDimId());
        return XpdDimRuleValidator.validateLevelValue(curDimRule.getLevelType(), levelList, checkLevelValue, maxValue, scoreSystem);
    }

    /**
     * 填充绩效为维度的绩效结果
     */
    private void fillDimRuleMatchValues4Perf(String orgId, XpdPO xpd, String actvPerfId, XpdDimRuleInfoDto curDimRuleOut) {
        if (CollectionUtils.isEmpty(curDimRuleOut.getLevelList())) {
            return;
        }
        Map<String, XpdDimPerfResultDto> perfResultMap = StreamUtil.list2map(getPerfResults(orgId, xpd.getId(), actvPerfId), XpdDimPerfResultDto::getId);
        for (XpdDimLevelRuleDto levelRuleDto : curDimRuleOut.getLevelList()) {
            List<XpdDimPerfResultDto> resultDtos = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(levelRuleDto.getMatchValues())) {
                for (String resultId : levelRuleDto.getMatchValues()) {
                    if (perfResultMap.containsKey(resultId)) {
                        XpdDimPerfResultDto resultDto = new XpdDimPerfResultDto();
                        resultDto.setId(resultId);
                        resultDto.setName(perfResultMap.get(resultId).getName());
                        resultDtos.add(resultDto);
                    } else {
                        curDimRuleOut.setError(new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_LEVEL_RESULT_ERROR, "分层规则的绩效结果不存在,请检查"));
                    }
                }
            } else {
                curDimRuleOut.setError(new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_LEVEL_RESULT_EMPTY, "分层规则的绩效结果为空,请检查"));
            }
            levelRuleDto.setMatchValueList(resultDtos);
        }
    }

    private void fillCalcList(String orgId,
                              XpdPO xpd, XpdDimRulePO curDimRule,
                              Map<String, ModelBase4Facade> modelBaseMap,
                              Map<String, List<XpdDimRulePO>> dimRuleParentIdMap,
                              Map<String, List<XpdDimRuleCalcPO>> calcsMap,
                              XpdDimRuleInfoDto dimRuleDetail) {
        // 非绩效维度，填充计算规则
        // 按指标结果计算 or 末级维度
        ErrorInfo errorInfo = null;
        if (DimCalcTypeEnum.byIndicator(curDimRule.getCalcType())) {
            List<XpdDimRuleCalcPO> dimRuleCalcs = calcsMap.get(curDimRule.getSdDimId());
            errorInfo = xpdDimRuleValidator.validateRefIdsAndFormula(xpd.getAomPrjId(), curDimRule, dimRuleCalcs);
            List<XpdIndicatorCalcBean> indicatorCalcs = convert2CalcBeans(orgId, xpd, dimRuleDetail.getDimName(), dimRuleCalcs);
            dimRuleDetail.setIndicatorCalcs(indicatorCalcs);
            if (Objects.isNull(dimRuleDetail.getError())) {
                dimRuleDetail.setError(errorInfo);
            }

            // 结果类型：分值
            if (byScore(curDimRule.getResultType())) {
                // 校验计算规则 以及 求维度分
                calcDimTotalScoreAndError(xpd, indicatorCalcs, dimRuleDetail);
            }
        } else {
            // 按子维度结果计算
            List<XpdDimRuleInfoDto> children = Lists.newArrayList();
            List<XpdDimRulePO> subDimRules = dimRuleParentIdMap.get(curDimRule.getId());
            BigDecimal totalScore = null;
            for (XpdDimRulePO subDimRule : subDimRules) {
                XpdDimRuleInfoDto child = new XpdDimRuleInfoDto();
                BeanCopierUtil.copy(subDimRule, child);
                child.setDimRuleId(subDimRule.getId());
                child.setScoreSystem(dimRuleDetail.getScoreSystem());
                child.setDimName(modelBaseMap.get(subDimRule.getSdDimId()).getDmName());
                child.setLevelList(JSON.parseArray(subDimRule.getLevelRule(), XpdDimLevelRuleDto.class));
                fillCalcList(orgId, xpd, subDimRule, modelBaseMap, dimRuleParentIdMap, calcsMap, child);
                if (byScore(curDimRule.getResultType()) && child.getTotalScore() != null) {
                    totalScore = totalScore == null ? BigDecimal.ZERO : totalScore;
                    totalScore = totalScore.add(child.getTotalScore());
                }
                children.add(child);
            }
            totalScore = calcWeightedTotalScore(totalScore, dimRuleDetail.getWeight());
            dimRuleDetail.setTotalScore(totalScore);
            dimRuleDetail.setChildren(children);
        }
    }

    protected List<XpdIndicatorCalcBean> convert2CalcBeans(String orgId, XpdPO xpd, String dimName, List<XpdDimRuleCalcPO> calcs) {
        if (CollectionUtils.isEmpty(calcs)) {
            return Lists.newArrayList();
        }
        List<XpdIndicatorCalcBean> calcBeans = BeanCopierUtil.convertList(calcs, ruleCalc -> {
            XpdIndicatorCalcBean calcDto = new XpdIndicatorCalcBean();
            BeanCopierUtil.copy(ruleCalc, calcDto);
            try {
                List<XpdDimRuleCalcRefDto> refDtos = JSON.parseArray(ruleCalc.getRefIds(), XpdDimRuleCalcRefDto.class);
                if (CollectionUtils.isEmpty(refDtos)) {
                    return calcDto;
                }
                List<String> refIds = refDtos.stream().map(XpdDimRuleCalcRefDto::getRefId)
                        .filter(StringUtils::isNotEmpty)
                        .filter(r -> !String.valueOf(DimRuleCalcRefEnum.PRI_PROFILE.getCode()).equals(r) && !String.valueOf(DimRuleCalcRefEnum.IMPORT_DATA.getCode()).equals(r))
                        .collect(Collectors.toList());
                Map<String, ActivityArrangeItem> actvMap = new HashMap<>();
                Map<String, XpdImportPO> importMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(refIds)) {
                    List<ActivityArrangeItem> items = xpdAomService.activityList(orgId, xpd.getAomPrjId(), refIds);
                    actvMap = StreamUtil.list2map(items, ActivityArrangeItem::getRefId);
                    importMap = StreamUtil.list2map(xpdImportMapper.selectByIds(orgId, refIds), XpdImportPO::getId);
                }
                // 填充活动名称
                for (XpdDimRuleCalcRefDto refDto : refDtos) {
                    // 个人档案
                    if (DimRuleCalcRefEnum.PRI_PROFILE.getCode() == refDto.getRefType()) {
                        refDto.setRefName(DimRuleCalcRefEnum.PRI_PROFILE.getName());
                        refDto.setRefType(DimRuleCalcRefEnum.PRI_PROFILE.getCode());
                    } else {
                        if (actvMap.containsKey(refDto.getRefId())) {
                            refDto.setRefName(actvMap.get(refDto.getRefId()).getRefName());
                            refDto.setRefType(DimRuleCalcRefEnum.AOM_ACT.getCode());
                        } else if (importMap.containsKey(refDto.getRefId())) {
                            refDto.setRefName(DimRuleCalcRefEnum.IMPORT_DATA.getName() + "-" + dimName);
                            refDto.setRefType(DimRuleCalcRefEnum.IMPORT_DATA.getCode());
                        }
                    }
                }
                calcDto.setRefList(refDtos);
            } catch (Exception e) {
                log.error("JSON转换出错, refIds: {}", JSON.toJSONString(ruleCalc.getRefIds()));
            }
            return calcDto;
        });

        List<String> indicatorIds = StreamUtil.mapList(calcBeans, XpdIndicatorCalcBean::getSdIndicatorId);
        if (CollectionUtils.isNotEmpty(indicatorIds)) {
            // 填充指标名称
            Map<String, IndicatorDto> indicatorInfoMap = StreamUtil.list2map(spsdAclService.getIndicatorInfo(orgId, xpd.getModelId(), indicatorIds), IndicatorDto::getItemId);
            for (XpdIndicatorCalcBean indicatorCalc : calcBeans) {
                if (indicatorInfoMap.containsKey(indicatorCalc.getSdIndicatorId())) {
                    indicatorCalc.setSdIndicatorName(indicatorInfoMap.get(indicatorCalc.getSdIndicatorId()).getIndicatorName());
                }
            }
        }
        return calcBeans;
    }

    private void calcDimTotalScoreAndError(XpdPO xpd, List<XpdIndicatorCalcBean> indicatorCalcs, XpdDimRuleInfoDto dimRuleDetail) {
        ErrorInfo errorInfo = null;
        // 求维度分
        BigDecimal totalScore = null;
        calcIndicatorWeightedTotalScoreByScoreSystem(xpd, indicatorCalcs);
        for (XpdIndicatorCalcBean indicatorCalc : indicatorCalcs) {
            List<XpdDimRuleCalcRefDto> refList = indicatorCalc.getRefList();
            if (CollectionUtils.isNotEmpty(refList)) {
                for (XpdDimRuleCalcRefDto ref : refList) {
                    if (ref.isRefIdNotExist() || ref.isNotRefIndicator()) {
                        errorInfo = new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_REF_NOTEXIST, "数据来源的活动不存在");
                        break;
                    }
                }
                if (Objects.isNull(dimRuleDetail.getError()) && Objects.nonNull(errorInfo)) {
                    dimRuleDetail.setError(errorInfo);
                    return;
                }
                totalScore = totalScore == null ? BigDecimal.ZERO : totalScore;
                totalScore = totalScore.add(indicatorCalc.getTotalScore());
            }
        }
        totalScore = calcWeightedTotalScore(totalScore, dimRuleDetail.getWeight());
        dimRuleDetail.setTotalScore(totalScore);
    }

    public Map<String, BigDecimal> getTotalScoreMap4Xpd(String orgId, String xpdId) {
        XpdPO xpd = checkXpdExist(xpdId);
        XpdRulePO xpdRule = xpdRuleMapper.getByXpdId(orgId, xpdId);
        List<XpdIndicatorCalcBean> calcDtoList = Lists.newArrayList();
        return calcXpdTotalScoreAndDimScores(orgId, xpd, xpdRule, false, calcDtoList);
    }

    /**
     * 获取项目计算规则
     *
     * @param orgId 机构ID
     * @param xpdId 项目ID
     * @return 详细规则数据
     */
    public XpdRuleOutDto getXpdRuleInfo(String orgId, String xpdId) {

        XpdPO xpd = checkXpdExist(xpdId);
        XpdRuleConfPO xpdRuleConf = xpdRuleConfMapper.selectByXpdId(orgId, xpdId);
        XpdRulePO xpdRule = xpdRuleMapper.getByXpdId(orgId, xpdId);
        if (Objects.isNull(xpdRuleConf) || Objects.isNull(xpdRule)) {
            return new XpdRuleOutDto();
        }

        XpdRuleOutDto ruleInfoDto = new XpdRuleOutDto();
        BeanCopierUtil.copy(xpdRule, ruleInfoDto);
        ruleInfoDto.setXpdRuleId(xpdRule.getId());
        ruleInfoDto.setHasImport(0);
        ruleInfoDto.setScoreSystem(xpdRuleConf.getScoreSystem());
        ruleInfoDto.setFormulaExpressions(JSON.parseArray(xpdRule.getFormulaExpression(), FormulaExpression.class));
        ruleInfoDto.setFormulaExpCodes(JSON.parseArray(xpdRule.getFormulaExpCode(), String.class));
        // 项目的分层
        List<XpdLevelPO> xpdLevels = getXpdLevelRules(orgId, xpdRule.getId());
        ruleInfoDto.setLevelRuleList(convert2XpdLevelRules(xpdLevels));
        ruleInfoDto.setConfResultType(xpdRuleConf.getResultType());

        ErrorInfo errorInfo;
        // 校验结果类型
        List<XpdImportPO> importDims = xpdImportMapper.findByXpdIdAndImportType(orgId, xpdRule.getXpdId(), XpdImportTypeEnum.DIM.getCode(), true);
        if (CollectionUtils.isNotEmpty(importDims)) {
            ruleInfoDto.setHasImport(1);
        }

        errorInfo = xpdRuleValidator.validateResultType(orgId, xpdRule.getResultType(), xpdRuleConf.getResultType(), xpdRule.getCalcType(), importDims);
        ruleInfoDto.setErrorInfo(errorInfo);

        List<XpdIndicatorCalcBean> calcDtoList = Lists.newArrayList();
        Map<String, BigDecimal> totalScoreMap = calcXpdTotalScoreAndDimScores(orgId, xpd, xpdRule, false, calcDtoList);

        if (Objects.isNull(ruleInfoDto.getErrorInfo())) {
            // 校验分层规则
            ruleInfoDto.setErrorInfo(xpdRuleValidator.validateLevelRule(xpdRule, xpdLevels, totalScoreMap.get(xpdId)));
        }

        // 计算方式：按指标结果计算
        if (XpdCalcTypeEnum.byIndicator(xpdRule.getCalcType())) {
            // 校验计算规则
            // 结果类型：得分
            if (Objects.isNull(ruleInfoDto.getErrorInfo())) {
                if (XpdCalcRuleEnum.isNormal(xpdRule.getCalcRule())) {
                    List<XpdRuleCalcIndicatorPO> cals = xpdRuleCalcIndicatorMapper.listByXpdRuleId(orgId, xpdRule.getId());
                    ruleInfoDto.setErrorInfo(xpdRuleValidator.validateRefIds(orgId, xpd, cals));
                } else {
                    // 计算规则：高级公式
                    ruleInfoDto.setErrorInfo(xpdRuleValidator.validateFormula(orgId, xpd, xpdRule.getFormula()));
                }
            }
            // 计算规则
            ruleInfoDto.setRuleCalcList(convert2XpdRuleCalcsByIndicator(orgId, xpd.getModelId(), calcDtoList));
        } else {
            // 计算方式：按维度结果计算
            // 结果类型: 达标率 over
            if (XpdResultTypeEnum.byRatio(xpdRule.getResultType()) || XpdResultTypeEnum.byDimLevelResult(xpdRule.getResultType())) {
                return ruleInfoDto;
            }

            // 结果类型；分值
            // 计算规则中的维度
            List<XpdRuleCalcDimPO> dims = xpdRuleCalcDimMapper.listByXpdRuleId(orgId, xpdRule.getId());
            if (Objects.isNull(ruleInfoDto.getErrorInfo()) && CollectionUtils.isEmpty(dims)) {
                ruleInfoDto.setErrorInfo(new ErrorInfo(RuleErrorCodeEnum.EMPTY.getCode(), ExceptionKeys.XPD_RULE_CALC_EMPTY, RULE_CALC_EMPTY));
                return ruleInfoDto;
            }
            // 计算规则
            ruleInfoDto.setRuleCalcList(convert2XpdRuleCalcsByDim(orgId, xpd.getModelId(), dims, totalScoreMap));

            if (Objects.isNull(ruleInfoDto.getErrorInfo()) && XpdLevelTypeEnum.byFixedValue(xpdRule.getLevelType()) && totalScoreMap.containsKey(xpdId)) {
                errorInfo = CommonValidator.checkLevelValue(ruleInfoDto.getLevelRuleList(), totalScoreMap.get(xpdId));
                ruleInfoDto.setErrorInfo(errorInfo);
            }
        }
        return ruleInfoDto;
    }

    private List<XpdRuleCalcDto> convert2XpdRuleCalcsByIndicator(String orgId, String modelId, List<XpdIndicatorCalcBean> calcDtoList) {
        List<String> sdIndicatorIds = StreamUtil.mapList(calcDtoList, XpdIndicatorCalcBean::getSdIndicatorId);
        Map<String, IndicatorDto> indicatorInfoMap = spsdAclService.getIndicatorInfoMap(orgId, modelId, sdIndicatorIds);

        List<XpdRuleCalcDto> ruleCalcs = Lists.newArrayList();
        for (XpdIndicatorCalcBean calcDto : calcDtoList) {
            if (indicatorInfoMap.containsKey(calcDto.getSdIndicatorId())) {
                XpdRuleCalcDto ruleCalcDto = new XpdRuleCalcDto();
                XpdRuleCalcSdDto sdDto = new XpdRuleCalcSdDto();
                sdDto.setSdId(calcDto.getSdIndicatorId());
                sdDto.setSdName(indicatorInfoMap.get(calcDto.getSdIndicatorId()).getIndicatorName());
                ruleCalcDto.setSdDtos(Lists.newArrayList(sdDto));
                ruleCalcDto.setWeight(calcDto.getWeight());
                ruleCalcDto.setCalcMethod(calcDto.getCalcMethod());
                ruleCalcDto.setTotalScore(calcDto.getTotalScore());
                ruleCalcDto.setRefList(calcDto.getRefList());
                ruleCalcs.add(ruleCalcDto);
            }
        }
        return ruleCalcs;
    }

    private List<XpdRuleCalcDto> convert2XpdRuleCalcsByDim(String orgId, String modelId, List<XpdRuleCalcDimPO> dims, Map<String, BigDecimal> totalScoreMap) {
        List<String> sdDimIds = StreamUtil.mapList(dims, XpdRuleCalcDimPO::getSdDimId);
        Map<String, ModelBase4Facade> dimInfoMap = spsdAclService.getDimInfoMap(orgId, modelId, sdDimIds);
        List<XpdRuleCalcDto> ruleCalcs = Lists.newArrayList();
        for (XpdRuleCalcDimPO dim : dims) {
            if (dimInfoMap.containsKey(dim.getSdDimId())) {
                XpdRuleCalcDto ruleCalcDto = new XpdRuleCalcDto();
                XpdRuleCalcSdDto sdDto = new XpdRuleCalcSdDto();
                sdDto.setSdId(dim.getSdDimId());
                sdDto.setSdName(dimInfoMap.get(dim.getSdDimId()).getDmName());
                ruleCalcDto.setSdDtos(Lists.newArrayList(sdDto));
                ruleCalcDto.setWeight(dim.getWeight());
                if (totalScoreMap.containsKey(dim.getSdDimId())) {
                    ruleCalcDto.setTotalScore(totalScoreMap.get(dim.getSdDimId()));
                }
                ruleCalcs.add(ruleCalcDto);
            }
        }
        return ruleCalcs;
    }

    /**
     * 保存项目规则
     *
     * @param orgId  机构ID
     * @param userId 登录人ID
     * @param inDto  项目规则入参
     */
    public ErrorInfo upXpdRuleInfo(String orgId, String userId, XpdRuleInDto inDto) {

        // 1.校验
        XpdPO xpd = checkXpdExist(inDto.getXpdId());
        XpdRulePO xpdRule = checkXpdRuleExistByXpdId(orgId, inDto.getXpdId());
        XpdRuleConfPO xpdRuleConf = checkXpdRuleConfExistByXpdId(orgId, inDto.getXpdId());

        // 校验结果类型
        List<XpdImportPO> importDims = xpdImportMapper.findByXpdIdAndImportType(orgId, xpdRule.getXpdId(), XpdImportTypeEnum.DIM.getCode(), true);
        ErrorInfo errorInfo = xpdRuleValidator.validateResultType(orgId, inDto.getResultType(), xpdRuleConf.getResultType(), inDto.getCalcType(), importDims);
        if (Objects.nonNull(errorInfo)) {
            return errorInfo;
        }

        XpdRuleCombDto combDto = XpdRuleCombDto.builder().build();
        // 2.保存
        xpdRule.setCalcType(inDto.getCalcType());
        xpdRule.setResultType(inDto.getResultType());
        if (XpdConfResultTypeEnum.byRate(xpdRuleConf.getResultType())) {
            // 如果计算方式是达标率，则计算规则只能是快捷配置，前端没有改，我们这里做了兼容
            inDto.setCalcRule(XpdCalcRuleEnum.NORMAL.getCode());
        }
        xpdRule.setCalcRule(inDto.getCalcRule() == null ? XpdCalcRuleEnum.NORMAL.getCode() : inDto.getCalcRule());
        xpdRule.setFormula(inDto.getFormula());
        xpdRule.setFormulaDisplay(inDto.getFormulaDisplay());
        xpdRule.setFormulaExpression(CollectionUtils.isNotEmpty(inDto.getFormulaExpressions()) ? JSON.toJSONString(inDto.getFormulaExpressions()) : null);
        xpdRule.setFormulaExpCode(CollectionUtils.isNotEmpty(inDto.getFormulaExpCodes()) ? JSON.toJSONString(inDto.getFormulaExpCodes()) : null);
        xpdRule.setLevelType(inDto.getLevelType() == null ? XpdLevelTypeEnum.getDefault().getCode() : inDto.getLevelType());
        xpdRule.setLevelPriority(inDto.getLevelPriority() == null ? XpdLevelPriorityEnum.getDefault().getCode() : inDto.getLevelPriority());
        xpdRule.setRuleDesc(inDto.getRuleDesc());
        xpdRule.setUpdateUserId(userId);
        xpdRule.setUpdateTime(LocalDateTime.now());
        combDto.setUpXpdRule(xpdRule);

        // 校验计算规则 && 分层规则
        BigDecimal totalScore = null;
        // 结果类型：得分 && 分层方式：固定值
        if (XpdResultTypeEnum.byScore(inDto.getResultType()) && XpdLevelTypeEnum.byFixedValue(inDto.getLevelType())) {
            totalScore = getTotalScore4XpdUp(orgId, xpd, inDto);
        }
        errorInfo = xpdRuleValidator.validateCalcAndLevelRule(orgId, xpd, xpdRule, inDto.getRuleCalcList(), inDto.getLevelRuleList(), totalScore);
        if (Objects.nonNull(errorInfo)) {
            return errorInfo;
        }

        // 计算规则
        // 计算方式：按指标结果计算
        if (XpdCalcTypeEnum.byIndicator(xpdRule.getCalcType())) {
            // 计算规则：快捷配置 || 盘点结果类型时达标率时，只能选择快捷配置
            if (XpdCalcRuleEnum.isNormal(xpdRule.getCalcRule())) {
                AtomicInteger orderIndex = new AtomicInteger(0);
                List<XpdImportPO> importIndicators = xpdImportMapper.findByXpdIdAndImportType(orgId, xpd.getId(), XpdImportTypeEnum.DIM_INDICATOR.getCode(), false);
                for (XpdRuleCalcDto ruleCalcDto : inDto.getRuleCalcList()) {
                    ruleCalcDto.setRefList(convertRefIds(ruleCalcDto.getRefIds(), importIndicators));
                }
                List<XpdRuleCalcIndicatorPO> calcIndicators = BeanCopierUtil.convertList(
                        inDto.getRuleCalcList(),
                        calcDto -> XpdPOFactory.buildXpdRuleCalcIndicator(orgId, userId, xpd.getId(), xpdRule.getId(),
                                orderIndex.incrementAndGet(),
                                calcDto));
                combDto.setNewRuleCalcIndicatorList(calcIndicators);
            }
        } else {
            // 计算方式：按维度结果计算
            // 结果类型：维度得分
            if (XpdResultTypeEnum.byScore(xpdRule.getResultType())) {
                AtomicInteger orderIndex = new AtomicInteger(0);
                List<XpdRuleCalcDimPO> calcDims = BeanCopierUtil.convertList(
                        inDto.getRuleCalcList(),
                        calcDto -> XpdPOFactory.buildXpdRuleCalcDim(orgId, userId, xpd.getId(), xpdRule.getId(),
                                orderIndex.incrementAndGet(),
                                calcDto));
                combDto.setNewRuleCalcDimList(calcDims);
            }
        }

        // 分层规则
        RuleMainBase mainData = new RuleMainBase();
        mainData.setOrgId(orgId);
        mainData.setBizId(inDto.getXpdId());
        spRuleService.calcRuleDisplay(mainData, inDto.getLevelRuleList(), XpdLevelRuleDto::getSpRuleBean,
                XpdLevelRuleDto::setFormulaDisplay);
        AtomicInteger orderIndex = new AtomicInteger(0);
        List<XpdLevelPO> levelList = BeanCopierUtil.convertList(
                inDto.getLevelRuleList(),
                levelRule -> {
                    levelRule.setOrderIndex(orderIndex.incrementAndGet());
                    levelRule.setFormula(JSON.toJSONString(levelRule.getSpRuleBean()));
                    return XpdPOFactory.buildXpdLevel(orgId, userId, xpd.getId(), xpdRule.getId(), xpdRuleConf.getGridId(), levelRule);
                });
        combDto.setNewLevelList(levelList);

        getSelfProxy().saveXpdRuleInfoTransaction(orgId, userId, combDto);

        return new ErrorInfo();
    }

    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void saveXpdRuleInfoTransaction(String orgId, String userId, XpdRuleCombDto combDto) {

        if (Objects.nonNull(combDto.getUpXpdRule())) {
            xpdRuleMapper.insertOrUpdate(combDto.getUpXpdRule());
            xpdRuleCalcIndicatorMapper.deleteByXpdRuleId(orgId, combDto.getUpXpdRule().getId(), userId);
            xpdRuleCalcDimMapper.deleteByXpdRuleId(orgId, combDto.getUpXpdRule().getId(), userId);
            xpdLevelMapper.deleteByXpdRuleId(orgId, combDto.getUpXpdRule().getId(), userId);
        }
        if (CollectionUtils.isNotEmpty(combDto.getNewRuleCalcIndicatorList())) {
            xpdRuleCalcIndicatorMapper.batchInsert(combDto.getNewRuleCalcIndicatorList());
        }
        if (CollectionUtils.isNotEmpty(combDto.getNewRuleCalcDimList())) {
            xpdRuleCalcDimMapper.batchInsert(combDto.getNewRuleCalcDimList());
        }
        if (CollectionUtils.isNotEmpty(combDto.getNewLevelList())) {
            xpdLevelMapper.batchInsert(combDto.getNewLevelList());
        }
    }

    /**
     * 获取项目所有的盘点维度
     *
     * @param xpdId 项目ID
     * @return list
     */
    public List<XpdDimDto> listAllDimsByXpdId(String orgId, String xpdId) {

        XpdPO xpd = checkXpdExist(xpdId);
        List<XpdDimPO> xpdDims = xpdDimMapper.listByXpdId(orgId, xpdId);
        List<String> sdDimIds = StreamUtil.mapList(xpdDims, XpdDimPO::getSdDimId);
        Map<String, ModelBase4Facade> dimInfoMap = spsdAclService.getDimInfoMap(orgId, xpd.getModelId(), sdDimIds);
        return BeanCopierUtil.convertList(xpdDims, xpdDim -> {
            XpdDimDto dimDto = new XpdDimDto();
            dimDto.setSdDimId(xpdDim.getSdDimId());
            dimDto.setDimName(dimInfoMap.getOrDefault(xpdDim.getSdDimId(), new ModelBase4Facade()).getDmName());
            return dimDto;
        });
    }

    /**
     * 获取当前维度的下一级子维度
     *
     * @param xpdId 项目ID
     * @return list
     */
    public List<XpdDimDto> listNextDimsBySdDimId(String orgId, String xpdId, String sdDimId) {

        XpdPO xpd = checkXpdExist(xpdId);
        List<ModelBase4Facade> dimInfos = spsdAclService.getDimInfoList(orgId, xpd.getModelId(), Lists.newArrayList(sdDimId));
        Map<String, ModelBase4Facade> dimInfoMap = StreamUtil.list2map(dimInfos, ModelBase4Facade::getDmId);
        Map<String, List<ModelBase4Facade>> dimInfoParentIdMap = dimInfos.stream().collect(Collectors.groupingBy(ModelBase4Facade::getParentId));
        if (!dimInfoMap.containsKey(sdDimId) || !dimInfoParentIdMap.containsKey(dimInfoMap.get(sdDimId).getId())) {
            return new ArrayList<>();
        }
        List<ModelBase4Facade> subDims = dimInfoParentIdMap.get(dimInfoMap.get(sdDimId).getId());
        return BeanCopierUtil.convertList(subDims, dim -> {
            XpdDimDto dimDto = new XpdDimDto();
            dimDto.setSdDimId(dim.getDmId());
            dimDto.setDimName(dim.getDmName());
            return dimDto;
        });
    }

    /**
     * 获取项目所有的指标
     *
     * @param xpdId 项目ID
     * @return list
     */
    public List<IndicatorInfoDto> listAllIndicatorsByXpdId(String orgId, String xpdId, String key) {

        XpdPO xpd = checkXpdExist(xpdId);
        List<IndicatorDto> lastIndicators = spsdAclService.getLastIndicators(orgId, xpd.getModelId());
        if (StringUtils.isNotEmpty(key)) {
            lastIndicators = StreamUtil.filterList(lastIndicators, indicator -> {
                if (indicator.getItemType() == 0 && StringUtils.isNotEmpty(indicator.getItemValue()) && indicator.getItemValue().contains(key)) {
                    return true;
                }
                return indicator.getItemType() != 0 && StringUtils.isNotEmpty(indicator.getIndicatorName()) && indicator.getIndicatorName().contains(key);
            });
        }
        return BeanCopierUtil.convertList(lastIndicators, indicator -> {
            IndicatorInfoDto indicatorDto = new IndicatorInfoDto();
            indicatorDto.setSdDimId(indicator.getDmId());
            indicatorDto.setSdIndicatorId(indicator.getItemId());
            indicatorDto.setIndicatorName(indicator.getItemType() == 0 ? indicator.getItemValue() : indicator.getIndicatorName());
            return indicatorDto;
        });
    }

    /**
     * 计算指标分
     *
     * @return 指标分
     */
    public BigDecimal getIndicatorScore(String orgId, String xpdId, XpdRuleCalcDto calcDto) {

        XpdPO xpd = checkXpdExist(xpdId);
        XpdIndicatorCalcBean calcBean = new XpdIndicatorCalcBean();
        calcBean.setSdIndicatorId(calcDto.getSdDtos().get(0).getSdId());
        calcBean.setCalcMethod(calcDto.getCalcMethod());
        calcBean.setWeight(calcDto.getWeight());

        List<XpdImportPO> importIndicators = xpdImportMapper.findByXpdIdAndImportType(orgId, xpd.getId(), XpdImportTypeEnum.DIM_INDICATOR.getCode(), false);
        List<XpdDimRuleCalcRefDto> refList = convertRefIds(calcDto.getRefIds(), importIndicators);
        calcBean.setRefList(refList);

        calcIndicatorWeightedTotalScoreByScoreSystem(xpd, ListUtil.singletonList(calcBean));

        return calcBean.getTotalScore();
    }

    /**
     * 计算维度分
     *
     * @return 指标分
     */
    public BigDecimal getDimScore(String orgId, String xpdId, XpdRuleCalcDto calcDto) {

        String sdDimId = calcDto.getSdDtos().get(0).getSdId();
        XpdPO xpd = checkXpdExist(xpdId);

        List<XpdDimRulePO> dimRules = xpdDimRuleMapper.listByXpdId(orgId, xpdId);
        Map<String, XpdDimRulePO> dimRuleMap = getDimRuleMap(dimRules);
        Map<String, List<XpdDimRulePO>> dimRuleParentIdMap = getDimRuleParentIdMap(dimRules);

        // 维度没有配置规则
        if (!dimRuleMap.containsKey(sdDimId)) {
            return null;
        }

        XpdDimPO xpdDim = xpdDimMapper.selectByXpdIdAndSdDimId(orgId, xpdId, sdDimId);
        if (xpdDim != null && DimTypeEnum.isPerf(xpdDim.getDimType())) {
            XpdDimRulePO dimRule = dimRuleMap.get(sdDimId);
            return calcDimPerfWeightedTotalScore(xpd, dimRule.getAomActId(), calcDto.getWeight());
        }

        // 配置了指标
        List<XpdDimRuleCalcPO> allDimRuleCalcs = xpdDimRuleCalcMapper.listByXpdId(orgId, xpdId);
        Map<String, List<XpdDimRuleCalcPO>> dimRuleCalcsMap = allDimRuleCalcs.stream().collect(Collectors.groupingBy(XpdDimRuleCalcPO::getSdDimId));

        Map<String, BigDecimal> totalScoreMap = new HashMap<>();
        XpdDimCalcDto dimCalcDto = new XpdDimCalcDto();
        dimCalcDto.setSdDimId(sdDimId);
        dimCalcDto.setWeight(calcDto.getWeight());
        calcDimValue(dimCalcDto, xpd, dimRuleMap, dimRuleParentIdMap, dimRuleCalcsMap, totalScoreMap);
        return totalScoreMap.get(sdDimId);
    }

    /**
     * 保存维度规则-绩效维度
     *
     * @param orgId  机构ID
     * @param userId 登录人ID
     * @param inDto  维度规则入参
     */
    public ErrorInfo upXpdDimRulePerf(String orgId, String userId, XpdDimRulePerfInDto inDto) {

        ErrorInfo errorInfo = validateInDto(inDto, true);
        if (Objects.nonNull(errorInfo)) {
            return errorInfo;
        }

        XpdPO xpd = checkXpdExist(inDto.getXpdId());
        checkXpdRuleConfExistByXpdId(orgId, inDto.getXpdId());
        XpdDimRulePO dimRule = xpdDimRuleMapper.getByXpdIdAndSdDimId(orgId, inDto.getXpdId(), inDto.getSdDimId());
        // 维度规则没配
        if (dimRule == null) {
            dimRule = new XpdDimRulePO();
            dimRule.setId(ApiUtil.getUuid());
            dimRule.setOrgId(orgId);
            dimRule.setXpdId(inDto.getXpdId());
            dimRule.setParentId(AppConstants.ROOT_ID);
            dimRule.setSdDimId(inDto.getSdDimId());
            dimRule.setCalcRule(NORMAL.getCode());
            dimRule.setEnabled(1);
            dimRule.setDeleted(DeleteEnum.NOT_DELETED.getCode());
            dimRule.setCreateUserId(userId);
            dimRule.setCreateTime(LocalDateTime.now());
        }

        //        // 校验数据来源
        //        ActivityPerfPO activityPerf = getActivityPerf(orgId, inDto.getAomActId());
        //        if (Objects.isNull(activityPerf)) {
        //            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_REF_NOTEXIST, "数据来源的绩效活动不存在");
        //        }
        //
        //        if (!validateCalcType(activityPerf.getEvalType(), inDto.getCalcType())) {
        //            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_CALCTYPE_ERROR, "计算方式设置有误，请检查");
        //        }

        // 绩效活动
        AomActvExtBO perfActv = getPerfActvExt(orgId, xpd, inDto.getAomActId());
        if (perfActv == null) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_REF_NOTEXIST, "数据来源的绩效活动不存在");
        }

        if (Objects.isNull(perfActv.getPerfExtDto()) || !validateCalcType(perfActv.getPerfExtDto().getEvalType(), inDto.getCalcType())) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_CALCTYPE_ERROR, "计算方式设置有误，请检查");
        }

        XpdRuleConfPO xpdRuleConf = checkXpdRuleConfExistByXpdId(orgId, inDto.getXpdId());
        Integer scoreSystem = xpdRuleConf != null ? xpdRuleConf.getScoreSystem() : null;
        errorInfo = xpdDimRuleValidator.validateLevelValue4Perf(perfActv, inDto.getCalcType(), inDto.getLevelType(), inDto.getLevelRuleList(), scoreSystem);
        if (Objects.nonNull(errorInfo)) {
            return errorInfo;
        }

        // 执行保存
        saveXpdDimRulePerf(userId, dimRule, inDto);
        return null;
    }

    private void saveXpdDimRulePerf(String userId, XpdDimRulePO dimRule, XpdDimRulePerfInDto inDto) {
        dimRule.setAomActId(inDto.getAomActId());
        dimRule.setCalcType(inDto.getCalcType());
        dimRule.setRuleDesc(inDto.getRuleDesc());
        if (DimCalcTypeEnum.byPerfScore(inDto.getCalcType())) {
            dimRule.setLevelType(inDto.getLevelType());
            dimRule.setLevelPriority(inDto.getLevelPriority());
        } else {
            if (dimRule.getLevelType() == null) {
                dimRule.setLevelType(DimLevelTypeEnum.getDefault().getCode());
                dimRule.setLevelPriority(DimLevelPriorityEnum.getDefault().getCode());
            }
        }
        List<DimGridLevelRuleDTO> dimGridLevelRules = BeanCopierUtil.convertList(inDto.getLevelRuleList(), levelRule -> {
            DimGridLevelRuleDTO dimGridLevelRuleDTO = new DimGridLevelRuleDTO();
            dimGridLevelRuleDTO.setGridLevelId(levelRule.getGridLevelId());
            dimGridLevelRuleDTO.setBaseValue(levelRule.getBaseValue());
            dimGridLevelRuleDTO.setMatchValues(levelRule.getMatchValues());
            return dimGridLevelRuleDTO;
        });
        dimRule.setLevelRule(JSON.toJSONString(dimGridLevelRules));
        dimRule.setUpdateUserId(userId);
        dimRule.setUpdateTime(LocalDateTime.now());
        xpdDimRuleMapper.insertOrUpdate(dimRule);
    }

    private boolean validateCalcType(Integer evalType, Integer calcType) {
        return (PerfEvalTypeEnum.byScore(evalType) && DimCalcTypeEnum.byPerfScore(calcType))
                || (PerfEvalTypeEnum.byLevel(evalType) && DimCalcTypeEnum.byPerfResult(calcType));
    }

    private ErrorInfo validateInDto(XpdDimRuleBaseInDto inDto, boolean isPerf) {

        boolean flag = (isPerf && DimCalcTypeEnum.isPerf(inDto.getCalcType())) || (!isPerf && !DimCalcTypeEnum.isPerf(inDto.getCalcType()));
        if (!flag) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_CALCTYPE_ERROR, "计算方式设置有误，请检查");
        }
        return null;
    }

    /**
     * 保存维度规则-其他维度
     *
     * @param orgId  机构ID
     * @param userId 登录人ID
     * @param inDto  维度规则入参
     */
    public ErrorInfo upXpdDimRuleOther(String orgId, String userId, XpdDimRuleOtherInDto inDto) {

        String sdDimId = inDto.getSdDimId();
        if (dimIsImport(orgId, inDto.getXpdId(), sdDimId)) {
            throw new ApiException(ExceptionKeys.XPD_RULE_CALC_DIM_IMPORT_ERROR);
        }

        // 初步判断入参是否正确
        ErrorInfo errorInfo = validateInDto(inDto, false);
        if (Objects.nonNull(errorInfo)) {
            return errorInfo;
        }

        XpdPO xpd = checkXpdExist(inDto.getXpdId());
        XpdRuleConfPO xpdRuleConf = checkXpdRuleConfExistByXpdId(orgId, inDto.getXpdId());

        // 校验结果类型
        Integer confResultType = xpdRuleConf.getResultType();
        errorInfo = XpdDimRuleValidator.validateResultType(confResultType, inDto.getResultType());
        if (Objects.nonNull(errorInfo)) {
            return errorInfo;
        }

        List<XpdDimRulePO> dimRules = xpdDimRuleMapper.listByXpdId(orgId, inDto.getXpdId());
        Map<String, XpdDimRulePO> dimRuleMap = getDimRuleMap(dimRules);

        // 校验计算规则
        List<XpdDimRulePO> toDelDimRules = new ArrayList<>();
        List<XpdDimRuleCalcPO> toDelDimRuleCalcs = new ArrayList<>();
        errorInfo = xpdDimRuleValidator.validateCalcListWhenUp(orgId, xpd, sdDimId, confResultType, dimRules, inDto, toDelDimRules, toDelDimRuleCalcs);
        if (Objects.nonNull(errorInfo)) {
            return errorInfo;
        }

        boolean isXpdDim = true;
        XpdDimPO xpdDim = xpdDimMapper.selectByXpdIdAndSdDimId(orgId, inDto.getXpdId(), sdDimId);
        // 不是盘点维度，无需校验分层规则
        if (xpdDim == null) {
            isXpdDim = false;
        }
        if (isXpdDim) {
            boolean checkMaxValue = false;
            BigDecimal maxValue = null;
            // 结果类型：分值 && 分层规则：按固定值
            if (byScore(inDto.getResultType()) && byFixedValue(inDto.getLevelType())) {
                checkMaxValue = true;
                Map<String, List<XpdDimRulePO>> dimRuleParentIdMap = getDimRuleParentIdMap(dimRules);
                maxValue = getTotalScore4DimUp(orgId, sdDimId, xpd, inDto.getCalcType(), dimRuleMap, dimRuleParentIdMap, inDto.getRuleCalcList());

            }
            // 校验分层规则;
            errorInfo = XpdDimRuleValidator.validateLevelValue(inDto.getLevelType(), inDto.getLevelRuleList(), checkMaxValue, maxValue, xpdRuleConf.getScoreSystem());
            if (Objects.nonNull(errorInfo)) {
                return errorInfo;
            }
        }

        XpdDimRuleCombDto combDto = XpdDimRuleCombDto.builder().build();
        // 维度规则没配
        if (!dimRuleMap.containsKey(sdDimId)) {
            XpdDimRuleContextDto contextDto = new XpdDimRuleContextDto();
            contextDto.setRuleDesc(inDto.getRuleDesc());
            BeanCopierUtil.copy(inDto, contextDto);
            combDto = ruleConfComponent.genRuleCombDataWhenAddDim(orgId, userId, sdDimId, xpd, xpdRuleConf, contextDto);
        } else {
            XpdDimRulePO dimRule = dimRuleMap.get(sdDimId);
            combDto = genDimRuleCombDtoOther(orgId, userId, xpd, dimRule, inDto, toDelDimRules, toDelDimRuleCalcs);
        }
        getSelfProxy().saveXpdDimRuleOther(orgId, userId, combDto);

        return null;
    }

    private XpdDimRuleCombDto genDimRuleCombDtoOther(String orgId, String userId, XpdPO xpd,
                                                     XpdDimRulePO dimRule,
                                                     XpdDimRuleOtherInDto inDto,
                                                     List<XpdDimRulePO> toDelDimRules,
                                                     List<XpdDimRuleCalcPO> toDelDimRuleCalcs) {

        XpdDimRuleCombDto combDto = XpdDimRuleCombDto.builder().build();

        dimRule.setCalcType(inDto.getCalcType());
        dimRule.setCalcRule(inDto.getCalcRule());
        dimRule.setFormula(inDto.getFormula());
        dimRule.setFormulaDisplay(inDto.getFormulaDisplay());
        dimRule.setFormulaExpression(CollectionUtils.isNotEmpty(inDto.getFormulaExpressions()) ? JSON.toJSONString(inDto.getFormulaExpressions()) : null);
        dimRule.setFormulaExpCode(CollectionUtils.isNotEmpty(inDto.getFormulaExpCodes()) ? JSON.toJSONString(inDto.getFormulaExpCodes()) : null);
        dimRule.setLevelType(inDto.getLevelType());
        dimRule.setLevelPriority(inDto.getLevelPriority());
        List<DimGridLevelRuleDTO> levelRules = BeanCopierUtil.convertList(inDto.getLevelRuleList(), levelRule -> {
            DimGridLevelRuleDTO dto = new DimGridLevelRuleDTO();
            BeanCopierUtil.copy(levelRule, dto);
            return dto;
        });
        dimRule.setLevelRule(JSON.toJSONString(levelRules));
        dimRule.setRuleDesc(inDto.getRuleDesc());
        dimRule.setUpdateTime(LocalDateTime.now());
        dimRule.setUpdateUserId(userId);
        combDto.setUpDimRule(dimRule);

        List<String> delDimRuleCalcIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(toDelDimRuleCalcs)) {
            delDimRuleCalcIds.addAll(StreamUtil.mapList(toDelDimRuleCalcs, XpdDimRuleCalcPO::getId));
        }
        List<String> delDimRuleIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(toDelDimRules)) {
            delDimRuleIds.addAll(StreamUtil.mapList(toDelDimRules, XpdDimRulePO::getId));
        }
        // 按指标结果计算
        if (DimCalcTypeEnum.byIndicator(inDto.getCalcType())) {
            List<XpdDimRuleCalcPO> dimRuleCalcs = ruleConfComponent.getDimRuleCalcList(orgId, userId, dimRule, inDto.getRuleCalcList());
            combDto.setDelDimRuleId(dimRule.getId());
            combDto.setNewDimRuleCalcList(dimRuleCalcs);
        } else if (DimCalcTypeEnum.bySubDimension(dimRule.getCalcType())) {
            // 按子维度结果计算
            List<XpdDimRuleCalcInDto> ruleCalcList = inDto.getRuleCalcList();
            Map<String, XpdDimRuleCalcInDto> ruleCalcMap = new HashMap<>();
            for (XpdDimRuleCalcInDto calcInDto : ruleCalcList) {
                ruleCalcMap.putIfAbsent(calcInDto.getSdDtos().get(0).getSdId(), calcInDto);
            }
            List<String> sdDimIds = new ArrayList<>(ruleCalcMap.keySet());

            List<XpdDimRulePO> existDimRules = xpdDimRuleMapper.listByXpdId(orgId, dimRule.getXpdId());
            Map<String, XpdDimRulePO> allDimRuleMap = getDimRuleMap(existDimRules);
            Map<String, List<XpdDimRulePO>> allDimRuleParentIdMap = getDimRuleParentIdMap(existDimRules);
            List<XpdDimRulePO> subDimRules = allDimRuleParentIdMap.get(dimRule.getId());
            List<String> existSdDimIds = CollectionUtils.isEmpty(subDimRules) ? new ArrayList<>() : StreamUtil.mapList(subDimRules, XpdDimRulePO::getSdDimId);
            // 待新增
            List<String> newSdDimIds = ListUtil.difference(sdDimIds, existSdDimIds);
            // 待删除
            List<String> delSdDimIds = ListUtil.difference(existSdDimIds, sdDimIds);
            // 待更新
            List<String> upSdDimIds = ListUtil.intersection(sdDimIds, existSdDimIds);
            List<ModelBase4Facade> modelBases = spsdAclService.getDimInfoList(orgId, xpd.getModelId(), newSdDimIds);
            Map<String, ModelBase4Facade> modelBaseMap = StreamUtil.list2map(modelBases, ModelBase4Facade::getDmId);
            Map<String, List<ModelBase4Facade>> modelBaseParentIdMap = modelBases.stream().collect(Collectors.groupingBy(ModelBase4Facade::getParentId));

            // 新增的子维度规则
            List<XpdDimRulePO> newDimRules = Lists.newArrayList();
            List<XpdDimRulePO> upDimRules = Lists.newArrayList();
            for (String newSdDimId : newSdDimIds) {
                if (modelBaseMap.containsKey(newSdDimId)) {
                    XpdDimRuleCalcInDto calcInDto = ruleCalcMap.get(newSdDimId);
                    Integer calcType = null;
                    // 绩效维度
                    if (DimTypeEnum.isPerfName(modelBaseMap.get(newSdDimId).getDmName())) {
                        calcType = DimCalcTypeEnum.PERF_RESULT.getCode();
                    } else if (modelBaseParentIdMap.containsKey(newSdDimId)) {
                        // 有子维度的维度
                        calcType = DimCalcTypeEnum.SUB_DIMENSION.getCode();
                    } else {
                        // 没有子维度的维度
                        calcType = DimCalcTypeEnum.INDICATOR.getCode();
                    }
                    XpdDimRulePO newDimRule = XpdPOFactory.buildSubXpdDimRule(orgId, userId, xpd.getId(), newSdDimId, dimRule.getId(), calcType, dimRule.getResultType(),
                            calcInDto.getWeight());
                    newDimRules.add(newDimRule);
                }
            }
            for (String upSdDimId : upSdDimIds) {
                XpdDimRulePO upDimRule = allDimRuleMap.get(upSdDimId);
                XpdDimRuleCalcInDto calcInDto = ruleCalcMap.get(upSdDimId);
                upDimRule.setWeight(calcInDto.getWeight());
                upDimRule.setUpdateUserId(userId);
                upDimRule.setUpdateTime(LocalDateTime.now());
                upDimRules.add(upDimRule);
            }
            for (String delSdDimId : delSdDimIds) {
                findDelDimRuleIds(delSdDimId, allDimRuleMap, allDimRuleParentIdMap, delDimRuleIds);
            }
            combDto.setNewDimRuleList(newDimRules);
            combDto.setUpDimRuleList(upDimRules);
        }
        combDto.setDelDimRuleCalcIdList(delDimRuleCalcIds);
        combDto.setDelDimRuleIdList(delDimRuleIds);

        return combDto;
    }

    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void saveXpdDimRuleOther(String orgId, String userId, XpdDimRuleCombDto combDto) {
        if (Objects.nonNull(combDto.getUpDimRule())) {
            xpdDimRuleMapper.insertOrUpdate(combDto.getUpDimRule());
        }

        if (CollectionUtils.isNotEmpty(combDto.getDelDimRuleIdList())) {
            xpdDimRuleMapper.deleteByIds(userId, combDto.getDelDimRuleIdList());
        }
        if (StringUtils.isNotEmpty(combDto.getDelDimRuleId())) {
            xpdDimRuleCalcMapper.deleteByDimRuleId(orgId, combDto.getDelDimRuleId(), userId);
        }
        if (CollectionUtils.isNotEmpty(combDto.getDelDimRuleCalcIdList())) {
            xpdDimRuleCalcMapper.deleteByIds(userId, combDto.getDelDimRuleCalcIdList());
        }

        if (CollectionUtils.isNotEmpty(combDto.getNewDimRuleList())) {
            xpdDimRuleMapper.batchInsert(combDto.getNewDimRuleList());
        }
        if (CollectionUtils.isNotEmpty(combDto.getUpDimRuleList())) {
            xpdDimRuleMapper.updateBatch(combDto.getUpDimRuleList());
        }
        if (CollectionUtils.isNotEmpty(combDto.getNewDimRuleCalcList())) {
            xpdDimRuleCalcMapper.batchInsert(combDto.getNewDimRuleCalcList());
        }
    }

    private void findDelDimRuleIds(String delSdDimId,
                                   Map<String, XpdDimRulePO> allDimRuleMap,
                                   Map<String, List<XpdDimRulePO>> existDimRuleParentIdMap,
                                   List<String> delDimRuleIds) {
        XpdDimRulePO delDimRule = allDimRuleMap.get(delSdDimId);
        delDimRuleIds.add(delDimRule.getId());
        if (existDimRuleParentIdMap.containsKey(delDimRule.getId())) {
            List<XpdDimRulePO> subDimRules = existDimRuleParentIdMap.get(delDimRule.getId());
            for (XpdDimRulePO subDimRule : subDimRules) {
                findDelDimRuleIds(subDimRule.getSdDimId(), allDimRuleMap, existDimRuleParentIdMap, delDimRuleIds);
            }
        }
    }

    /**
     * 导入维度的时候，清空维度规则;
     * 只有盘点维度才会删除维度规则
     *
     * @param xpdId   项目ID
     * @param sdDimId 删除的维度ID
     */
    public void deleteDimRuleWhenImport(String orgId, String userId, String xpdId, String sdDimId) {

        XpdDimPO xpdDim = xpdDimMapper.selectByXpdIdAndSdDimId(orgId, xpdId, sdDimId);
        // 如果不是盘点维度就不需要删除
        if (xpdDim == null) {
            return;
        }

        deleteDimRuleRecursive(orgId, userId, xpdId, sdDimId);
    }

    /**
     * 删除指定维度的规则，同时级联删除子维度的规则
     *
     * @param orgId   机构ID
     * @param userId  用户ID
     * @param sdDimId 维度ID
     */
    public void deleteDimRuleRecursive(String orgId, String userId, String xpdId, String sdDimId) {

        List<XpdDimRulePO> dimRuleList = xpdDimRuleMapper.listByXpdId(orgId, xpdId);
        Map<String, XpdDimRulePO> dimRuleMap = getDimRuleMap(dimRuleList);
        // 维度没有配置规则，直接结束
        if (!dimRuleMap.containsKey(sdDimId)) {
            return;
        }
        Map<String, List<XpdDimRulePO>> dimRuleParentIdMap = getDimRuleParentIdMap(dimRuleList);

        List<String> delDimRuleIds = new ArrayList<>();
        findDelDimRuleIds(sdDimId, dimRuleMap, dimRuleParentIdMap, delDimRuleIds);
        if (CollectionUtils.isNotEmpty(delDimRuleIds)) {
            getSelfProxy().deleteDimRulesByDimRuleIds(orgId, userId, delDimRuleIds);
        }
    }

    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void deleteDimRulesByDimRuleIds(String orgId, String userId, List<String> delDimRuleIds) {
        xpdDimRuleMapper.deleteByIds(userId, delDimRuleIds);
        xpdDimRuleCalcMapper.deleteByDimRuleIds(orgId, userId, delDimRuleIds);
    }

    public Map<String, List<ActivityInfoDTO>> listImportsByIndicators(String orgId, String xpdId, List<String> indicatorIds) {

        if (CollectionUtils.isEmpty(indicatorIds)) {
            return new HashMap<>();
        }

        XpdPO xpd = xpdMapper.selectById(xpdId);
        if (xpd == null) {
            return new HashMap<>();
        }
        return xpdAomService.listImportsByIndicators(orgId, xpd, indicatorIds);
    }
}
