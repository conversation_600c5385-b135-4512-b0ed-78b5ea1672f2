package com.yxt.talent.rv.application.xpd.grid;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.ILock;
import com.yxt.common.service.MessageSourceService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.EntityUtil;
import com.yxt.globalfacade.bean.langorg.LangOrg4Batch;
import com.yxt.globalfacade.bean.langorg.LangOrg4Detail;
import com.yxt.globalfacade.bean.langorg.LangOrgKey4Detail;
import com.yxt.spsdk.common.bean.RuleMainBase;
import com.yxt.spsdk.common.bean.SpRuleBean;
import com.yxt.spsdk.common.component.SpRuleService;
import com.yxt.talent.rv.controller.manage.xpd.grid.command.XpdLevelCreateCmd;
import com.yxt.talent.rv.controller.manage.xpd.grid.viewobj.XpdLevelVO;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdLevelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdLevelPO;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslator;
import com.yxt.talent.rv.infrastructure.service.remote.GlobalAclService;
import com.yxt.talent.rv.infrastructure.service.remote.UdpAclService;
import com.yxt.udpfacade.bean.org.OrgBean;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class XpdLevelAppService {

    private final XpdLevelMapper xpdLevelMapper;

    private final ILock lockService;

    private final GlobalAclService globalAclService;

    private final UdpAclService udpAclService;

    private final MessageSourceService messageSourceService;

    private final SpRuleService spRuleService;

    private static final int MAX_LEASE_TIME = 100;

    /**
     * 机构级配置id
     */
    private static String DEFAULT_ID = "00000000-0000-0000-0000-000000000000";

    @Value("${prj.result.label.high:''}")
    private final String highJson;

    @Value("${prj.result.label.mid:''}")
    private final String midJson;

    @Value("${prj.result.label.low:''}")
    private final String lowJson;

    public static final String LK_XPD_LEVEL_CONFIG = "sprv:lk:xpd:level:config:%s";
    private final I18nTranslator i18nTranslator;
    private final AuthService authService;

    /**
     * 保存人才层级
     *
     */
    @DbHintMaster
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void createLevel(String orgId, String userId, XpdLevelCreateCmd xpdLevel) {

        String lockKey = String.format(LK_XPD_LEVEL_CONFIG, orgId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                if (xpdLevelMapper.countByName(orgId, xpdLevel.getLevelName(), DEFAULT_ID, xpdLevel.getGridId(), null) > 0) {
                    throw new ApiException("apis.sptalentrv.xpd.level.name.conflict");
                }
                XpdLevelPO entity = new XpdLevelPO();
                BeanHelper.copyProperties(xpdLevel, entity);
                entity.setId(ApiUtil.getUuid());
                entity.setOrgId(orgId);
                entity.setDeleted(0);
                if (entity.getXpdId() == null || entity.getXpdId().isEmpty()) {
                    entity.setXpdId(DEFAULT_ID);
                }
                if (entity.getXpdRuleId() == null || entity.getXpdRuleId().isEmpty()) {
                    entity.setXpdRuleId(DEFAULT_ID);
                }

                // todo 根据规则bean获取规则可视化名称
                entity.setFormula(BeanHelper.bean2Json(xpdLevel.getSpRuleBean()));
                RuleMainBase ruleMainBase = new RuleMainBase();
                ruleMainBase.setOrgId(orgId);
                ruleMainBase.setBizId(entity.getGridId());
                entity.setFormulaDisplay(spRuleService.calcRuleDisplay(ruleMainBase, xpdLevel.getSpRuleBean()));

                int maxOrder = xpdLevelMapper.queryMaxOrderIndexByOrgId(orgId, DEFAULT_ID, xpdLevel.getGridId());
                entity.setOrderIndex(maxOrder + 1);
                EntityUtil.setCreateInfo(userId, entity);
                entity.setCreateTime(LocalDateTime.now());
                entity.setUpdateTime(LocalDateTime.now());
                xpdLevelMapper.insert(entity);
            } catch (Exception e) {
                log.error(":createLevel error", e);
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
    }




    /**
     * 更新人才层级
     * @param orgId
     * @param userId
     * @param xpdLevel
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void updateLevel(String orgId, String userId, XpdLevelCreateCmd xpdLevel) {

        if (StringUtils.isBlank(xpdLevel.getId())){
            throw new ApiException("apis.sptalentrv.xpd.level.null");
        }

        XpdLevelPO entity = xpdLevelMapper.selectByPrimaryKey(xpdLevel.getId());
        if (entity == null) {
            // 人才层级不存在
            throw new ApiException("apis.sptalentrv.xpd.level.null");
        }
        if ( !entity.getLevelName().equals(xpdLevel.getLevelName()) &&
                xpdLevelMapper.countByName(orgId, xpdLevel.getLevelName(), DEFAULT_ID, xpdLevel.getGridId(),null) > 0) {
            throw new ApiException("apis.sptalentrv.xpd.level.name.conflict");
        }

        BeanHelper.copyProperties(xpdLevel, entity);
        entity.setOrgId(orgId);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUserId(userId);
        EntityUtil.setUpdatedInfo(userId, entity);
        entity.setUpdateTime(LocalDateTime.now());
        if (entity.getXpdId() == null || entity.getXpdId().isEmpty()) {
            entity.setXpdId(DEFAULT_ID);
        }
        if (entity.getXpdRuleId() == null || entity.getXpdRuleId().isEmpty()) {
            entity.setXpdRuleId(DEFAULT_ID);
        }

        // todo 根据规则bean获取规则可视化名称
        entity.setFormula(BeanHelper.bean2Json(xpdLevel.getSpRuleBean()));
        RuleMainBase ruleMainBase = new RuleMainBase();
        ruleMainBase.setOrgId(orgId);
        ruleMainBase.setBizId(entity.getGridId());
        entity.setFormulaDisplay(spRuleService.calcRuleDisplay(ruleMainBase, xpdLevel.getSpRuleBean()));
        xpdLevelMapper.insertOrUpdate(entity);
    }

    public void deleteLevel(String orgId, String userId, String id) {
        XpdLevelPO entity = xpdLevelMapper.selectByPrimaryKey(id);
        if (entity == null) {
            // 人才层级不存在
            throw new ApiException("apis.sptalentrv.xpd.level.null");
        }

        entity.setDeleted(YesOrNo.YES.getValue());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUserId(userId);
        xpdLevelMapper.insertOrUpdate(entity);
    }

    public XpdLevelVO getLevelDetail(String orgId, String id, String lang) {
        XpdLevelPO entity = new XpdLevelPO();
        XpdLevelVO xpdLevelDTO = new XpdLevelVO();
        if (StringUtils.isNotBlank(id)) {
            entity = xpdLevelMapper.selectByPrimaryKey(id);
            if (entity == null) {
                throw new ApiException("apis.sptalentrv.xpd.level.null");
            }
        } else {
            throw new ApiException("apis.sptalentrv.xpd.level.null");
        }

        BeanHelper.copyProperties(entity, xpdLevelDTO);
        xpdLevelDTO.setSpRuleBean(BeanHelper.json2Bean(entity.getFormula(), SpRuleBean.class));

//        //国际化
//        OrgBean orgInfo = udpAclService.getOrgInfo(orgId);
//        boolean enableI18n = orgInfo.getEnableI18n() == YesOrNo.YES.getValue();
//        if (enableI18n) {
//            String tranLang = messageSourceService.getLocalLanguageCode(lang);
//            String levelKey = entity.getLevelNameI18n();
//            String globalName = globalAclService.getTransByLangAndOrgId(levelKey, tranLang, orgId);
//            if (StringUtils.isNotBlank(globalName)) {
//                xpdLevelDTO.setLevelNameI18n(globalName);
//            }
//        }

        return xpdLevelDTO;
    }

    /**
     * 查询人才层级列表
     *
     * @param orgId
     * @return
     */
    public List<XpdLevelVO> getLevelList(String orgId, String gridId, String lang) {
        List<XpdLevelPO> list = xpdLevelMapper.queryAllByOrgId(orgId, DEFAULT_ID, gridId);
        List<XpdLevelVO> result = BeanCopierUtil.convertList(list, XpdLevelPO.class, XpdLevelVO.class);

        result.forEach(
                xpdLevelVO -> {
                    if (StringUtils.isNotBlank(xpdLevelVO.getFormula())) {
                        SpRuleBean spRuleBean = BeanHelper.json2Bean(xpdLevelVO.getFormula(), SpRuleBean.class);
                        xpdLevelVO.setSpRuleBean(spRuleBean);
                    }
                }
        );
        RuleMainBase ruleMainBase = new RuleMainBase();
        ruleMainBase.setOrgId(orgId);
        ruleMainBase.setBizId(gridId);
        ruleMainBase.setLocale(lang);
        spRuleService.calcRuleDisplay(ruleMainBase, result,
            XpdLevelVO::getSpRuleBean, XpdLevelVO::setFormulaDisplay);
        //多语言转换
        OrgBean orgInfo = udpAclService.getOrgInfo(orgId);
        boolean enableI18n = orgInfo.getEnableI18n() == YesOrNo.YES.getValue();
        Map<String, Map<String, String>> keysMap = null;
        String tranLang = messageSourceService.getLocalLanguageCode(lang);
        if (enableI18n) {
            List<String> levelKeys =
                    list.stream().map(XpdLevelPO::getLevelNameI18n).toList();
            LangOrg4Batch langOrg4Batch = new LangOrg4Batch();
            langOrg4Batch.setKeys(levelKeys);
            langOrg4Batch.setOrgId(orgId);
            LangOrg4Detail langOrg4Detail = globalAclService.orgBatchSearchKeys(langOrg4Batch);
            keysMap = langOrg4Detail.getKeys().stream().collect(
                    Collectors.toMap(LangOrgKey4Detail::getLangKey, LangOrgKey4Detail::getTrans));
            log.info("LOG14505:获取对应的翻译：{}", BeanHelper.bean2Json(keysMap));
        }
        Map<String, Map<String, String>> finalKeysMap = keysMap;
        fillGlobal(result, tranLang, finalKeysMap);
        return result;
    }

    private void fillGlobal(
            List<XpdLevelVO> prjResultVOList, String tranLang,
            Map<String, Map<String, String>> finalKeysMap) {
        prjResultVOList.forEach(prjResultVO -> {
            if (null != finalKeysMap && !finalKeysMap.isEmpty()) {
                String levelKey = prjResultVO.getLevelNameI18n();
                Map<String, String> tranMap = finalKeysMap.get(levelKey);
                if(null != tranMap){
                    tranMap.forEach((k, v) -> {
                        if (k.equals(tranLang)) {
                            prjResultVO.setLevelName(v);
                        }
                    });
                }

            }

        });
    }

    /**
     * 根据项目获取人才分层，用于下拉列表使用
     * @param xpdId
     * @param userCacheDetail
     * @return
     */
    public List<XpdLevelVO> getLevelList(String xpdId, UserCacheDetail userCacheDetail) {
        String orgId = userCacheDetail.getOrgId();
        List<XpdLevelPO> xpdLevels = xpdLevelMapper.selectByXpdId(orgId, xpdId);
        List<XpdLevelVO> xpdLevelVOList = BeanCopierUtil.convertList(xpdLevels, XpdLevelPO.class, XpdLevelVO.class);
        i18nTranslator.translate(orgId, userCacheDetail.getLocale(), xpdLevelVOList);
        return xpdLevelVOList;
    }

    /**
     * 获取人才分层
     *
     * @param xpdId
     * @param request
     * @return
     */
    public PagingList<XpdLevelVO> getLevelPage(String xpdId, HttpServletRequest request) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail(request);
        String orgId = userCacheDetail.getOrgId();
        IPage<XpdLevelPO> xpdLevels = xpdLevelMapper.selectByXpdId(ApiUtil.toPage(request), orgId, xpdId);
        PagingList<XpdLevelVO> pagingList = BeanCopierUtil.toPagingList(xpdLevels, XpdLevelPO.class, XpdLevelVO.class);
        i18nTranslator.translate(orgId, userCacheDetail.getLocale(), pagingList.getDatas());
        return pagingList;
    }
}
