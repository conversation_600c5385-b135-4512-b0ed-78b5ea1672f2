package com.yxt.talent.rv.application.calimeet;

import com.yxt.ApplicationQueryService;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetAttendeeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetAttendeePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetPO;
import com.yxt.talent.rv.infrastructure.service.remote.CoreAclService;
import com.yxt.talent.rv.infrastructure.service.remote.MessageAclService;
import com.yxt.talent.rv.infrastructure.service.remote.dto.MessageDTO;
import com.yxt.talent.rv.infrastructure.service.remote.impl.CoreAclServiceImpl;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 校准会消息发送服务
 */
@Slf4j
@RequiredArgsConstructor
@ApplicationQueryService
public class CaliMeetMsgSender {
    /**加入校准会 消息模板 **/
    private static final String TPL_CODE_CALIBRATION_JOIN = "calibration_join";
    /**校准会开始 消息模板 **/
    private static final String TPL_CODE_CALIBRATION_START = "calibration_start";
    private static final String PLACEHOLDER_CALIBRATION_NAME = "{{calibrationName}}";
    private static final String PLACEHOLDER_CALIBRATION_START_TIME = "{{startTime}}";
    private static final String SEND_USER_ID = "talentrv_calibration_auto_msg";

    private final MessageAclService messageAclService;
    private final AppProperties appProperties;
    private final CaliMeetAttendeeMapper caliMeetAttendeeMapper;
    private final CoreAclService coreAclService;

    @Async
    public void sendTemplateMessageForJoinUser(
            String token, UserCacheDetail currentUser, @Nullable CaliMeetPO cm) {
        this.sendTemplateMessage(1, token, currentUser, cm);
    }

    @Async
    public void sendTemplateMessageForMeetingStart(List<CaliMeetPO> cmList) {
        if (CollectionUtils.isEmpty(cmList)) {
            return;
        }
        for (CaliMeetPO cm : cmList) {
            UserCacheDetail currentUser = new UserCacheDetail();
            String domain = currentUser.getDomain();
            currentUser.setOrgId(cm.getOrgId());
            currentUser.setUserId(SEND_USER_ID);
            currentUser.setFullname(SEND_USER_ID);
            currentUser.setDomain(domain);

            this.sendTemplateMessage(2, "", currentUser, cm);
        }
    }

    /**
     * 发送消息 msgType 1 = 校准会加人消息， 2 = 准会开始消息
     */
    public void sendTemplateMessage(
            int msgType, String token, UserCacheDetail currentUser, @Nullable CaliMeetPO cm) {

        if (cm == null) {
            return;
        }

        List<CaliMeetAttendeePO> meetingUsers =
                caliMeetAttendeeMapper.listByOrgIdAndMeetingId(currentUser.getOrgId(), cm.getId());

        Set<String> userIds = meetingUsers.stream()
                .map(CaliMeetAttendeePO::getUserId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        // 构造 messageBean
        MessageDTO messageDTO;
        if (msgType == 1) {
            messageDTO = getMessageBeanForCalibrationJoin(cm.getId(), token, cm.getMeetName(),
                    cm.getMeetTime(), new ArrayList<>(userIds), currentUser);
        } else if (msgType == 2) {
            messageDTO = getMessageBeanForCalibrationStart(cm.getId(), token, cm.getMeetName(),
                    new ArrayList<>(userIds), currentUser);
        } else {
            return;
        }

        // 发送消息
        messageAclService.sendTemplateMessage(messageDTO);
    }

    private MessageDTO getMessageBeanForCalibrationStart(
            String id, String token, String meetingName, List<String> userIds,
            UserCacheDetail currentUser) {
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setTemplateCode(TPL_CODE_CALIBRATION_START);
        messageDTO.setId(id);
        messageDTO.setOrgId(currentUser.getOrgId());
        messageDTO.setUserId(currentUser.getUserId());
        messageDTO.setUserFullName(currentUser.getFullname());
        messageDTO.setDomain(currentUser.getDomain());
        messageDTO.setToken(token);
        messageDTO.setUserIds(userIds);

        HashMap<String, String> placeholderMap = new HashMap<>(8);

        String url = coreAclService.getScanentryURL(currentUser.getOrgId(),
                appProperties.getCaliMeetMsgUrl(), "");

        placeholderMap.put("{{url}}", url);
        placeholderMap.put(PLACEHOLDER_CALIBRATION_NAME, meetingName);
        messageDTO.setPlaceholderMap(placeholderMap);

        return messageDTO;
    }

    private MessageDTO getMessageBeanForCalibrationJoin(
            String id, String token, String meetingName, Date startTime, List<String> userIds,
            UserCacheDetail currentUser) {
        MessageDTO mb =
                getMessageBeanForCalibrationStart(id, token, meetingName, userIds, currentUser);
        mb.setTemplateCode(TPL_CODE_CALIBRATION_JOIN);
        Map<String, String> placeholderMap = mb.getPlaceholderMap();
        String value =
                DateTimeUtil.dateToString(startTime, DateTimeUtil.YYYY_MM_DD_HH_MM);
        placeholderMap.put(PLACEHOLDER_CALIBRATION_START_TIME, value);
        return mb;
    }
}
