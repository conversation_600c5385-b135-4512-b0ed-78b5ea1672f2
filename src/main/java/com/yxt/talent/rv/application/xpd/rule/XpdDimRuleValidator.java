package com.yxt.talent.rv.application.xpd.rule;

import com.alibaba.fastjson.JSON;
import com.vip.vjtools.vjkit.collection.ListUtil;
import com.vip.vjtools.vjkit.collection.MapUtil;
import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.spsdfacade.bean.spsd.IndicatorDto;
import com.yxt.spsdfacade.bean.spsd.ModelBase4Facade;
import com.yxt.spsdk.common.component.ExpressionCalc;
import com.yxt.talent.rv.application.xpd.aom.XpdAomService;
import com.yxt.talent.rv.application.xpd.common.CommonValidator;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.common.enums.FormulaTypeEnum;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.*;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimRuleCalcMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimRuleMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdImportMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRuleCalcPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRulePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 维度规则校验器
 *
 * <AUTHOR>
 * @date 2024/12/18 18:56
 */
@Component
@RequiredArgsConstructor
public class XpdDimRuleValidator {

    private static final Logger log = LoggerFactory.getLogger(XpdDimRuleValidator.class);
    private final SpsdAclService spsdAclService;
    private final XpdAomService xpdAomService;
    private final XpdImportMapper xpdImportMapper;
    private final XpdDimRuleCalcMapper xpdDimRuleCalcMapper;

    private static final BigDecimal HUNDRED = new BigDecimal(100);
    private final XpdDimRuleMapper xpdDimRuleMapper;

    @Nullable
    public static ErrorInfo validateResultType(Integer confResultType, Integer dimResultType) {
        boolean equals = resultTypeEquals(confResultType, dimResultType);
        if (!equals) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_RESULTTYPE_ERROR, "结果类型与全局设置不一致，请重新生成规则");
        }
        return null;
    }

    /**
     * 校验数据来源和高级公式
     * 1、指标的数据来源不能为空
     * 2、高级公式不能有误【高级公式只有在按指标结果计算时需要校验】
     *
     * @return ErrorInfo
     */
    @Nullable
    public ErrorInfo validateRefIdsAndFormula(String aomPrjId, XpdDimRulePO dimRule, List<XpdDimRuleCalcPO> ruleCalcList) {

        // 校验高级公式
        if (DimCalcRuleEnum.byFormula(dimRule.getCalcRule())) {
            if (StringUtils.isEmpty(dimRule.getFormula())) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_FORMULA_EMPTY, "高级公式未配置");
            }
            // 校验高级公式的合法性
            ErrorInfo errorInfo = CommonValidator.checkXpdFormula(dimRule.getFormula());
            if (Objects.nonNull(errorInfo)) {
                return errorInfo;
            }
            return checkFormula(dimRule.getOrgId(), aomPrjId, dimRule.getFormula());
        } else {
            // 校验数据来源
            List<IndicatorBase> indicatorBases = BeanCopierUtil.convertList(ruleCalcList, XpdDimRuleCalcPO.class, IndicatorBase.class);
            if (CollectionUtils.isEmpty(indicatorBases)) {
                return null;
            }
            // 计算规则中指标数据来源为空，提示“指标数据来源为空，请检查”
            ErrorInfo errorInfo = CommonValidator.checkIndicatorRefIdsEmpty(indicatorBases);
            if (Objects.nonNull(errorInfo)) {
                return errorInfo;
            }
            return validateRefIds(dimRule.getOrgId(), aomPrjId, indicatorBases);
        }
    }

    /**
     * 校验高级公式
     *
     * @param formula 高级公式
     * @return ErrorInfo
     */
    @Nullable
    public ErrorInfo checkFormula(String orgId, String aomPrjId, String formula) {
        ExpressionCalc formulaCalc = FormulaTypeEnum.parseExpression(formula);
        if (formulaCalc == null) {
            return null;
        }

        Set<String> aomActvIds = new HashSet<>();
        Set<String> importIds = new HashSet<>();
        formulaCalc.paramChipList().forEach(paramChip -> {
            if (paramChip.getParamBean() == null) {
                return;
            }
            if (paramChip.getBizType() == FormulaTypeEnum.AOM_ACTV_EVAL.getCode()
                    || paramChip.getBizType() == FormulaTypeEnum.AOM_ACTV_OTHER.getCode()) {
                FormulaAomActvParam param = (FormulaAomActvParam) paramChip.getParamBean();
                aomActvIds.add(param.getActvId());
            } else if (paramChip.getBizType() == FormulaTypeEnum.XPD_IMPORT.getCode()) {
                FormulaImportParam param = (FormulaImportParam) paramChip.getParamBean();
                importIds.add(param.getImportId());
            } else if (paramChip.getBizType() == FormulaTypeEnum.XPD_FAST.getCode()) {
                FormulaFastParam param = (FormulaFastParam) paramChip.getParamBean();
                param.getRefIds().forEach(ref -> {
                    if (DimRuleCalcRefEnum.AOM_ACT.getCode() == ref.getRefType()) {
                        aomActvIds.add(ref.getRefId());
                    } else if (DimRuleCalcRefEnum.IMPORT_DATA.getCode() == ref.getRefType()) {
                        importIds.add(ref.getRefId());
                    }
                });
            }
        });
        if (CollectionUtils.isNotEmpty(aomActvIds)) {
            List<ActivityArrangeItem> items = xpdAomService.activityList(orgId, aomPrjId, new ArrayList<>(aomActvIds));
            if (aomActvIds.size() != items.size()) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_FORMULA_ERROR, "高级公式配置有误");
            }
        }

        if (CollectionUtils.isNotEmpty(importIds)) {
            List<XpdImportPO> imports = xpdImportMapper.selectByIds(orgId, new ArrayList<>(importIds));
            if (importIds.size() != imports.size()) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_FORMULA_ERROR, "高级公式配置有误");
            }
        }
        return null;
    }

    @Nullable
    public ErrorInfo validateRefIds(String orgId, String aomPrjId, List<IndicatorBase> indicatorBases) {

        Set<String> actvIds = new HashSet<>();
        Set<String> importIds = new HashSet<>();
        for (IndicatorBase indicatorBase : indicatorBases) {
            List<XpdDimRuleCalcRefDto> refList = JSON.parseArray(indicatorBase.getRefIds(), XpdDimRuleCalcRefDto.class);
            actvIds.addAll(refList.stream().filter(r -> DimRuleCalcRefEnum.AOM_ACT.getCode() == r.getRefType()).map(XpdDimRuleCalcRefDto::getRefId).collect(Collectors.toSet()));
            importIds.addAll(refList.stream().filter(r -> DimRuleCalcRefEnum.IMPORT_DATA.getCode() == r.getRefType()).map(XpdDimRuleCalcRefDto::getRefId).collect(Collectors.toSet()));
        }

        if (CollectionUtils.isNotEmpty(actvIds)) {
            List<ActivityArrangeItem> actvs = xpdAomService.activityList(orgId, aomPrjId, new ArrayList<>(actvIds));
            if (actvIds.size() != actvs.size()) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_INDICATOR_REF_ERROR, "指标数据来源错误，请检查");
            }
        }
        if (CollectionUtils.isNotEmpty(importIds)) {
            List<XpdImportPO> imports = xpdImportMapper.selectByIds(orgId, new ArrayList<>(importIds));
            if (importIds.size() != imports.size()) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_INDICATOR_REF_ERROR, "指标数据来源错误，请检查");
            }
        }
        return null;
    }

    /**
     * 校验分层规则的分值
     * 只有被盘点的维度需要校验
     *
     * @param levelType 分层方式 0：按比例 1：按固定值
     * @return ErrorInfo
     */
    public static ErrorInfo validateLevelValue(Integer levelType,
                                               List<XpdDimLevelRuleDto> levelRuleList,
                                               boolean checkMaxValue, BigDecimal maxValue) {
        return validateLevelValue(levelType, levelRuleList, checkMaxValue, maxValue, null);
    }

    /**
     * 校验分层规则的分值
     * 只有被盘点的维度需要校验
     *
     * @param levelType 分层方式 0：按比例 1：按固定值
     * @param scoreSystem 分制 0：原始分值 1：五分制 2：十分制
     * @return ErrorInfo
     */
    @Nullable
    public static ErrorInfo validateLevelValue(Integer levelType,
                                               List<XpdDimLevelRuleDto> levelRuleList,
                                               boolean checkMaxValue, BigDecimal maxValue,
                                               Integer scoreSystem) {
        if (CollectionUtils.isEmpty(levelRuleList)) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_LEVEL_EMPTY, "分层规则未设置");
        }
        // 分层方式：按比例
        if (XpdLevelTypeEnum.byRatio(levelType)) {
            return CommonValidator.sumPercent(levelRuleList, "baseValue", ExceptionKeys.XPD_RULE_LEVEL_PERCENT_SUM_ERROR, "人员之和必须为100%");
        }

        // 分层方式：按固定值
        for (int i = 1; i < levelRuleList.size(); i++) {
            if (levelRuleList.get(i).getBaseValue().compareTo(levelRuleList.get(i - 1).getBaseValue()) >= 0) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_LEVEL_VALUE_DECREASE_ERROR, "分层规则的值必须递减");
            }
        }
        // 最后一个输入框必须是0，若不是则高亮并提示“最低分层分值必须为0”
        BigDecimal lastLevelValue = levelRuleList.get(levelRuleList.size() - 1).getBaseValue();
        if (BigDecimal.ZERO.compareTo(lastLevelValue) != 0) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_LEVEL_VALUE_MIN_ZERO_ERROR, "最低分层分值必须为0");
        }

        if (checkMaxValue && maxValue != null) {
            List<LevelBase> levelBases = BeanCopierUtil.convertList(levelRuleList, dimLevelRuleDto -> {
                LevelBase levelBase = new LevelBase();
                levelBase.setLevelValue(dimLevelRuleDto.getBaseValue());
                return levelBase;
            });

            return CommonValidator.checkLevelValue(levelBases, ScoreSystemEnum.getMaxScore(scoreSystem, maxValue));
        }
        return null;
    }

    private static boolean resultTypeEquals(Integer confResultType, Integer dimResultType) {
        return (XpdConfResultTypeEnum.byScore(confResultType) && DimResultTypeEnum.byScore(dimResultType))
                || (XpdConfResultTypeEnum.byRate(confResultType) && DimResultTypeEnum.byRate(dimResultType));
    }

    /**
     * 编辑维度规则的校验
     *
     * @param confResultType 全局配置的结果类型
     * @param dimRules       维度规则，有可能为空
     * @return ErrorInfo
     */
    public ErrorInfo validateCalcListWhenUp(String orgId, XpdPO xpd, String sdDimId, Integer confResultType,
                                            List<XpdDimRulePO> dimRules,
                                            XpdDimRuleOtherInDto inDto,
                                            List<XpdDimRulePO> toDelDimRules,
                                            List<XpdDimRuleCalcPO> toDelDimRuleCalcs) {
        Integer calcType = inDto.getCalcType();
        Integer calcRule = inDto.getCalcRule();
        List<XpdDimRuleCalcInDto> ruleCalcList = inDto.getRuleCalcList();
        // 只有 (按子维度结果计算 && 达标率) or (按指标结果计算 && 高级公式) 的情况不需要计算规则
        boolean needCalcs = !(DimCalcTypeEnum.bySubDimension(calcType) && XpdConfResultTypeEnum.byRate(confResultType))
                && !(DimCalcTypeEnum.byIndicator(calcType) && DimCalcRuleEnum.byFormula(calcRule));
        if (needCalcs && CollectionUtils.isEmpty(ruleCalcList)) {
            return new ErrorInfo(RuleErrorCodeEnum.EMPTY.getCode(), ExceptionKeys.XPD_DIM_RULE_CALC_EMPTY, "暂未配置计算规则");
        }

        String curSdDimId = inDto.getSdDimId();
        // 计算方式：按子维度结果计算
        if (DimCalcTypeEnum.bySubDimension(calcType)) {
            return validateCalcListBySubDimWhenUp(orgId, xpd.getId(), xpd.getModelId(), curSdDimId, confResultType, ruleCalcList, toDelDimRuleCalcs);
        }

        // 计算方式：按指标结果计算
        // 高级公式
        if (DimCalcRuleEnum.byFormula(calcRule)) {
            return validateFormulaAndFillDelIds(orgId, xpd.getId(), inDto, dimRules, toDelDimRules, toDelDimRuleCalcs);
        }
        // 快捷配置
        return validateCalcListByIndicatorWhenUp(orgId, xpd, sdDimId, confResultType, ruleCalcList, dimRules, toDelDimRules, toDelDimRuleCalcs);
    }

    @Nullable
    private ErrorInfo validateFormulaAndFillDelIds(String orgId, String xpdId,
                                                   XpdDimRuleOtherInDto inDto,
                                                   List<XpdDimRulePO> dimRules,
                                                   List<XpdDimRulePO> toDelDimRules,
                                                   List<XpdDimRuleCalcPO> toDelDimRuleCalcs) {
        ErrorInfo errorInfo = CommonValidator.checkXpdFormula(inDto.getFormula());
        if (Objects.nonNull(errorInfo)) {
            return errorInfo;
        } else {
            List<XpdDimRuleCalcPO> dimRuleCalcs = xpdDimRuleCalcMapper.listByXpdId(orgId, xpdId);
            Set<String> selfAndOffspringSdDimIds = getSelfAndOffspringSdDimIds(inDto.getSdDimId(), dimRules);
            fillDelIds(inDto.getSdDimId(), selfAndOffspringSdDimIds, dimRules, dimRuleCalcs, toDelDimRules, toDelDimRuleCalcs);
            return null;
        }
    }

    private void fillDelIds(String sdDimId,
                            Collection<String> sdDimIds,
                            List<XpdDimRulePO> dimRules,
                            List<XpdDimRuleCalcPO> dimRuleCalcs,
                            List<XpdDimRulePO> toDelDimRules,
                            List<XpdDimRuleCalcPO> toDelDimRuleCalcs) {
        for (XpdDimRulePO dimRule : dimRules) {
            //  !Objects.equals(sdDimId, dimRule.getSdDimId())：维度本身的规则配置不要删除
            if (sdDimIds.contains(dimRule.getSdDimId()) && !Objects.equals(sdDimId, dimRule.getSdDimId())) {
                toDelDimRules.add(dimRule);
            }
        }

        for (XpdDimRuleCalcPO dimRuleCalc : dimRuleCalcs) {
            if (sdDimIds.contains(dimRuleCalc.getSdDimId()) && !Objects.equals(sdDimId, dimRuleCalc.getSdDimId())) {
                toDelDimRuleCalcs.add(dimRuleCalc);
            }
        }
    }

    private boolean validateIndicatorCalcMethod(Integer resultType, Integer calcMethod) {
        return (XpdConfResultTypeEnum.byScore(resultType) && DimCalcMethodEnum.byScore(calcMethod))
                || (XpdConfResultTypeEnum.byRate(resultType) && DimCalcMethodEnum.byRatio(calcMethod));
    }

    /**
     * 计算方式：按子维度结果计算
     */
    @Nullable
    private ErrorInfo validateCalcListBySubDimWhenUp(String orgId, String xpdId, String modelId, String curSdDimId,
                                                     Integer confResultType,
                                                     List<XpdDimRuleCalcInDto> ruleCalcList,
                                                     List<XpdDimRuleCalcPO> toDelDimRuleCalcs) {
        List<ModelBase4Facade> dimInfoList = spsdAclService.getDimInfoList(orgId, modelId, ListUtil.singletonList(curSdDimId));
        Map<String, ModelBase4Facade> dimInfoMap = StreamUtil.list2map(dimInfoList, ModelBase4Facade::getDmId);
        Map<String, List<ModelBase4Facade>> dimParentIdMap = dimInfoList.stream().collect(Collectors.groupingBy(ModelBase4Facade::getParentId));
        // 当前维度的直接子维度
        if (!dimInfoMap.containsKey(curSdDimId)) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_NOT_EXIST, "当前维度不存在");
        }
        ModelBase4Facade dimInfo = dimInfoMap.get(curSdDimId);
        List<ModelBase4Facade> directSubDims = dimParentIdMap.get(dimInfo.getId());
        List<String> sdDimIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(directSubDims)) {
            sdDimIds = StreamUtil.mapList(directSubDims, ModelBase4Facade::getDmId);
        }
        BigDecimal sum = BigDecimal.ZERO;
        for (XpdDimRuleCalcInDto calcDto : ruleCalcList) {
            // 校验维度合法性
            if (!sdDimIds.contains(calcDto.getSdDtos().get(0).getSdId())) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_SUBDIM_ERROR, "维度选择有误");
            }
            if (XpdConfResultTypeEnum.byScore(confResultType)) {
                if (calcDto.getWeight() == null) {
                    return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_SUBDIM_WEIGHT_EMPTY, "维度权重未设置");
                }
                sum = sum.add(calcDto.getWeight());
            }
        }
        // 结果类型：分值 校验权重
        if (XpdConfResultTypeEnum.byScore(confResultType) && (HUNDRED.compareTo(sum) != 0)) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_CALC_WEIGHT_SUM_ERROR, "权重之和必须为100%");
        }
        XpdDimRulePO xpdDimRulePO = xpdDimRuleMapper.getByXpdIdAndSdDimId(orgId, xpdId, curSdDimId);
        // 之前是按照指标结果计算，现在修改为按照子维度计算之后，需要删除之前的维度结果计算的规则
        if (xpdDimRulePO != null && DimCalcTypeEnum.byIndicator(xpdDimRulePO.getCalcType())) {
            List<XpdDimRuleCalcPO> dimRuleCalcs = xpdDimRuleCalcMapper.listByXpdIdAndSdDimId(orgId, xpdId, curSdDimId);
            toDelDimRuleCalcs.addAll(dimRuleCalcs);
        }
        return null;
    }

    /**
     * 计算方式：按指标结果计算
     */
    @Nullable
    private ErrorInfo validateCalcListByIndicatorWhenUp(String orgId, XpdPO xpd, String sdDimId,
                                                        Integer confResultType,
                                                        List<XpdDimRuleCalcInDto> ruleCalcList,
                                                        List<XpdDimRulePO> dimRules,
                                                        List<XpdDimRulePO> toDelDimRules,
                                                        List<XpdDimRuleCalcPO> toDelDimRuleCalcs) {
        String xpdId = xpd.getId();
        String modelId = xpd.getModelId();
        List<XpdDimRuleCalcPO> dimRuleCalcs = xpdDimRuleCalcMapper.listByXpdId(orgId, xpdId);
        Set<String> excludeSdDimIds = getSelfAndOffspringSdDimIds(sdDimId, dimRules);
        fillDelIds(sdDimId, excludeSdDimIds, dimRules, dimRuleCalcs, toDelDimRules, toDelDimRuleCalcs);

        // 项目中已使用的指标ID，要排除子维度的指标
        Set<String> sdIds = dimRuleCalcs.stream().filter(r -> !excludeSdDimIds.contains(r.getSdDimId())).map(XpdDimRuleCalcPO::getSdIndicatorId).collect(Collectors.toSet());
        List<IndicatorDto> lastIndicators = spsdAclService.getLastIndicators(orgId, modelId);
        Map<String, IndicatorDto> lastIndicatorMap = StreamUtil.list2map(lastIndicators, IndicatorDto::getItemId);
        BigDecimal sum = BigDecimal.ZERO;
        for (XpdDimRuleCalcInDto calcDto : ruleCalcList) {
            String indicatorId = calcDto.getSdDtos().get(0).getSdId();
            if (sdIds.contains(indicatorId)) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_INDICATOR_DUPLICATE, "指标不能重复选择");
            }
            sdIds.add(indicatorId);

            // 校验指标合法性
            if (!lastIndicatorMap.containsKey(calcDto.getSdDtos().get(0).getSdId())) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_INDICATOR_NOTEXIST, "指标不存在");
            }
            // 校验数据来源合法性
            ErrorInfo errorInfo = checkRefIds(orgId, indicatorId, xpd, calcDto.getRefIds());
            if (Objects.nonNull(errorInfo)) {
                return errorInfo;
            }
            // 校验计算逻辑
            if (calcDto.getCalcMethod() == null) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_INDICATOR_CALCMETOD_EMPTY, "计算逻辑为空，请检查");
            }
            if (!validateIndicatorCalcMethod(confResultType, calcDto.getCalcMethod())) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_INDICATOR_CALCMETOD_ERROR, "计算逻辑有误");
            }
            if (XpdConfResultTypeEnum.byScore(confResultType)) {
                if (calcDto.getWeight() == null) {
                    return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_INDICATOR_WIGHT_EMPTY, "指标权重未设置");
                }
                sum = sum.add(calcDto.getWeight());
            }
        }
        if (XpdConfResultTypeEnum.byScore(confResultType) && HUNDRED.compareTo(sum) != 0) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_CALC_WEIGHT_SUM_ERROR, "权重之和必须为100%");
        }
        return null;
    }

    @Nullable
    public ErrorInfo checkRefIds(String orgId, String indicatorId, XpdPO xpd, List<String> refIds) {
        if (CollectionUtils.isEmpty(refIds)) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_INDICATOR_REF_EMPTY, "指标数据来源为空，请检查");
        }

        // 校验数据来源
        List<String> actvIds = new ArrayList<>();
        // 活动
        Map<String, List<ActivityInfoDTO>> aomActvMap = xpdAomService.listActivityByIndicators(orgId, xpd.getId(), Lists.newArrayList(indicatorId));
        // 导入
        Map<String, List<ActivityInfoDTO>> importMap = xpdAomService.listImportsByIndicators(orgId, xpd, Lists.newArrayList(indicatorId));
        if (!MapUtil.isEmpty(aomActvMap) && aomActvMap.containsKey(indicatorId)) {
            actvIds.addAll(StreamUtil.mapList(aomActvMap.get(indicatorId), ActivityInfoDTO::getActvId));
        }
        if (!MapUtil.isEmpty(importMap) && importMap.containsKey(indicatorId)) {
            actvIds.addAll(StreamUtil.mapList(importMap.get(indicatorId), ActivityInfoDTO::getActvId));
        }
        for (String refId : refIds) {
            if (!StringUtils.equals(String.valueOf(DimRuleCalcRefEnum.PRI_PROFILE.getCode()), refId) && !actvIds.contains(refId)) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_INDICATOR_REF_ERROR, "指标数据来源错误，请检查");
            }
        }

        return null;
    }

    /**
     * 获取当前维度及其规则下所有的子维度
     *
     * @param sdDimId
     * @param dimRules
     * @return 当前维度及其规则下所有的子维度
     */
    public Set<String> getSelfAndOffspringSdDimIds(String sdDimId, List<XpdDimRulePO> dimRules) {
        if (CollectionUtils.isEmpty(dimRules)) {
            return new HashSet<>();
        }
        XpdDimRulePO root = null;
        for (XpdDimRulePO dimRule : dimRules) {
            if (Objects.equals(sdDimId, dimRule.getSdDimId())) {
                root = dimRule;
                break;
            }
        }

        Set<String> subDimIds = new HashSet<>();
        Map<String, List<XpdDimRulePO>> dimRuleParentIdMap = dimRules.stream().collect(Collectors.groupingBy(XpdDimRulePO::getParentId));
        if (root != null) {
            subDimIds.addAll(getSdDimIds(root, dimRuleParentIdMap));
        }
        return subDimIds;
    }

    private Set<String> getSdDimIds(XpdDimRulePO cur, Map<String, List<XpdDimRulePO>> dimRuleParentIdMap) {
        Set<String> sdDimIds = new HashSet<>();
        sdDimIds.add(cur.getSdDimId());
        if (dimRuleParentIdMap.containsKey(cur.getId())) {
            List<XpdDimRulePO> dimRules = dimRuleParentIdMap.get(cur.getId());
            for (XpdDimRulePO dimRule : dimRules) {
                sdDimIds.addAll(getSdDimIds(dimRule, dimRuleParentIdMap));
            }
        }
        return sdDimIds;
    }

    /**
     * 校验绩效维度的分层规则
     *
     * @param perfActvExt   绩效活动
     * @param calcType      计算方式 2:按绩效结果结算 3:按绩效得分计算
     * @param levelType     人员分层方式 0:按比例 1:按固定值
     * @param levelRuleList 分层规则
     * @return ErrorInfo 错误信息
     */
    @Nullable
    public ErrorInfo validateLevelValue4Perf(AomActvExtBO perfActvExt,
                                             Integer calcType,
                                             Integer levelType,
                                             List<XpdDimLevelRuleDto> levelRuleList) {
        return validateLevelValue4Perf(perfActvExt, calcType, levelType, levelRuleList, null);
    }

    /**
     * 校验绩效维度的分层规则
     *
     * @param perfActvExt   绩效活动
     * @param calcType      计算方式 2:按绩效结果结算 3:按绩效得分计算
     * @param levelType     人员分层方式 0:按比例 1:按固定值
     * @param levelRuleList 分层规则
     * @param scoreSystem   分制 0：原始分值 1：五分制 2：十分制
     * @return ErrorInfo 错误信息
     */
    @Nullable
    public ErrorInfo validateLevelValue4Perf(AomActvExtBO perfActvExt,
                                             Integer calcType,
                                             Integer levelType,
                                             List<XpdDimLevelRuleDto> levelRuleList,
                                             Integer scoreSystem) {

        // 计算方式:按绩效结果结算
        if (DimCalcTypeEnum.byPerfResult(calcType)) {
            // 校验分层规则的绩效结果
            for (XpdDimLevelRuleDto levelRuleDto : levelRuleList) {
                if (CollectionUtils.isEmpty(levelRuleDto.getMatchValues())) {
                    return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_LEVEL_RESULT_EMPTY, "分层规则的绩效结果为空");
                }
            }
        } else {
            if (CollectionUtils.isEmpty(perfActvExt.getIndicators())) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_ACTV_PERF_NOTEXIST, "绩效活动或指标不存在");
            }
            // 绩效活动的最高分
            AomActvIndicatorDto actvIndicatorDto = perfActvExt.getIndicators().get(0);
            return validateLevelValue(levelType, levelRuleList, true, actvIndicatorDto.getTotalScore(), scoreSystem);
        }
        return null;
    }
}
