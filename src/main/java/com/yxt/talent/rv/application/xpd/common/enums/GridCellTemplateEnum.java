package com.yxt.talent.rv.application.xpd.common.enums;

/**
 * @Author: geyan
 * @Date: 12/12/24 15:24
 * @Description:
 **/
public enum GridCellTemplateEnum {
    FOUR_CELL_1(GridTypeEnum.FOUR_GRID, "#FFF1F1", 1, 1, 1),
    FOUR_CELL_2(GridTypeEnum.FOUR_GRID, "#FFF6ED", 2, 1, 2),
    FOUR_CELL_3(GridTypeEnum.FOUR_GRID, "#FFF6ED", 3, 2, 1),
    FOUR_CELL_4(GridTypeEnum.FOUR_GRID, "#EFF1FC", 4, 2, 2),

    NINE_CELL_1(GridTypeEnum.NINE_GRID, "#FFF1F1", 1, 1, 1),
    NINE_CELL_2(GridTypeEnum.NINE_GRID, "#FFF1F1", 2, 1, 2),
    NINE_CELL_3(GridTypeEnum.NINE_GRID, "#FFF1F1", 3, 2, 1),
    NINE_CELL_4(GridTypeEnum.NINE_GRID, "#FFF6ED", 4, 1, 3),
    NINE_CELL_5(GridTypeEnum.NINE_GRID, "#FFF6ED", 5, 2, 2),
    NINE_CELL_6(GridTypeEnum.NINE_GRID, "#FFF6ED", 6, 3, 1),
    NINE_CELL_7(GridTypeEnum.NINE_GRID, "#EFF1FC", 7, 2, 3),
    NINE_CELL_8(GridTypeEnum.NINE_GRID, "#EFF1FC", 8, 3, 2),
    NINE_CELL_9(GridTypeEnum.NINE_GRID, "#EFF1FC", 9, 3, 3),

    SIXTEEN_CELL_1(GridTypeEnum.SIXTEEN_GRID, "#FFF1F1", 1, 1, 1),
    SIXTEEN_CELL_2(GridTypeEnum.SIXTEEN_GRID, "#FFF1F1", 2, 1, 2),
    SIXTEEN_CELL_3(GridTypeEnum.SIXTEEN_GRID, "#FFF1F1", 3, 2, 1),
    SIXTEEN_CELL_4(GridTypeEnum.SIXTEEN_GRID, "#FFF6ED", 4, 1, 3),
    SIXTEEN_CELL_5(GridTypeEnum.SIXTEEN_GRID, "#FFF6ED", 5, 2, 2),
    SIXTEEN_CELL_6(GridTypeEnum.SIXTEEN_GRID, "#FFF6ED", 6, 3, 1),
    SIXTEEN_CELL_7(GridTypeEnum.SIXTEEN_GRID, "#EFF1FC", 7, 1, 4),
    SIXTEEN_CELL_8(GridTypeEnum.SIXTEEN_GRID, "#EFF1FC", 8, 2,3),
    SIXTEEN_CELL_9(GridTypeEnum.SIXTEEN_GRID, "#EFF1FC", 9, 3, 2),
    SIXTEEN_CELL_10(GridTypeEnum.SIXTEEN_GRID, "#EFF1FC", 10, 4, 1),
    SIXTEEN_CELL_11(GridTypeEnum.SIXTEEN_GRID, "#FDEFF7", 11, 2, 4),
    SIXTEEN_CELL_12(GridTypeEnum.SIXTEEN_GRID, "#FDEFF7", 12, 3, 3),
    SIXTEEN_CELL_13(GridTypeEnum.SIXTEEN_GRID, "#FDEFF7", 13, 4, 2),
    SIXTEEN_CELL_14(GridTypeEnum.SIXTEEN_GRID, "#EEF4EB", 14, 3, 4),
    SIXTEEN_CELL_15(GridTypeEnum.SIXTEEN_GRID, "#EEF4EB", 15, 4, 3),
    SIXTEEN_CELL_16(GridTypeEnum.SIXTEEN_GRID, "#EEF4EB", 16, 4, 4),
    ;
    private GridTypeEnum gridType;
    private String cellColor;
    private int cellIndex;
    private int x;
    private int y;

    GridCellTemplateEnum(
        GridTypeEnum gridType, String cellColor, int cellIndex, int x, int y) {
        this.gridType = gridType;
        this.cellColor = cellColor;
        this.cellIndex = cellIndex;
        this.x = x;
        this.y = y;
    }

    public GridTypeEnum getGridType() {
        return gridType;
    }

    public String getCellColor() {
        return cellColor;
    }

    public int getCellIndex() {
        return cellIndex;
    }

    public int getX() {
        return x;
    }

    public int getY() {
        return y;
    }
}
