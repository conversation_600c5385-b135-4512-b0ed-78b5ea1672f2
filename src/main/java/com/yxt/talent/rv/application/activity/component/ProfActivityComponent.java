package com.yxt.talent.rv.application.activity.component;

import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import com.yxt.modelhub.api.bean.dto.SearchDTO;
import com.yxt.modelhub.api.utils.QueryUtil;
import com.yxt.talent.rv.application.activity.ActivityProfileService;
import com.yxt.talent.rv.application.activity.dto.*;
import com.yxt.talent.rv.infrastructure.common.utilities.util.AmDrawerData;
import com.yxt.talent.rv.infrastructure.common.utilities.util.ApassEntityUtils;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.javers.common.collections.Lists;
import org.springframework.stereotype.Component;

import java.time.ZoneId;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/12/25
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ProfActivityComponent {
    private final ActivityProfileService activityProfileService;
    private static final String SEARCH_FIELD_ACTID = "act_id";
    private static final String SEARCH_FIELD_STATUS = "userid.status";
    private static final String SEARCH_FIELD_DEPTID = "userid.deptId";
    private static final String SEARCH_FIELD_POSID = "userid.positionId";

    public DynamicTalentAssessment4Get detail(String orgId, String id) {
        ActProfileDetailVO actProfileDetailVO = activityProfileService.detailById(orgId, id);
        ProfileStatisticsVO profileStatisticsVO = activityProfileService.getProfileStatisticsInfo(orgId, id);
        return convertPlatformVO(actProfileDetailVO, profileStatisticsVO);
    }

    private DynamicTalentAssessment4Get convertPlatformVO(ActProfileDetailVO actProfileDetailVO,
            ProfileStatisticsVO profileStatisticsVO) {
        DynamicTalentAssessment4Get assessment4Get = new DynamicTalentAssessment4Get();
        assessment4Get.setId(actProfileDetailVO.getId());
        assessment4Get.setName(actProfileDetailVO.getName());
        //评估时间类型,1:动态评估,2:定时评估
        //        assessment4Get.setEvaltimetype(actProfileDetailVO.getEvalTimeType() == 1 ? "动态评估" : "定时评估");
        assessment4Get.setEvaltimetype(String.valueOf(actProfileDetailVO.getEvalTimeType()));
        if (Integer.valueOf(2).equals(actProfileDetailVO.getEvalTimeType()) && Objects.nonNull(
                actProfileDetailVO.getEvalTime())) {
            assessment4Get.setEvaltime(
                    Date.from(actProfileDetailVO.getEvalTime().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (Integer.valueOf(1).equals(actProfileDetailVO.getEvalTimeType()) && Objects.nonNull(
                profileStatisticsVO.getEvalTime())) {
            assessment4Get.setEvaltime(
                    Date.from(profileStatisticsVO.getEvalTime().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (CollectionUtils.isNotEmpty(actProfileDetailVO.getIndicators())) {
            List<Object> datas = new ArrayList<>();
            profileStatisticsVO.getIndicatorStrList().forEach(indicatorVO -> {
                AmDrawerData amDrawerData = new AmDrawerData();
                amDrawerData.setId(indicatorVO.getId());
                amDrawerData.setValue(indicatorVO.getId());
                amDrawerData.setName(indicatorVO.getName());
                amDrawerData.setLabel(indicatorVO.getName());
                datas.add(amDrawerData);
            });
            assessment4Get.setIndicatorids(ApassEntityUtils.createAmSlDrawer4RespDTOList(datas));
        }
        assessment4Get.setScorequalified(actProfileDetailVO.getQualifiedScore());
        assessment4Get.setScoreunqualified(actProfileDetailVO.getUnqualifiedScore());
        assessment4Get.setActvdesc(actProfileDetailVO.getDescription());
        assessment4Get.setActvnumber(
                profileStatisticsVO.getFinishedUserCount() + "/" + profileStatisticsVO.getTotalUserCount());
        return assessment4Get;
    }

    public PagingList<DynamicAssessmentUsers4Get> pageUserList(String orgId, SearchDTO bean, PageRequest pageRequest) {
        ProfUserListParam param = convertSearchParam(bean);
        PagingList<ActMemberUser> pageRes = activityProfileService.pageQuery(pageRequest, orgId, param.getActvId(),
                param);
        PagingList<DynamicAssessmentUsers4Get> users4GetPagingList = new PagingList<>();
        users4GetPagingList.setPaging(pageRes.getPaging());
        users4GetPagingList.setDatas(new ArrayList<>());
        if (CollectionUtils.isEmpty(pageRes.getDatas())) {
            return users4GetPagingList;
        }
        List<DynamicAssessmentUsers4Get> datas = new ArrayList<>();
        pageRes.getDatas().forEach(item -> {
            DynamicAssessmentUsers4Get tech4Get = convertPageAssessmentUsers4Get(item);
            datas.add(tech4Get);
        });
        users4GetPagingList.setDatas(datas);
        return users4GetPagingList;
    }

    private DynamicAssessmentUsers4Get convertPageAssessmentUsers4Get(ActMemberUser item) {
        DynamicAssessmentUsers4Get users4Get = new DynamicAssessmentUsers4Get();
        users4Get.setName(item.getFullname());
        //        String finsihStatusStr = "--";
        if (item.getResultStatus() != null) {
            users4Get.setStatus(String.valueOf(item.getResultStatus()));
            //            if (item.getResultStatus() == 0) {
            //                finsihStatusStr = "未完成";
            //            }
            //            if (item.getResultStatus() == 1) {
            //                finsihStatusStr = "进行中";
            //            }
            //            if (item.getResultStatus() == 2) {
            //                finsihStatusStr = "已完成";
            //            }
        }

        AmUser4DTO amUser4DTO = new AmUser4DTO();
        List<AmUser4DTO.UserInfo> userInfos = new ArrayList<>();
        AmUser4DTO.UserInfo userInfo = new AmUser4DTO.UserInfo();
        userInfo.setId(item.getUserId());
        userInfo.setName(item.getFullname());
        userInfo.setUserName(item.getUsername());
        AmUser4DTO.AmDept dept = new AmUser4DTO.AmDept();
        AmUser4DTO.DeptInfo deptInfo = new AmUser4DTO.DeptInfo();
        deptInfo.setId(item.getDeptId());
        deptInfo.setName(item.getDeptName());
        dept.setDatas(Lists.asList(deptInfo));
        userInfo.setDeptList(dept);
        AmUser4DTO.AmPosition position = new AmUser4DTO.AmPosition();
        AmUser4DTO.PositionInfo positionInfo = new AmUser4DTO.PositionInfo();
        positionInfo.setId(item.getPositionId());
        positionInfo.setName(item.getPositionName());
        position.setDatas(Lists.asList(positionInfo));
        userInfo.setPositionList(position);
        //用户状态：用于标识当前用户的状态(0-禁用,1-启用)
        if (item.getUserStatus() != null) {
            userInfo.setStatus(String.valueOf(item.getUserStatus()));
            //            if (item.getUserStatus() == 0) {
            //                userInfo.setStatus("禁用");
            //            }
            //            if (item.getUserStatus() == 1) {
            //                userInfo.setStatus("启用");
            //            }
            //            if (item.getUserStatus() == 2) {
            //                userInfo.setStatus("已删除");
            //            }
        }

        userInfos.add(userInfo);
        amUser4DTO.setDatas(userInfos);
        users4Get.setNumberstate(userInfo.getStatus());
        users4Get.setUserid(amUser4DTO);
        return users4Get;
    }

    private ProfUserListParam convertSearchParam(SearchDTO bean) {
        ProfUserListParam param = new ProfUserListParam();
        QueryUtil.Search search = QueryUtil.parse(bean);
        Map<String, String> filterEqMap = search.getFilterEq();
        //        Map<String, List<String>> filterInMap = search.getFilterIn();
        QueryUtil.SearchQuery searchQuery = search.getSearchLike();
        param.setActvId(filterEqMap.get(SEARCH_FIELD_ACTID));
        if (StringUtils.isNotBlank(filterEqMap.get(SEARCH_FIELD_POSID))) {
            param.setPositionIds(Lists.asList(filterEqMap.get(SEARCH_FIELD_POSID)));
        }
        if (StringUtils.isNotBlank(filterEqMap.get(SEARCH_FIELD_DEPTID))) {
            param.setDeptIds(Lists.asList(filterEqMap.get(SEARCH_FIELD_DEPTID)));
        }
        //        if (StringUtils.isNotBlank(filterEqMap.get(SEARCH_FIELD_STATUS))) {
        //            param.setStatus(Integer.valueOf(filterEqMap.get(SEARCH_FIELD_STATUS)));
        //        }
        // 0-禁用 1-启用 2-删除
        if (StringUtils.isNotBlank(filterEqMap.get("numberstate"))) {
            param.setStatus(Integer.valueOf(filterEqMap.get("numberstate")));
        }
        param.setKeyword(searchQuery.getValue());
        param.setKwType(CommonUtil.getKeywordType(search));
        return param;
    }

    public void exportUserList(String orgId, String optUserId, SearchDTO bean) {
        ActProfileUserExportParam param = convertExportParam(bean);
        activityProfileService.exportProfileUserInfo(orgId, param.getActProfileId(), optUserId, param);
    }

    private ActProfileUserExportParam convertExportParam(SearchDTO bean) {
        ActProfileUserExportParam param = new ActProfileUserExportParam();
        QueryUtil.Search search = QueryUtil.parse(bean);
        Map<String, String> filterEqMap = search.getFilterEq();
        //        Map<String, List<String>> filterInMap = search.getFilterIn();
        QueryUtil.SearchQuery searchQuery = search.getSearchLike();
        param.setActProfileId(filterEqMap.get(SEARCH_FIELD_ACTID));
        if (StringUtils.isNotBlank(filterEqMap.get(SEARCH_FIELD_POSID))) {
            param.setPositionIds(Lists.asList(filterEqMap.get(SEARCH_FIELD_POSID)));
        }
        if (StringUtils.isNotBlank(filterEqMap.get(SEARCH_FIELD_DEPTID))) {
            param.setDeptIds(Lists.asList(filterEqMap.get(SEARCH_FIELD_DEPTID)));
        }
        if (StringUtils.isNotBlank(filterEqMap.get(SEARCH_FIELD_STATUS))) {
            param.setUserStatus(Integer.valueOf(filterEqMap.get(SEARCH_FIELD_STATUS)));
        }
        param.setSearchKey(searchQuery.getValue());
        return param;
    }
}
