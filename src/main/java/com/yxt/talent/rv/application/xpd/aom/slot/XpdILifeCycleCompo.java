package com.yxt.talent.rv.application.xpd.aom.slot;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.aom.base.bean.part.cycle.*;
import com.yxt.aom.base.custom.ILifeCycleCompo;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.auditlog.AuditLogContext;
import com.yxt.auditlog.annotation.Auditing;
import com.yxt.auditlog.bean.AuditDetail;
import com.yxt.auditlog.consts.AuditConsts;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.application.calimeet.legacy.CaliMeetAppService;
import com.yxt.talent.rv.application.xpd.result.XpdResultCalcService;
import com.yxt.talent.rv.application.xpd.rule.XpdRuleConfAppService;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.application.xpd.common.dto.ErrorInfo;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.RvActivityMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 盘点项目生命周期自定义接口
 */
@Slf4j
@RequiredArgsConstructor
@Component("iLifeCycleCompo4ProjRcpd")
public class XpdILifeCycleCompo implements ILifeCycleCompo {
    private final XpdMapper xpdMapper;
    private final XpdService xpdService;
    private final XpdRuleConfAppService xpdRuleConfAppService;
    private final XpdResultCalcService xpdResultCalcService;
    private final CaliMeetAppService caliMeetAppService;
    private final RvActivityMapper rvActivityMapper;

    @Override
    @Auditing
    public void release(ActivityReleasePostProcessorThird activityStatusChangeThird) {
        log.info("项目发布,{}", BeanHelper.bean2Json(activityStatusChangeThird, JsonInclude.Include.NON_NULL));
        String orgId = activityStatusChangeThird.getOrgId();
        if (CollectionUtils.isNotEmpty(activityStatusChangeThird.getRefIds())) {
            for (String aomActvId : activityStatusChangeThird.getRefIds()) {
                XpdPO xpdPO = xpdService.findXpdByAomId(orgId, aomActvId);
                if (xpdPO == null) {
                    throw new ApiException(ExceptionKeys.XPD_NOT_EXIST);
                }
                ErrorInfo errorInfo = xpdRuleConfAppService.checkXpdRuleConf4Common(orgId, xpdPO.getId(), false);
                if (errorInfo != null && errorInfo.getCode() != 0) {
                    log.info("盘点规则校验失败,{}", BeanHelper.bean2Json(errorInfo, JsonInclude.Include.NON_NULL));
                    throw new ApiException(ExceptionKeys.XPD_RULE_CONF_CHECK_FAIL);
                }
                String actName = rvActivityMapper.selectAcNameById(orgId, xpdPO.getAomPrjId());
                // 操作日志
                AuditLogContext.get().setAsync(true).setDetailFetcher(() -> {
                    AuditDetail ad = new AuditDetail();
                    ad.setAction(AuditConsts.UPDATE);
                    ad.setModule(AuditLogHelper.Module.PROJECT.getCode());
                    ad.setEntityId(xpdPO.getId());
                    ad.setEntityName("盘点-" + actName + "-发布");
                    return List.of(ad);
                });
            }
        }

    }

    @Override
    public void withdraw(ActivityWithdrawPostProcessorThird activityStatusChangeThird) {

    }

    @Override
    @Auditing
    public void close(ActivityEndPostProcessorThird activityEndPostProcessorThird) {
        log.info("项目结束,{}", BeanHelper.bean2Json(activityEndPostProcessorThird, JsonInclude.Include.NON_NULL));
        // 结束项目，触发项目计算
        String orgId = activityEndPostProcessorThird.getOrgId();
        List<String> xpdIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(activityEndPostProcessorThird.getRefIds())) {
            for (String aomActvId : activityEndPostProcessorThird.getRefIds()) {
                XpdPO xpdPO = xpdService.findXpdByAomId(orgId, aomActvId);
                if (xpdPO == null) {
                    throw new ApiException(ExceptionKeys.XPD_NOT_EXIST);
                }
                xpdIds.add(xpdPO.getId());
                String actvName = rvActivityMapper.selectAcNameById(orgId, xpdPO.getAomPrjId());
                // 操作日志
                AuditLogContext.get().setAsync(true).setDetailFetcher(() -> {
                    AuditDetail ad = new AuditDetail();
                    ad.setAction(AuditConsts.UPDATE);
                    ad.setModule(AuditLogHelper.Module.PROJECT.getCode());
                    ad.setEntityId(xpdPO.getId());
                    ad.setEntityName("盘点-" + actvName+ "-结束");
                    return List.of(ad);
                });
            }
        }

        try {
            if (CollectionUtils.isNotEmpty(xpdIds)) {
                log.info("盘点计算 start,{}", BeanHelper.bean2Json(xpdIds, JsonInclude.Include.NON_NULL));
                xpdIds.forEach(xpdId -> xpdResultCalcService.calcResult(orgId, xpdId));
                log.info("盘点计算 end,{}", BeanHelper.bean2Json(xpdIds, JsonInclude.Include.NON_NULL));
            }
        } catch (Exception e) {
            log.error("盘点计算失败,{}", BeanHelper.bean2Json(xpdIds, JsonInclude.Include.NON_NULL));
        }

    }

    @Override
    public void archive(ActivityArchivePostProcessorThird activityStatusChangeThird) {

    }

    @Override
    @Auditing
    public void del(ActivityDelPostProcessorThird activityStatusChangeThird) {
        String orgId = activityStatusChangeThird.getOrgId();
        String userId = activityStatusChangeThird.getUserId();
        // 删除项目主表
        if (StringUtils.isNotBlank(activityStatusChangeThird.getOrgId()) &&
            CollectionUtils.isNotEmpty(activityStatusChangeThird.getRefIds())) {

            for (String aomActvId : activityStatusChangeThird.getRefIds()) {
                XpdPO xpdPO = xpdService.findXpdByAomId(orgId, aomActvId);
                if (xpdPO == null) {
                    throw new ApiException(ExceptionKeys.XPD_NOT_EXIST);
                }
                caliMeetAppService.deleteMeetingByProjectId(orgId, userId, xpdPO.getId());

                String actName = rvActivityMapper.selectAcNameById(orgId, xpdPO.getAomPrjId());
                // 操作日志
                AuditLogContext.get().setAsync(true).setDetailFetcher(() -> {
                    AuditDetail ad = new AuditDetail();
                    ad.setAction(AuditConsts.DELETE);
                    ad.setModule(AuditLogHelper.Module.PROJECT.getCode());
                    ad.setEntityId(xpdPO.getId());
                    ad.setEntityName("盘点-" + actName + "-删除");
                    return List.of(ad);
                });
            }

            xpdMapper.deleteByAomPrjIds(activityStatusChangeThird.getOrgId(), activityStatusChangeThird.getRefIds());
        }
    }

}
