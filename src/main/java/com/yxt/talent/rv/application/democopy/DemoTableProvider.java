package com.yxt.talent.rv.application.democopy;

import com.yxt.ApplicationCommandService;
import com.yxt.spevalfacade.bean.enums.DemoIdMapKeyEnum;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.DemoCopyRunner;
import com.yxt.spsdk.democopy.PreSetIdMapRepository;
import com.yxt.talent.rv.application.dmp.calc.DmpCalculator;
import com.yxt.talent.rv.domain.prj.entity.conf.rule.PrjDimRuleExpr;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.persistence.cache.RedisRepo;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetAttendeeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetUserResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.common.AttachmentMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.common.CategoryMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.DmpMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.DmpPermMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.DmpPosMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.conf.DmpConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.conf.DmpMsgConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.rule.DmpRuleLayerMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.rule.DmpRuleMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.rule.DmpRuleScoreDetailMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.rule.DmpRuleScoreMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.task.DmpTaskDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.task.DmpTaskJobMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.task.DmpTaskMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.org.OrgSettingMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfGradeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfPeriodMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfToolMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimSameModelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.result.PrjResultConfigMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.result.PrjResultRuleMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.rule.PrjDimRuleCondMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.rule.PrjDimRuleExprMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.rule.PrjDimRuleMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserImportResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.UserPrjSummaryMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetAttendeePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.CategoryPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.DmpPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.DmpPermPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.DmpPosPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.DmpUserAutoGroupPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.conf.DmpConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.conf.DmpMsgConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.rule.DmpRuleLayerPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.rule.DmpRulePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.rule.DmpRuleScoreDetailPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.rule.DmpRuleScorePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.org.OrgSettingPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjImptTypePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjLabelPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjMgrPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfToolPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimSameModelPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.result.PrjResultConfigPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.result.PrjResultRulePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.rule.PrjDimRuleCondPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.rule.PrjDimRuleExprPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.rule.PrjDimRulePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.train.PrjTrainMapPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserImportResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.UserPrjSummaryPO;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.DYN_SPM_LABEL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPEVAL_DIM_SETTING_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPM_INDICATOR_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPM_LABEL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPRV_DMP_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPRV_DMP_TASK_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPRV_PERF_PERIOD_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPRV_PRJ_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPRV_PRJ_TOOL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_SKILL_ID;

@Slf4j
@RequiredArgsConstructor
@ApplicationCommandService
public class DemoTableProvider {

    private final RedisRepo redisRepo;

    private final PerfGradeMapper perfGradeMapper;

    private final PerfPeriodMapper perfPeriodMapper;

    private final PerfMapper perfMapper;

    private final CaliMeetMapper caliMeetMapper;

    private final CaliMeetUserMapper caliMeetUserMapper;

    private final CaliMeetUserResultMapper caliMeetUserResultMapper;

    private final CategoryMapper categoryMapper;

    private final AttachmentMapper attachmentMapper;

    private final OrgSettingMapper orgSettingMapper;

    private final CaliMeetAttendeeMapper caliMeetAttendeeMapper;

    private final PrjMapper prjMapper;

    private final PrjDimMapper prjDimMapper;

    private final PrjDimConfMapper prjDimConfMapper;

    private final PrjDimConfToolMapper prjDimConfToolMapper;

    private final PrjDimSameModelMapper prjDimSameModelMapper;

    private final PrjMgrMapper prjMgrMapper;

    private final PrjDimRuleCondMapper prjDimRuleCondMapper;

    private final PrjDimRuleMapper prjDimRuleMapper;

    private final PrjDimRuleExprMapper prjDimRuleExprMapper;

    private final PrjResultRuleMapper prjResultRuleMapper;

    private final PrjResultConfigMapper prjResultConfigMapper;

    private final PrjImptTypeMapper prjImptTypeMapper;

    private final PrjUserMapper prjUserMapper;

    private final PrjUserImportResultMapper prjUserImportResultMapper;

    private final PrjUserResultMapper prjUserResultMapper;

    private final PrjLabelMapper prjLabelMapper;

    private final PrjTrainMapMapper prjTrainMapMapper;

    private final UserPrjSummaryMapper userPrjSummaryMapper;
    private final DmpMapper dmpMapper;
    private final DmpConfMapper dmpConfMapper;
    private final DmpRuleMapper dmpRuleMapper;
    private final DmpMsgConfMapper dmpMsgConfMapper;
    private final DmpPermMapper dmpPermMapper;
    private final DmpPosMapper dmpPosMapper;
    private final DmpRuleLayerMapper dmpRuleLayerMapper;
    private final DmpRuleScoreMapper dmpRuleScoreMapper;
    private final DmpRuleScoreDetailMapper dmpRuleScoreDetailMapper;
    private final DmpTaskMapper dmpTaskMapper;
    private final DmpTaskDimMapper dmpTaskDimMapper;
    private final DmpUserMapper dmpUserMapper;
    private final DmpUserAutoGroupMapper dmpUserAutoGroupMapper;
    private final DmpUserDimDetailMapper dmpUserDimDetailMapper;
    private final DmpUserDimResultMapper dmpUserDimResultMapper;
    private final DmpUserResultMapper dmpUserResultMapper;
    private final DmpUserTaskResultMapper dmpUserTaskResultMapper;
    private final DmpTaskJobMapper dmpTaskJobMapper;

    public DemoCopyRunner buildRunner(OrgInit4Mq orgInit) {

        DemoCopyRunner runner = new DemoCopyRunner();
        runner.initCopyCtx(orgInit);

        // 设置被其他服务在复制时依赖的id
        setDependentIdKeys(runner);

        // 盘点基础信息表
        addBase(runner);
        // 绩效管理
        addPerf(runner);
        //校准信息
        addCali(runner);
       // 盘点相关表
        addRvProject(runner);
        //人岗匹配
        addDmp(runner);

        return runner;
    }

    private void setDependentIdKeys(DemoCopyRunner runner) {
        String[] dependentIdKeys = {SPRV_DMP_ID, SPRV_DMP_TASK_ID, SPRV_PRJ_ID};
        runner.setPreSetIdMapKey(new PreSetIdMapRepository() {
            @Override
            public void saveValue(String idMapKey, String subKey, String value) {
                String saveKey = String.format(RedisKeys.CACHE_COPY_ORG_IDMAP, idMapKey);
                redisRepo.setHmValue(
                        saveKey, subKey, value, AppConstants.DEMO_COPY_RUN_DATA_KEEP_TIME);
            }

            @Override
            public String queryValue(String idMapKey, String subKey) {
                String saveKey = String.format(RedisKeys.CACHE_COPY_ORG_IDMAP, idMapKey);
                return redisRepo.getHmValue(saveKey, subKey);
            }

            @Override
            public void delByIdMapKey(String idMapKey) {
                String saveKey = String.format(RedisKeys.CACHE_COPY_ORG_IDMAP, idMapKey);
                redisRepo.removeKey(saveKey);
            }
        }, dependentIdKeys);
    }

    private void addDmp(DemoCopyRunner runner) {
        // 处理一对多字段：即我们一个字段存储了外部系统多个表主键，根据type区分的情况
        runner.addDynamicIdMap(DYN_SPM_LABEL_ID, (target, copyCtx) -> {
            DmpUserDimDetailPO entity = (DmpUserDimDetailPO) target;
            if (DmpCalculator.DimLabelTypeEnum.isLabel(entity.getLabelType())) {
                // 标签
                return SPM_LABEL_ID;
            } else {
                // 指标
                return SPM_INDICATOR_ID;
            }
        });

        // 注册拷贝实体
        runner.addCopyEntity(DmpPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpMapper.selectByOrgId(sourceOrgId);
        }, dmpMapper::batchInsert);

        runner.addCopyEntity(DmpConfPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpConfMapper.selectByOrgId(sourceOrgId);
        }, dmpConfMapper::batchInsert);

        runner.addCopyEntity(DmpRulePO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpRuleMapper.selectByOrgId(sourceOrgId);
        }, dmpRuleMapper::batchInsert);

        runner.addCopyEntity(DmpMsgConfPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpMsgConfMapper.selectByOrgId(sourceOrgId);
        }, dmpMsgConfMapper::batchInsert);

        runner.addCopyEntity(DmpPermPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpPermMapper.selectByOrgId(sourceOrgId);
        }, dmpPermMapper::batchInsert);

        runner.addCopyEntity(DmpPosPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            List<DmpPosPO> dmpPosPos = dmpPosMapper.selectByOrgId(sourceOrgId);
            dmpPosPos.stream().filter(e -> StringUtils.isNotBlank(e.getGradeId())).forEach(e -> {
                String[] sourceGradeIds = e.getGradeId().split(",");
                String targetGradeId = Arrays.stream(sourceGradeIds)
                        .map(sourceGradeId -> runner.getIdMapValue(
                                DemoCopyConstants.UDP_GRADE_ID, sourceGradeId))
                        .collect(Collectors.joining(","));
                e.setGradeId(targetGradeId);
            });
            return dmpPosPos;
        }, dmpPosMapper::batchInsert);

        runner.addCopyEntity(DmpRuleLayerPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpRuleLayerMapper.selectByOrgId(sourceOrgId);
        }, dmpRuleLayerMapper::batchInsert);

        runner.addCopyEntity(DmpRuleScorePO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpRuleScoreMapper.selectByOrgId(sourceOrgId);
        }, dmpRuleScoreMapper::batchInsert);

        runner.addCopyEntity(DmpRuleScoreDetailPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpRuleScoreDetailMapper.selectByOrgId(sourceOrgId);
        }, dmpRuleScoreDetailMapper::batchInsert);

        runner.addCopyEntity(DmpTaskPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpTaskMapper.selectByOrgId(sourceOrgId);
        }, dmpTaskMapper::batchInsert);

        runner.addCopyEntity(DmpTaskDimPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpTaskDimMapper.selectByOrgId(sourceOrgId);
        }, dmpTaskDimMapper::batchInsert);

        runner.addCopyEntity(DmpUserPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpUserMapper.selectByOrgId(sourceOrgId);
        }, dmpUserMapper::batchInsert);

        runner.addCopyEntity(DmpUserAutoGroupPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpUserAutoGroupMapper.selectByOrgId(sourceOrgId);
        }, dmpUserAutoGroupMapper::batchInsert);

        runner.addCopyEntity(DmpUserDimDetailPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpUserDimDetailMapper.selectByOrgId(sourceOrgId);
        }, dmpUserDimDetailMapper::batchInsert);

        runner.addCopyEntity(DmpUserDimResultPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpUserDimResultMapper.selectByOrgId(sourceOrgId);
        }, dmpUserDimResultMapper::batchInsert);

        runner.addCopyEntity(DmpUserResultPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpUserResultMapper.selectByOrgId(sourceOrgId);
        }, dmpUserResultMapper::batchInsert);

        runner.addCopyEntity(DmpUserTaskResultPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return dmpUserTaskResultMapper.selectByOrgId(sourceOrgId);
        }, dmpUserTaskResultMapper::batchInsert);
    }

    private void addRvProject(DemoCopyRunner runner) {

        runner.addDynamicIdMap(SPRV_PRJ_TOOL_ID, (target, copyCtx) -> {
            PrjDimConfToolPO entity = (PrjDimConfToolPO) target;
            if (entity.getToolType() == 1) {
                // 标签
                return SPRV_PERF_PERIOD_ID;
            } else if (entity.getToolType() == 2 || entity.getToolType() == 4) {
                // 指标
                return DemoIdMapKeyEnum.MAP_KEY_SPEVAL_EVALUATION_ID.getIdMapKey();
            } else {
                return null;
            }
        });

        //rv_project
        runner.addCopyEntity(PrjPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjMapper.selectByOrgId(sourceOrgId);
        }, prjMapper::insertList);

        runner.addCopyEntity(PrjDimPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjDimMapper.selectByOrgId(sourceOrgId);
        }, prjDimMapper::insertOrUpdateBatch);

        runner.addCopyEntity(PrjDimConfPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjDimConfMapper.selectByOrgId(sourceOrgId);
        }, prjDimConfMapper::insertOrUpdateBatch);

        runner.addCopyEntity(PrjDimConfToolPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjDimConfToolMapper.selectByOrgId(sourceOrgId);
        }, prjDimConfToolMapper::insertOrUpdateBatch);

        runner.addCopyEntity(PrjDimSameModelPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjDimSameModelMapper.selectByOrgId(sourceOrgId);
        }, prjDimSameModelMapper::insertOrUpdateBatch);

        runner.addCopyEntity(PrjMgrPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjMgrMapper.selectByOrgId(sourceOrgId);
        }, prjMgrMapper::insertOrUpdateBatch);

        // rv_prj_rule
        runner.addCopyEntity(PrjDimRulePO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjDimRuleMapper.selectByOrgId(sourceOrgId);
        }, prjDimRuleMapper::insertOrUpdateBatch);

        // rv_prj_rule_expression
        runner.addCopyEntity(PrjDimRuleExprPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            List<PrjDimRuleExprPO> prjDimRuleExprPOS = dealExpress(runner, sourceOrgId);
            return prjDimRuleExprPOS;
        }, prjDimRuleExprMapper::insertOrUpdateBatch);

        // rv_rule_condition
        runner.addCopyEntity(PrjDimRuleCondPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjDimRuleCondMapper.selectByOrgId(sourceOrgId);
        }, prjDimRuleCondMapper::insertOrUpdateBatch);

        // rv_prj_result_config
        runner.addCopyEntity(PrjResultConfigPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjResultConfigMapper.queryAllByOrgId(sourceOrgId);

        }, list -> {
            prjResultConfigMapper.deleteByOrgId(runner.getCopyContext().getTargetOrgId());
            prjResultConfigMapper.insertOrUpdateBatch(list);
        });

        // rv_prj_result_rule
        runner.addCopyEntity(PrjResultRulePO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjResultRuleMapper.queryByOrgId(sourceOrgId);
        }, prjResultRuleMapper::insertBatch);

        // rv_prj_impt_type
        runner.addCopyEntity(PrjImptTypePO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjImptTypeMapper.selectAllByOrgId(sourceOrgId);
        }, prjImptTypeMapper::insertOrUpdateBatch);

        // rv_prj_user
        runner.addCopyEntity(PrjUserPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjUserMapper.selectByOrgId(sourceOrgId);
        }, prjUserMapper::insertOrUpdateBatch);

        // rv_prj_user_calc_import
        runner.addCopyEntity(PrjUserImportResultPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjUserImportResultMapper.selectByOrgId(sourceOrgId);
        }, prjUserImportResultMapper::insertOrUpdateBatch);

        // rv_prj_user_calc_result
        runner.addCopyEntity(PrjUserResultPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjUserResultMapper.selectByOrgId(sourceOrgId);
        }, prjUserResultMapper::insertOrUpdateBatch);

        // rv_project_label
        runner.addCopyEntity(PrjLabelPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjLabelMapper.selectByOrgId(sourceOrgId);
        }, prjLabelMapper::batchInsert);

        // rv_project_training
        runner.addCopyEntity(PrjTrainMapPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return prjTrainMapMapper.selectByOrgId(sourceOrgId);
        }, prjTrainMapMapper::batchInsert);

        // rv_user_data
        runner.addCopyEntity(UserPrjSummaryPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return userPrjSummaryMapper.selectByOrgId(sourceOrgId);
        }, userPrjSummaryMapper::insertOrUpdateBatch);

    }


    private List<PrjDimRuleExprPO> dealExpress(DemoCopyRunner runner, String sourceOrgId) {
        List<PrjDimRuleExprPO> prjDimRuleExprPOS = prjDimRuleExprMapper.selectByOrgId(sourceOrgId);
        Pattern pattern = Pattern.compile(PrjDimRuleExpr.REGEX);

        for (PrjDimRuleExprPO prjDimRuleExprPO : prjDimRuleExprPOS) {
            String expression = prjDimRuleExprPO.getExpression();
            if (StringUtils.isEmpty(expression)) {
                continue;
            }
            String expressionSubmitList = prjDimRuleExprPO.getExpressionSubmitList();
            Matcher matcher = pattern.matcher(expression);
            List<String> list = new ArrayList<>();
            while (matcher.find()) {
                String data = matcher.group(1);
                // 去掉中括号
                String[] ids= data.split("\\.");
                for (String id : ids) {
                    // 过滤掉 硬编码字符串
                    if (id.length() < 12){
                        continue;
                    }
                    list.add(id);
                }
            }
            // 需要替换的id
            Map<String, String> repMap = new HashMap<>();
            for (String strId : list) {
                log.info("dealExpress strId={}", strId);
                // 测评id
                String targetEvalId = runner.getIdMapValue(DemoIdMapKeyEnum.MAP_KEY_SPEVAL_EVALUATION_ID.getIdMapKey(), strId);
                if (StringUtils.isNotBlank(targetEvalId)) {
                    log.info("dealExpress evalId={}, tar={}", targetEvalId, strId);
                    repMap.put(strId, targetEvalId);
                    continue;
                }
                // 能力id
                String skillId = runner.getIdMapValue(SPSD_SKILL_ID, strId);

                if (StringUtils.isNotBlank(skillId)) {
                    log.info("dealExpress skillId={}, tar={}", targetEvalId, strId);
                    repMap.put(strId, skillId);
                    continue;
                }
                //评估维度id
                String levelId = runner.getIdMapValue(SPEVAL_DIM_SETTING_ID, strId);
                if (StringUtils.isNotBlank(levelId)) {
                    log.info("dealExpress levelId={}, tar={}", targetEvalId, strId);
                    repMap.put(strId, levelId);
                }
            }
            for (String key : repMap.keySet()) {
                String value = repMap.get(key);
                expression = expression.replaceAll(key, value);
                expressionSubmitList = expressionSubmitList.replaceAll(key, value);
            }
            prjDimRuleExprPO.setExpression(expression);
            prjDimRuleExprPO.setExpressionSubmitList(expressionSubmitList);
        }
        return prjDimRuleExprPOS;
    }

    private void addBase(DemoCopyRunner runner) {
        runner.addCopyEntity(CategoryPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return categoryMapper.listByOrgId(sourceOrgId);
        }, categoryMapper::insertList);

        runner.addCopyEntity(OrgSettingPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return orgSettingMapper.listByOrgId(sourceOrgId);
        }, orgSettingMapper::batchInsertOrUpdate);
    }

    // 校准会
    private void addCali(DemoCopyRunner runner) {
        runner.addCopyEntity(CaliMeetPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return caliMeetMapper.listByOrgId(sourceOrgId);

        }, caliMeetMapper::insertOrUpdateBatch);

        runner.addCopyEntity(CaliMeetUserPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return caliMeetUserMapper.listByOrgId(sourceOrgId);
        }, caliMeetUserMapper::insertList);

        runner.addCopyEntity(CaliMeetUserResultPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return caliMeetUserResultMapper.listByOrgId(sourceOrgId);
        }, caliMeetUserResultMapper::insertList);

        runner.addCopyEntity(CaliMeetAttendeePO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return caliMeetAttendeeMapper.listByOrgId(sourceOrgId);
        }, caliMeetAttendeeMapper::insertList);
        // 附件不复制
        /*runner.addCopyEntity(AttachmentPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return attachmentMapper.listByOrgId(sourceOrgId);
        }, attachmentMapper::insertOrUpdateBatch);*/
    }

    private void addPerf(DemoCopyRunner runner) {

        runner.addCopyEntity(PerfGradePO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return perfGradeMapper.selectByOrgIdIncludeDeleted(sourceOrgId);
        }, list ->{
            perfGradeMapper.deleteByOrgId(runner.getCopyContext().getTargetOrgId());
            perfGradeMapper.insertOrUpdateBatch(list);
        });

        runner.addCopyEntity(PerfPeriodPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return perfPeriodMapper.selectByOrgId(sourceOrgId);
        }, perfPeriodMapper::insertOrUpdateBatch);

        runner.addCopyEntity(PerfPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return perfMapper.selectByOrgId(sourceOrgId);
        }, perfMapper::insertOrUpdateBatch);
    }

}
