package com.yxt.talent.rv.application.prj.prj.legacy;

import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.ILock;
import com.yxt.common.service.MessageSourceService;
import com.yxt.common.util.*;
import com.yxt.export.DlcComponent;
import com.yxt.spevalfacade.bean.Evaluation4Get;
import com.yxt.spevalfacade.bean.ReportRecordEvaluationVo;
import com.yxt.spevalfacade.bean.ReportRecordVo;
import com.yxt.spevalfacade.bean.ReportSkillIdScore;
import com.yxt.spevalfacade.bean.evaluation.BindEvaluaFacade;
import com.yxt.spevalfacade.bean.evaluation.Evaluation4User;
import com.yxt.spevalfacade.bean.evaluation.Evaluation4UserResultl;
import com.yxt.sptalentapifacade.bean.SkillModelItem;
import com.yxt.sptalentapifacade.bean.SkillModelItemSearch;
import com.yxt.sptalentapifacade.service.SptalentFacade;
import com.yxt.talent.rv.application.prj.prj.dto.TeamSkillMapDTO;
import com.yxt.talent.rv.application.prj.prj.expt.PrjResultExportStrategy;
import com.yxt.talent.rv.controller.client.bizmgr.dept.query.DeptProjectClientQuery;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptPrjClientVO;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptPrjStatisticsClientVO;
import com.yxt.talent.rv.controller.manage.prj.prj.viewobj.*;
import com.yxt.talent.rv.controller.manage.prj.result.viewobj.PrjResultVO;
import com.yxt.talent.rv.domain.prj.Prj;
import com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConf;
import com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConfTool;
import com.yxt.talent.rv.domain.prj.repo.PrjDomainRepo;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.PrjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfToolMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserImportResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfToolPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserImportResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.talent.rv.infrastructure.service.file.dto.DynamicExcelExportContent;
import com.yxt.talent.rv.infrastructure.service.remote.L10nAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpevalAclService;
import com.yxt.talent.rv.infrastructure.service.remote.UdpAclService;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static java.lang.String.format;

@Slf4j
@Component
@Deprecated(since = "5.2")
@RequiredArgsConstructor
public class PrjViewAppManage {
    private static final String HEADER_PREFIX = "apis.sptalentrv.prj.overview.export.header.";
    private static final String[] HEADER_SUB_KEYS = {"count", "percent"};
    private static final int MAX_LEASE_TIME = 100;
    private final PrjUserMapper prjUserMapper;
    private final PrjDimConfMapper prjDimConfMapper;
    private final PrjDimConfToolMapper prjDimConfToolMapper;
    private final PerfMapper perfMapper;
    private final PrjUserResultMapper prjUserResultMapper;
    private final PrjUserImportResultMapper prjUserImptResultMapper;
    private final PrjDimMapper prjDimMapper;
    private final SpevalAclService spevalAclService;
    private final SptalentFacade gwnlFacade;
    private final MessageSourceService msgSource;
    private final AuthService authService;
    private final DlcComponent dlcComponent;
    private final ILock lockService;
    private final PrjResultExportStrategy prjResultExportStrategy;
    private final PrjAppService prjAppService;
    private final PrjMapper prjMapper;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final PrjDomainRepo prjDomainRepo;
    private final L10nAclService l10nAclService;
    private final UdpAclService udpAclService;
    private final PrjResultService prjResultService;

    /**
     * 查询盘点目标和概览信息(接口缓存15min)
     *
     * @param orgId
     * @param projectId
     * @param userId
     */
    @SuppressWarnings("OverlyLongMethod")
    public PrjOverviewBasicVO getProjectBasicInfo(String orgId, String projectId, String userId) {
        PrjOverviewBasicVO proOverViewBasicVO = new PrjOverviewBasicVO();
        PrjPO prj = prjMapper.selectByOrgIdAndId(orgId, projectId);
        if (prj == null) {
            throw new ApiException(ExceptionKeys.PRJ_NOT_EXISTED);
        }
        proOverViewBasicVO.setTarget(prj.getRemark());
        List<PrjDimConfPO> prjDimConfs = prjDimConfMapper.selectByOrgIdAndPrjId(orgId, projectId);
        proOverViewBasicVO.setDimensionCount(prjDimConfs.size());
        List<PrjDimConfPO> hasModelList = prjDimConfs.stream().filter(c -> StringUtils.isNotBlank(c.getModelId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(hasModelList)) {
            Set<String> modelIds = getCatalogList(hasModelList.stream().map(PrjDimConfPO::getModelId));
            proOverViewBasicVO.setDimensionConfigCount(modelIds.isEmpty() ? 0 : modelIds.size());
        }
        // 盘点维度工具数量
        List<PrjDimConfToolPO> configTools = prjDimConfToolMapper.selectByOrgIdAndPrjId(orgId, projectId);
        Map<String, List<PrjDimConfToolPO>> toolMap = new HashMap<>(8);
        if (CollectionUtils.isNotEmpty(configTools)) {
            toolMap = configTools.stream().collect(Collectors.groupingBy(PrjDimConfToolPO::getDimensionConfigId));
            // 去除同模引用的工具。同模并且不是来自于测评中心的
            configTools = configTools.stream().filter(t -> !(t.getToolSource() == 1 && t.getEvalRefCenter() == 0))
                    .collect(Collectors.toList());
            //测评中心同模可能被多个相同模型维度引用
            configTools = configTools.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PrjDimConfToolPO::getToolId))),
                    ArrayList::new));
            // 测评数量
            long evalCount = configTools.stream().filter(t -> (t.getToolType() == 2 || t.getToolType() == 4)).count();
            proOverViewBasicVO.setEvalCount((int) evalCount);
        }
        // 查询盘点总人数
        List<PrjUserPO> users = prjUserMapper.selectByOrgIdAndPrjIdOrderByUpdateTimeDesc(orgId, projectId);
        if (CollectionUtils.isNotEmpty(users)) {
            proOverViewBasicVO.setUserCount(users.size());
            // 计算完成度
            if (0 != prj.getProjectStatus() && 1 != prj.getProjectStatus()) {
                // 计算完成率
                BigDecimal progress = calcuRate(orgId, projectId, users, prjDimConfs, toolMap, proOverViewBasicVO);
                prj.setProgress(progress);
                prj.setUpdateTime(new Date());
                prj.setUpdateUserId(userId);
                if (StringUtils.isNotBlank(prj.getId())) {
                    log.debug("LOG10810:");
                    prjMapper.updateById(prj);
                } else {
                    log.debug("LOG10820:");
                    prjMapper.insert(prj);
                }
            }
        }
        // 盘点维度数量
        return proOverViewBasicVO;
    }

    /**
     * 计算盘点完成度信息
     *
     * @param orgId
     * @param projectId
     * @param allUsers
     * @param prjDimConfs
     * @param configToolMap
     * @param proOverViewBasicVO
     */
    @SuppressWarnings({"java:S3776"})
    private BigDecimal calcuRate(String orgId, String projectId, List<PrjUserPO> allUsers,
            List<PrjDimConfPO> prjDimConfs, Map<String, List<PrjDimConfToolPO>> configToolMap,
            PrjOverviewBasicVO proOverViewBasicVO) {
        // 盘点项目中的测评维度数量
        AtomicInteger dimEvalCount = new AtomicInteger();
        // 盘点项目中的导入数据维度数量
        AtomicInteger dimImportCount = new AtomicInteger();

        // 项目下包含的维度类型种类，1-只有数据导入类型，2-包含测评类型
        int dimType = 1;
        // 是否存在工具为测评
        AtomicInteger hasEval = new AtomicInteger(0);
        // 是否存在工具为导入数据
        AtomicInteger hasImport = new AtomicInteger(0);
        // 存储各个导入数据维度下完成人员的map关系用于最终的计算，key为维度id
        Map<String, List<String>> userDimensionMap = new HashMap<>();
        // 存储各个测评数据维度下完成人员的map关系用于最终的计算，key为维度id
        Map<String, List<Evaluation4User>> userDimensionEvalMap = new HashMap<>();
        List<String> userIds = allUsers.stream().map(PrjUserPO::getUserId).toList();
        Map<String, List<PrjDimConfToolPO>> toolMap = new HashMap<>();
        // 计算各个维度下的完成情况
        prjDimConfs.forEach(dim -> {
            log.debug("LOG10490:{}", BeanHelper.bean2Json(dim, ALWAYS));
            // 获取工具信息
            @Nullable List<PrjDimConfToolPO> tools = configToolMap.get(dim.getId());
            if (CollectionUtils.isEmpty(tools)) {
                log.error("calcuRate error projectId={}", dim.getProjectId());
                return;
            }
            // 去除同模的工具.不包括测评中心的
            if (CollectionUtils.isNotEmpty(tools)) {
                tools = tools.stream().filter(t -> !(t.getToolSource() == 1 && t.getEvalRefCenter() == 0)).toList();
            }
            if (CollectionUtils.isNotEmpty(tools)) {
                tools = tools.stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PrjDimConfToolPO::getToolId))),
                        ArrayList::new));
            }
            // 判断维度的盘点工具类型：0-未配置，1-绩效，2-测评，3-盘点数据导入
            // 绩效的话查询判断绩效是否都导入
            // 测评判断是否完成
            // 盘点数据导入的情况判断是否导入了数据
            if (2 == dim.getToolType() || 4 == dim.getToolType()) {
                // 测评
                hasEval.set(1);
                for (PrjDimConfToolPO tool : tools) {
                    // 测评数量加1
                    dimEvalCount.getAndIncrement();
                    BindEvaluaFacade bindEvaluaFacade = new BindEvaluaFacade();
                    bindEvaluaFacade.setOrgId(orgId);
                    bindEvaluaFacade.setEvaluationId(tool.getToolId());
                    Evaluation4UserResultl evaluationUser4GetList = spevalAclService.getEvalustionUserList(
                            bindEvaluaFacade);
                    if (null != evaluationUser4GetList && CollectionUtils.isNotEmpty(
                            evaluationUser4GetList.getUserList())) {
                        userDimensionEvalMap.put(tool.getToolId(), evaluationUser4GetList.getUserList());
                    }
                }
                toolMap.put(dim.getId(), tools);
            } else {
                hasImport.set(1);
                dimImportCount.getAndIncrement();
                if (1 == dim.getToolType()) {
                    // 绩效
                    // 根据绩效周期id去绩效导入表中查询
                    // 去绩效表中查询数据
                    PrjDimConfToolPO prjDimConfTool = tools == null ? null : tools.get(0);
                    Validate.isNotNull(prjDimConfTool, ExceptionKeys.PRJ_DIM_CONF_TOOL_NOT_EXISTED);
                    //noinspection DataFlowIssue
                    List<PerfPO> userList = perfMapper.getPerfByPeriodAndUserIdIfHas(orgId, prjDimConfTool.getToolId(),
                            userIds);
                    if (CollectionUtils.isNotEmpty(userList)) {
                        List<String> hasDataUsers = userList.stream().map(PerfPO::getUserId).toList();
                        userDimensionMap.put(dim.getDimensionId(), hasDataUsers);
                    }
                } else {
                    // 盘点数据导入
                    // 先从导入表查询
                    List<PrjUserImportResultPO> list = prjUserImptResultMapper.selectByOrgIdAndPrjIdAndDimIdAndIfPrjUserIds(
                            orgId, projectId, dim.getDimensionId(), userIds);
                    if (CollectionUtils.isNotEmpty(list)) {
                        List<String> hasDataUsers = list.stream().map(PrjUserImportResultPO::getUserId).toList();
                        userDimensionMap.put(dim.getDimensionId(), hasDataUsers);
                    } else {
                        // 导入表为空，从结果表再去获取一次，以防数据是分数类型
                        List<PrjUserResultPO> resList = prjUserResultMapper.selectByOrgIdAndPrjIdAndDimIdAndIfPrjUserIds(
                                orgId, projectId, dim.getDimensionId(), userIds);
                        if (CollectionUtils.isNotEmpty(resList)) {
                            List<String> hasDataUsers = resList.stream().map(PrjUserResultPO::getUserId).toList();
                            userDimensionMap.put(dim.getDimensionId(), hasDataUsers);
                        }
                    }

                }

            }
        });
        // 计算完成人数和进度
        // 逐个计算每个盘点人员的进度，按照每个维度进行计算
        // A=单个盘点对象的测评进度=被盘点对象所有测评的完成进度相加和/参加的测评数量*100%
        // B=单个盘点对象导入数据的进度=被盘点对象已导入数据维度数据/所有需要导入数据的维度数量
        // 单个盘点对象的进度=（A+B）/2*100%
        // 项目进度=所有盘点对象的进度和/盘点人数*100%
        // 计算每个盘点对象的A和B的值
        if (hasEval.get() == 1 && hasImport.get() == 1) {
            // 有测评且有数据导入
            dimType = 2;
        }
        // 全部完成的用户计算
        int allFinishuser = 0;
        BigDecimal allUserProgress = new BigDecimal(0);
        for (PrjUserPO user : allUsers) {
            // 测评进度
            BigDecimal a = new BigDecimal(0);
            // 导入数据进度
            int b = 0;
            for (PrjDimConfPO dimBean : prjDimConfs) {
                if (2 == dimBean.getToolType() || 4 == dimBean.getToolType()) {
                    // 测评
                    @Nullable List<PrjDimConfToolPO> tools = toolMap.get(dimBean.getId());
                    if (CollectionUtils.isNotEmpty(tools)) {
                        for (PrjDimConfToolPO tool : tools) {
                            if (null != userDimensionEvalMap.get(tool.getToolId())) {
                                Optional<Evaluation4User> evaluationUser4Get = userDimensionEvalMap.get(
                                                tool.getToolId()).stream()
                                        .filter(x -> StringUtils.equalsIgnoreCase(x.getUserId(), user.getUserId()))
                                        .findFirst();
                                if (evaluationUser4Get.isPresent()) {
                                    a = a.add(evaluationUser4Get.get().getEvaluationFinishRate());
                                }
                            }
                        }
                    } else {
                        log.error("calcuRate toolIds error projecId ={}, dimConfigId={}", user.getProjectId(), dimBean.getId());
                    }
                } else {
                    // 盘点数据导入
                    if (null != userDimensionMap.get(dimBean.getDimensionId())) {
                        Optional<String> userId = userDimensionMap.get(dimBean.getDimensionId()).stream()
                                .filter(x -> StringUtils.equalsIgnoreCase(x, user.getUserId())).findFirst();
                        if (userId.isPresent()) {
                            b += 1;
                        }
                    }
                }
            }
            BigDecimal singleUserProgress = getUserProgress(a, dimEvalCount.get(), b, dimImportCount.get(), dimType);
            allUserProgress = allUserProgress.add(singleUserProgress);
            if (a.compareTo(BigDecimal.valueOf(dimEvalCount.get())) == 0 && b == dimImportCount.get()) {
                allFinishuser += 1;
            }
        }
        BigDecimal progress = allUserProgress.divide(BigDecimal.valueOf(allUsers.size()), 2, RoundingMode.DOWN)
                .multiply(BigDecimal.valueOf(100));
        proOverViewBasicVO.setProgress(progress);
        return progress;
    }

    /**
     * 计算单个用户的盘点完成率
     *
     * @param userEvalProgress 测评进度
     * @param evalCount        测评的维度数量
     * @param userImportCount  用户完成的导入维度数量
     * @param dimImportCount   需要导入数据的维度数量
     * @param dimType          项目下包含的维度类型种类，1-只有数据导入类型，2-包含测评类型
     */
    private BigDecimal getUserProgress(BigDecimal userEvalProgress, int evalCount, int userImportCount,
            int dimImportCount, int dimType) {

        BigDecimal aEval = 0 != evalCount ?
                userEvalProgress.divide(BigDecimal.valueOf(evalCount), 2, RoundingMode.HALF_UP) :
                new BigDecimal(0);
        BigDecimal bImport = 0 != dimImportCount ?
                BigDecimal.valueOf(userImportCount)
                        .divide(BigDecimal.valueOf(dimImportCount), 2, RoundingMode.HALF_UP) :
                new BigDecimal(0);
        return aEval.add(bImport).divide(BigDecimal.valueOf(dimType), 2, RoundingMode.HALF_UP);
    }

    /**
     * 获取盘点画像信息(缓存15min)
     *
     * @param orgId
     * @param projectId
     */
    public PrjOverviewPictureVO getPicture(String orgId, String projectId) {
        PrjOverviewPictureVO prjOverviewPictureVO = new PrjOverviewPictureVO();
        List<PrjDimConfPO> prjDimConfs = prjDimConfMapper.selectByOrgIdAndPrjId(orgId, projectId);
        if (CollectionUtils.isNotEmpty(prjDimConfs)) {
            List<String> dimIds = prjDimConfs.stream().map(PrjDimConfPO::getDimensionId).collect(Collectors.toList());
            List<PrjDimPO> prjDims = prjDimMapper.selectByOrgIdAndIfDimIdsOrderByCreateTime(orgId, dimIds);
            Map<String, PrjDimPO> dimNameMap = new HashMap<>(8);
            if (CollectionUtils.isNotEmpty(prjDims)) {
                dimNameMap = StreamUtil.list2map(prjDims, PrjDimPO::getId);
            }
            List<PrjPictureInfoVO> dimDetails = new ArrayList<>();
            Map<String, PrjDimPO> finalDimensionNameMap = dimNameMap;
            prjDimConfs.forEach(dimConf -> {
                PrjPictureInfoVO pictureInfo = new PrjPictureInfoVO();
                PrjDimPO prjDim = finalDimensionNameMap.get(dimConf.getDimensionId());
                pictureInfo.setDimenisonType(prjDim.getDimensionType());
                pictureInfo.setDimensionName(prjDim.getDimensionName());
                pictureInfo.setDimensionId(prjDim.getId());
                // 获取每个维度的模型信息，如果没有配置模型不做处理
                if (StringUtils.isNotBlank(dimConf.getModelId())) {
                    SkillModelItemSearch modelItemSearch = new SkillModelItemSearch();
                    modelItemSearch.setOrgId(orgId);
                    modelItemSearch.setModelId(dimConf.getModelId());
                    // 由于人才标注删除了老的接口，人才盘点也即将从老盘点迁移到新盘点，这些接口都不再需要，所以这里只简单注释掉无效方法使其不再报错，不做额外的兼容处理。 -- by zhangq 2025-01-06
//                    List<SkillModelItem> skillList = gwnlFacade.getSkillModelList(modelItemSearch);
                    List<SkillModelItem> skillList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(skillList)) {
                        List<String> skillNames = skillList.stream().map(SkillModelItem::getSkillName)
                                .collect(Collectors.toList());
                        pictureInfo.setSkillNames(skillNames);
                    }
                }
                dimDetails.add(pictureInfo);
            });
            prjOverviewPictureVO.setDimensionDetails(dimDetails);
        }
        return prjOverviewPictureVO;
    }

    public DeptPrjStatisticsClientVO getMineProjectStatistics(String orgId, String userId,
            DeptProjectClientQuery criteria, String lang) {
        DeptPrjClientVO projectStatistics = prjMapper.selectMineProjectStatistics(orgId, userId, criteria);
        if (projectStatistics == null || projectStatistics.getId() == null) {
            throw new ApiException(ExceptionKeys.PRJ_NOT_EXISTED);
        }

        DeptPrjStatisticsClientVO data = new DeptPrjStatisticsClientVO();
        BeanCopierUtil.copy(projectStatistics, data);

        // 填充分类名称
        prjAppService.fillCategoryName(orgId, Collections.singletonList(data));

        // 基于条件查询出来的人员进行优秀、中坚、待提升的统计
        List<String> scopePrjUserIds = prjAppService.getScopedPrjUserIds(orgId, criteria.getPrjId(), criteria);
        PrjOverviewGroupVO prjOverviewGroupVO = getGroup4Project(orgId, criteria.getPrjId(), scopePrjUserIds, true,
                lang);
        data.setUserResults(prjOverviewGroupVO.getUserResults());

        return data;
    }

    /**
     * 盘点项目概览-人才储备
     *
     * @param orgId
     * @param projectId
     * @param userIds       参与计算的用户Id
     * @param isOnlyUserIds 是否只在提供的userId中计算
     */
    public PrjOverviewGroupVO getGroup4Project(String orgId, String projectId, List<String> userIds,
            boolean isOnlyUserIds, String lang) {
        PrjOverviewGroupVO prjOverviewGroupVO = new PrjOverviewGroupVO();
        if (isOnlyUserIds && CollectionUtils.isEmpty(userIds)) {
            return prjOverviewGroupVO;
        }

        // 先获取盘点下的人员总数
        List<PrjUserPO> users = prjUserMapper.selectByOrgIdAndPrjIdOrderByUpdateTimeDesc(orgId, projectId);
        if (CollectionUtils.isEmpty(users)) {
            return prjOverviewGroupVO;
        }

        if (isOnlyUserIds) {
            log.info("LOG65010:");
            users.removeIf(e -> !userIds.contains(e.getUserId()));
        }

        List<PrjOverviewResultDTO> userResults = new ArrayList<>();
        List<PrjResultVO> resultLabelList = prjResultService.getResultLabelList(orgId, lang);
        handleUserResults(users, userResults, resultLabelList);
        prjOverviewGroupVO.setUserResults(userResults);
        List<PrjOverviewGroupDeptDTO> prjOverviewGroupDeptDtos = deptGroup(orgId, projectId, users, resultLabelList,
                lang);
        prjOverviewGroupVO.setDepts(prjOverviewGroupDeptDtos);
        // 优秀人才定义：不存在低等级且至少有一个维度为高等级
        // 待提升人员定义：所有维度不存在高等级，且低等级数量大中等级数量
        // 中坚人员定义：中坚=总人数-优秀-待提升
        // 查询项目配置的维度信息
        //        List<PrjDimConfPO> prjDimConfs = prjDimConfMapper.selectByOrgIdAndPrjId(
        //                orgId, projectId);
        //        if (CollectionUtils.isNotEmpty(prjDimConfs)) {
        //            // 查询所有项目下的计算后的数据
        //            List<PrjUserResultPO> userCalcResults =
        //                    prjUserResultMapper.selectByOrgIdAndPrjIdAndDimIds(orgId, projectId,
        //                            prjDimConfs.stream()
        //                                    .map(PrjDimConfPO::getDimensionId)
        //                                    .collect(Collectors.toList()));
        //            if (CollectionUtils.isNotEmpty(userCalcResults)) {
        //                Map<String, List<PrjUserResultPO>> resultMap = userCalcResults.stream()
        //                        .collect(Collectors.groupingBy(PrjUserResultPO::getUserId));
        //
        //                // 优秀人才数量
        //                int goodUserCount = 0;
        //                // 待提升
        //                int improveUserCount = 0;
        //                // 不满足条件
        //                int noneUserCount = 0;
        //                for (PrjUserPO user : users) {
        //                    List<PrjUserResultPO> ucResults = resultMap.get(user.getUserId());
        //                    // 维度结果少于2个不计算
        //                    if (CollectionUtils.isEmpty(ucResults) || ucResults.size() < 2) {
        //                        noneUserCount++;
        //                    } else if (isGood(ucResults)) {
        //                        goodUserCount++;
        //                    } else if (isImprove(ucResults)) {
        //                        improveUserCount++;
        //                    }
        //                }
        //                prjOverviewGroupVO.setGoodUserCount(goodUserCount);
        //                // 计算优秀人才率
        //                BigDecimal goodUserPercent = BigDecimal.valueOf(goodUserCount)
        //                        .divide(BigDecimal.valueOf(users.size()), 2, RoundingMode.HALF_UP)
        //                        .multiply(BigDecimal.valueOf(100));
        //                prjOverviewGroupVO.setGoodUserPercent(goodUserPercent);
        //                prjOverviewGroupVO.setImproveUserCount(improveUserCount);
        //                // 计算待提升人才率
        //                BigDecimal improveUserPercent = BigDecimal.valueOf(improveUserCount)
        //                        .divide(BigDecimal.valueOf(users.size()), 2, RoundingMode.HALF_UP)
        //                        .multiply(BigDecimal.valueOf(100));
        //                prjOverviewGroupVO.setImproveUserPercent(improveUserPercent);
        //                // 中坚人员
        //                int middleUserCount =
        //                        users.size() - goodUserCount - improveUserCount - noneUserCount;
        //                prjOverviewGroupVO.setMiddleUserCount(middleUserCount);
        //                List<PrjOverviewGroupDeptDTO> prjOverviewGroupDeptDtos =
        //                        deptGroup(orgId, projectId, users, userCalcResults, lang);
        //                prjOverviewGroupVO.setDepts(prjOverviewGroupDeptDtos);
        //            }
        //        }
        return prjOverviewGroupVO;
    }

    private void handleUserResults(List<PrjUserPO> users, List<PrjOverviewResultDTO> userResults,
            List<PrjResultVO> resultLabelList) {
        resultLabelList.forEach(label -> {
            PrjOverviewResultDTO prjOverviewResultDTO = new PrjOverviewResultDTO();
            prjOverviewResultDTO.setLabelName(label.getLabelName());
            int count = users.stream().filter(user -> Objects.equals(label.getOrderIndex(), user.getPrjResult()))
                    .toList().size();
            prjOverviewResultDTO.setLabelCount(count);
            prjOverviewResultDTO.setLabelPercent(
                    BigDecimal.valueOf(count).divide(BigDecimal.valueOf(users.size()), 2, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100)));
            prjOverviewResultDTO.setRuleContent(label.getRuleContent());
            userResults.add(prjOverviewResultDTO);
        });
        //其他人员处理
        int otherCount = users.stream().filter(user -> Objects.equals(0, user.getPrjResult())).toList().size();
        PrjOverviewResultDTO otherResultDTO = new PrjOverviewResultDTO();
        otherResultDTO.setLabelName("其他人员");
        otherResultDTO.setLabelCount(otherCount);
        otherResultDTO.setLabelPercent(
                BigDecimal.valueOf(otherCount).divide(BigDecimal.valueOf(users.size()), 2, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100)));
        otherResultDTO.setRuleContent("不在现有盘点结果规则统计范围内的人员");
        otherResultDTO.setOthers(1);
        userResults.add(otherResultDTO);
    }

    @Nonnull
    private List<PrjOverviewGroupDeptDTO> deptGroup(String orgId, String projectId, List<PrjUserPO> users,
            List<PrjResultVO> resultLabelList, String lang) {
        if (CollectionUtils.isEmpty(users)) {
            users = prjUserMapper.selectByOrgIdAndPrjIdOrderByUpdateTimeDesc(orgId, projectId);
        }

        //userCalcResults = getPrjUserResultPOS(orgId, projectId, userCalcResults);
        List<PrjOverviewGroupDeptDTO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(users)) {
            //            Map<String, List<PrjUserResultPO>> userResultMap = userCalcResults.stream()
            //                    .collect(Collectors.groupingBy(PrjUserResultPO::getUserId));
            // 获取userIds
            List<String> userIds = users.stream().map(PrjUserPO::getUserId).collect(Collectors.toList());
            // 去udp查询人员信息
            List<UdpLiteUserPO> rvUdpUsers = udpLiteUserMapper.selectByUserIdsIncludeDelete(orgId, userIds);

            fillDeptName(orgId, lang, rvUdpUsers);

            // 人员根据部门分组
            // 存一份部门的id和名称的map关系
            Map<String, String> deptNameMap = StreamUtil.list2map(rvUdpUsers, UdpLiteUserPO::getDeptId,
                    UdpLiteUserPO::getDeptName);
            // 人员根据部门id进行分组
            Map<String, List<UdpLiteUserPO>> userMap = rvUdpUsers.stream()
                    .collect(Collectors.groupingBy(UdpLiteUserPO::getDeptId));
            // 根据每个部门下的人员进行计算
            for (Map.Entry<String, List<UdpLiteUserPO>> entry : userMap.entrySet()) {
                String deptId = entry.getKey();
                String deptName = deptNameMap.get(deptId);
                List<UdpLiteUserPO> deptUsers = entry.getValue();
                if (CollectionUtils.isEmpty(deptUsers)) {
                    continue;
                }
                List<String> deptUserIds = deptUsers.stream().map(UdpLiteUserPO::getId).toList();
                PrjOverviewGroupDeptDTO prjOverviewGroupDeptDTO = new PrjOverviewGroupDeptDTO();
                prjOverviewGroupDeptDTO.setDeptName(deptName);
                List<PrjOverviewResultDTO> userResults = new ArrayList<>();
                List<PrjUserPO> prjUserPOS = users.stream().filter(user -> deptUserIds.contains(user.getUserId()))
                        .toList();
                handleUserResults(prjUserPOS, userResults, resultLabelList);
                prjOverviewGroupDeptDTO.setUserResults(userResults);
                //                AtomicInteger goodUserCount = new AtomicInteger(0);
                //                AtomicInteger improveUserCount = new AtomicInteger(0);
                //                AtomicInteger noneUserCount = new AtomicInteger(0);
                //                AtomicInteger totalCount = new AtomicInteger();
                //                PrjOverviewGroupDeptDTO prjOverviewGroupDeptDTO = new PrjOverviewGroupDeptDTO();
                //                prjOverviewGroupDeptDTO.setDeptName(deptName);
                //                doCalc(deptUsers, userResultMap, noneUserCount, goodUserCount, totalCount,
                //                        improveUserCount);
                //                prjOverviewGroupDeptDTO.setGoodUserCount(goodUserCount.get());
                //                prjOverviewGroupDeptDTO.setImproveUserCount(improveUserCount.get());
                //                int middleUserCount =
                //                        totalCount.get() - improveUserCount.get() - goodUserCount.get();
                //                prjOverviewGroupDeptDTO.setMiddleUserCount(middleUserCount);
                //                fillPercent(totalCount, prjOverviewGroupDeptDTO, goodUserCount, improveUserCount,
                //                        middleUserCount);
                result.add(prjOverviewGroupDeptDTO);
            }
        }
        return result;
    }

    private void fillDeptName(String orgId, String lang, List<UdpLiteUserPO> rvUdpUsers) {
        Map<String, IdName> finalIdNameMap = getStringIdNameMap(orgId, lang, rvUdpUsers);
        rvUdpUsers.forEach(e -> {
            if (MapUtils.isNotEmpty(finalIdNameMap) && null != finalIdNameMap.get(e.getDeptId())) {
                e.setDeptName(finalIdNameMap.get(e.getDeptId()).getName());
            }
        });
    }

    private Map<String, IdName> getStringIdNameMap(String orgId, String lang, List<UdpLiteUserPO> rvUdpUsers) {
        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        l10nAclService.translateList(enableLocalization, List.of(orgId), lang, UdpLiteUserPO.class, rvUdpUsers);
        Map<String, IdName> idNameMap = new HashMap<>(8);
        if (enableLocalization) {
            Set<String> deptIds = rvUdpUsers.stream().map(UdpLiteUserPO::getDeptId).collect(Collectors.toSet());
            List<IdName> idNames = udpAclService.getDeptInfoByIds(orgId, new ArrayList<>(deptIds));
            if (CollectionUtils.isNotEmpty(idNames)) {
                idNameMap = StreamUtil.list2map(idNames, IdName::getId);
            }
        }
        return idNameMap;
    }

    /**
     * 盘点项目概览-能力矩阵
     *
     * @param prjId
     */
    // @OverviewCache
    public PrjOverviewSkillAverageVO matrix(UserCacheBasic userCache, String prjId, String dimId) {
        String orgId = userCache.getOrgId();
        String userId = userCache.getUserId();
        PrjOverviewSkillAverageVO prjOverviewSkillAverageVO = new PrjOverviewSkillAverageVO();
        // 查询项目下的所有的维度配置的测评信息
        PrjDimConfPO prjDimConf = prjDimConfMapper.selectByOrgIdAndId(orgId, dimId);
        if (null == prjDimConf) {
            return prjOverviewSkillAverageVO;
        }

        List<String> configIds = new ArrayList<>();
        configIds.add(prjDimConf.getId());
        // 获取项目人员
        List<PrjUserPO> users = prjUserMapper.selectByOrgIdAndPrjIdOrderByUpdateTimeDesc(orgId, prjId);
        List<ReportRecordEvaluationVo> reportRecordVos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(users)) {
            List<String> userIds = users.stream().map(PrjUserPO::getUserId).collect(Collectors.toList());
            // 读取配置的工具
            List<PrjDimConfToolPO> configTools = prjDimConfToolMapper.selectEvalAndBzByOrgIdAndDimConfIds(orgId,
                    configIds);

            // 如果是云方案，盘点矩阵分析：云方案的维度只计算数据源的数据
            PrjPO prj = prjMapper.selectByOrgIdAndId(orgId, prjId);
            if (prj == null) {
                throw new ApiException(ExceptionKeys.PRJ_NOT_EXISTED);
            }
            removeCloudPrjTool(prj, configTools);
            logIfConfigToolEmpty(configTools);

            // 测评相关的能力矩阵
            if ((prjDimConf.getToolType() == PrjDimConfTool.Type.EVAL.getCode()
                    || prjDimConf.getToolType() == PrjDimConfTool.Type.BZ.getCode()) && CollectionUtils.isNotEmpty(
                    configTools)) {
                prjOverviewSkillAverageVO = getPrjOverviewSkillAverageVO(configTools, orgId, userId, userIds,
                        prjDimConf, reportRecordVos);
            }
            // 非测评相关的能力数据导入
            if (prjDimConf.getToolType() == PrjDimConfTool.Type.IMPT.getCode()) {
                // 判断导入的是否是评分数据，如果是直接导入的结果数据则不做任何处理
                prjOverviewSkillAverageVO = getPrjOverviewSkillAverageVO(prjId, orgId, prjDimConf, userIds,
                        prjOverviewSkillAverageVO);
            }
        }
        return prjOverviewSkillAverageVO;
    }

    private PrjOverviewSkillAverageVO getPrjOverviewSkillAverageVO(String prjId, String orgId, PrjDimConfPO prjDimConf,
            List<String> userIds, PrjOverviewSkillAverageVO prjOverviewSkillAverageVO) {

        Prj.LoadConfig loadConfig = Prj.LoadConfig.builder().loadPrjDimConf(PrjDimConf.LoadConfig.WITH_ALL).build();
        Prj prj = prjDomainRepo.load(orgId, prjId, loadConfig)
                .orElseThrow(() -> new ApiException(ExceptionKeys.PRJ_NOT_EXISTED));

        String dimId = prjDimConf.getDimensionId();
        String modelId = prjDimConf.getModelId();

        return prj.findPrjImptType(dimId).map(imptType -> {
            // 1-导入结果，2-导入能力评分, 获取维度模型信息，不配置模型不进行计算
            log.debug("LOG20680:imptType={}, modeId={}", imptType, modelId);
            if (imptType.getImptTypeEnum().byScore() && StringUtils.isNotBlank(modelId)) {
                // 查询维度导入的能力得分数据
                List<PrjUserImportResultPO> datas = prjUserImptResultMapper.selectByOrgIdAndPrjIdAndDimIdAndIfPrjUserIds(
                        orgId, prjId, dimId, userIds);
                if (CollectionUtils.isNotEmpty(datas)) {
                    log.debug("LOG20620:");
                    return getResultSkillImport(orgId, modelId, datas);
                }
            }
            return null;
        }).orElse(prjOverviewSkillAverageVO);
    }

    @Nonnull
    private PrjOverviewSkillAverageVO getPrjOverviewSkillAverageVO(@NonNull List<PrjDimConfToolPO> configTools,
            String orgId, String userId, List<String> userIds, PrjDimConfPO prjDimConf,
            List<ReportRecordEvaluationVo> reportRecordVos) {

        List<String> evalIds = StreamUtil.mapList(configTools, PrjDimConfToolPO::getToolId);
        log.debug("LOG20590:{}", evalIds);

        for (String evalId : evalIds) {

            log.info("LOG30050:从测评放请求测评基本信息，入参：orgId = {}, evalId = {}", orgId, evalId);
            Evaluation4Get evaluation = spevalAclService.getEvaluationDetail(evalId, orgId, userId);
            log.info("LOG30030:从测评放请求测评基本信息，返回：{}", BeanHelper.bean2Json(evaluation, ALWAYS));

            BindEvaluaFacade search = new BindEvaluaFacade();
            search.setOrgId(orgId);
            search.setEvaluationId(evalId);
            search.setUserIds(userIds);
            log.info("LOG62070:从测评放请求测评完成人员数据，入参：{}", BeanHelper.bean2Json(search, ALWAYS));
            Evaluation4UserResultl evaluation4UserResultl = spevalAclService.getEvalustionUserList(search);
            log.info("LOG62080:从测评放请求测评完成人员数据，返回结果：{}",
                    BeanHelper.bean2Json(evaluation4UserResultl, ALWAYS));
            List<Evaluation4User> relationUsers = evaluation4UserResultl.getUserList();
            if (CollectionUtils.isEmpty(relationUsers)) {
                log.info("LOG62090:从测评放请求测评完成人员数据，返回结果为空，跳过:{}", evalId);
                continue;
            }
            // 过滤掉没有完成的
            List<Evaluation4User> finishedUsers = relationUsers.stream().filter(user -> user.getFinished() == 1)
                    .collect(Collectors.toList());
            List<String> finishedUserIds = finishedUsers.stream().map(Evaluation4User::getUserId)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isNotEmpty(finishedUserIds)) {
                log.info("LOG62100:测评下没有用户完成测评：{}", evalId);
                continue;
            }
            search.setUserIds(finishedUserIds);
            log.info("LOG20610:从测评放请求数据，入参：{}", BeanHelper.bean2Json(search, ALWAYS));
            ReportRecordEvaluationVo reportRecordVo = null;
            try {
                reportRecordVo =
                        evaluation.getScene() == 2 ? spevalAclService.findThirdRecordInfoSkillInfo(search) // 倍智测评
                                : spevalAclService.findRecordInfoSkillInfo(search); // 普通测评
            } catch (Exception e) {
                log.error("LOG20570:", e);
            }
            if (reportRecordVo == null) {
                continue;
            }
            log.info("LOG20600:从测评放请求数据，返回结果：{}", BeanHelper.bean2Json(reportRecordVo, ALWAYS));
            if (StringUtils.isNotBlank(prjDimConf.getModelId())) {
                reportRecordVo.setModelId(prjDimConf.getModelId());
            }
            reportRecordVos.add(reportRecordVo);
        }
        return getResult(orgId, reportRecordVos);
    }

    private static void logIfConfigToolEmpty(List<PrjDimConfToolPO> configTools) {
        if (CollectionUtils.isEmpty(configTools)) {
            log.warn("LOG11380:{}", BeanHelper.bean2Json(configTools, ALWAYS));
        }
    }

    private static void removeCloudPrjTool(PrjPO prj, List<PrjDimConfToolPO> configTools) {
        if (prj.getProjectType() == 1) {
            log.warn("LOG11390:{}", configTools.size());
            configTools.removeIf(e -> e.getToolSource() != 3);
        }
    }

    /**
     * 根据导入的数据计算能力矩阵
     *
     * @param orgId
     * @param modelId         模型id
     * @param importSkillData
     */
    @SuppressWarnings("java:S1172")
    private PrjOverviewSkillAverageVO getResultSkillImport(String orgId, String modelId,
            List<PrjUserImportResultPO> importSkillData) {
        PrjOverviewSkillAverageVO result = new PrjOverviewSkillAverageVO();
        // 团队能力得分平均数
        List<TeamSkillDetailVO> skillAverageList = new ArrayList<>();
        // 团队能力矩阵
        List<TeamSkillAnalyseVO> analyseList = new ArrayList<>();
        // 能力平均分最大值
        BigDecimal maxSkillScore = new BigDecimal(0);
        // 能力平均分最小值
        BigDecimal minSkillScore = null;
        // 离散度最大值
        BigDecimal maxDispersion = new BigDecimal(0);
        // 离散度最小值
        BigDecimal minDispersion = null;
        // 能力平均分平均值
        BigDecimal skillAverage = new BigDecimal(0);
        // 离散度平均值
        BigDecimal dispersionAverage = new BigDecimal(0);

        int skillCount = 0;

        // 能力模型id获取
        SkillModelItemSearch modelItemSearch = new SkillModelItemSearch();
        modelItemSearch.setOrgId(orgId);
        modelItemSearch.setModelId(modelId);
        // 由于人才标注删除了老的接口，人才盘点也即将从老盘点迁移到新盘点，这些接口都不再需要，所以这里只简单注释掉无效方法使其不再报错，不做额外的兼容处理。 -- by zhangq 2025-01-06
//        List<SkillModelItem> skillModelMaps = gwnlFacade.getSkillModelList(modelItemSearch);
        List<SkillModelItem> skillModelMaps = new ArrayList<>();
        Map<String, List<SkillModelItem>> skillCatalogMap = new HashMap<>(8);
        Map<String, String> skillCatalogNameMap = getSkillCatalogNameMap(skillModelMaps, skillCatalogMap);
        Map<String, String> skillNameMap = getSkillNameMap(skillModelMaps);
        Set<String> catalogList = getCatalogList(skillModelMaps.stream().map(SkillModelItem::getCatalogModelId));
        Map<String, Map<String, List<PrjUserImportResultPO>>> skillScoreMap = getSkillScoreMap(importSkillData);

        log.debug("LOG20630:{}", catalogList);
        for (String catalogId : catalogList) {
            List<String> skillIds = getSkillIds(catalogId, skillCatalogMap);
            log.debug("LOG20640:{}", skillIds);
            List<TeamSkillAvaDetailVO> details = new ArrayList<>(skillIds.size());
            // 开始计算团队能力得分平均数
            for (String skillId : skillIds) {
                Map<String, List<PrjUserImportResultPO>> userSkillScoreMap = skillScoreMap.get(skillId);
                if (null == userSkillScoreMap) {
                    // 能力无得分情况不参与计算
                    log.debug("LOG20650:{}", skillId);
                    continue;
                }
                skillCount++;
                List<BigDecimal> scores = getUserMultiScore(userSkillScoreMap);
                log.debug("LOG20660:{}", scores);
                if (CollectionUtils.isNotEmpty(scores)) {
                    TeamSkillAvaDetailVO detail = new TeamSkillAvaDetailVO();
                    detail.setSkillId(skillId);
                    detail.setSkillName(skillNameMap.get(skillId));
                    BigDecimal aveScore = getSkillAverage(scores);
                    detail.setAverageScore(aveScore);
                    TeamSkillMapDTO teamSkillMapDTO = new TeamSkillMapDTO();
                    detail.setTeamSkillMap(teamSkillMapDTO);
                    details.add(detail);
                    // 开始计算团队能力矩阵
                    TeamSkillAnalyseVO analyse = new TeamSkillAnalyseVO();
                    analyse.setSkillId(skillId);
                    analyse.setSkillName(skillNameMap.get(skillId));
                    analyse.setAverageScore(aveScore);
                    BigDecimal dispersion = getDispersion(scores, aveScore);
                    analyse.setDispersion(dispersion);
                    skillAverage = skillAverage.add(aveScore);
                    dispersionAverage = dispersionAverage.add(dispersion);
                    maxSkillScore = maxSkillScore.compareTo(aveScore) < 0 ? aveScore : maxSkillScore;
                    minSkillScore = doGetMinDispersionOrSkill(minSkillScore, aveScore);
                    maxDispersion = maxDispersion.compareTo(dispersion) < 0 ? dispersion : maxDispersion;
                    minDispersion = doGetMinDispersionOrSkill(minDispersion, dispersion);
                    analyseList.add(analyse);
                }
            }
            TeamSkillDetailVO teamSkillDetailVO = new TeamSkillDetailVO();
            teamSkillDetailVO.setCatalogId(catalogId);
            teamSkillDetailVO.setCatalogName(skillCatalogNameMap.get(catalogId));
            teamSkillDetailVO.setSkillAverage(details);
            skillAverageList.add(teamSkillDetailVO);
        }
        result.setSkillAverageList(skillAverageList);
        result.setAnalyse(analyseList);
        result.setMaxDispersion(maxDispersion);
        result.setMinDispersion(minDispersion);
        result.setMaxSkillScore(maxSkillScore);
        result.setMinSkillScore(minSkillScore);
        if (0 != skillCount) {
            result.setSkillAverage(skillAverage.divide(BigDecimal.valueOf(skillCount), 2, RoundingMode.HALF_UP));
            result.setDispersionAverage(
                    dispersionAverage.divide(BigDecimal.valueOf(skillCount), 2, RoundingMode.HALF_UP));
        }
        return result;
    }

    @Nonnull
    private static List<String> getSkillIds(String catalogId, Map<String, List<SkillModelItem>> skillCatalogMap) {
        return skillCatalogMap.get(catalogId).stream().map(SkillModelItem::getSkillId).collect(Collectors.toList());
    }

    private static Map<String, String> getSkillNameMap(List<SkillModelItem> skillModelMaps) {
        return StreamUtil.list2map(skillModelMaps, SkillModelItem::getSkillId, SkillModelItem::getSkillName);
    }

    @Nonnull
    private static Set<String> getCatalogList(Stream<String> skillModelMaps) {
        return skillModelMaps.collect(Collectors.toSet());
    }

    private static BigDecimal doGetMinDispersionOrSkill(@Nullable BigDecimal minDispersion, BigDecimal dispersion) {
        if (null == minDispersion) {
            return dispersion;
        }
        return minDispersion.compareTo(dispersion) > 0 ? dispersion : minDispersion;
    }

    @Nonnull
    private static List<BigDecimal> getUserMultiScore(Map<String, List<PrjUserImportResultPO>> userSkillScoreMap) {
        List<BigDecimal> scores = new ArrayList<>();
        // 处理一个人多个能力得分的情况
        for (Map.Entry<String, List<PrjUserImportResultPO>> e : userSkillScoreMap.entrySet()) {
            List<PrjUserImportResultPO> sList = e.getValue();
            if (CollectionUtils.isNotEmpty(sList)) {
                BigDecimal sum = BigDecimal.ZERO;
                for (PrjUserImportResultPO s : sList) {
                    sum = sum.add(s.getScore());
                }
                // 求平均值返回
                BigDecimal aveValue = sum.divide(BigDecimal.valueOf(sList.size()), 4, RoundingMode.HALF_UP);
                scores.add(aveValue);
            } else {
                scores.add(BigDecimal.ZERO);
            }
        }
        return scores;
    }

    @Nonnull
    private static Map<String, String> getSkillCatalogNameMap(List<SkillModelItem> skillModelMaps,
            Map<String, List<SkillModelItem>> skillCatalogMap) {
        Map<String, String> skillCatalogNameMap = new HashMap<>(8);
        for (SkillModelItem entity : skillModelMaps) {
            if (null != skillCatalogMap.get(entity.getCatalogModelId())) {
                // 重复能力排除
                List<SkillModelItem> existList = skillCatalogMap.get(entity.getCatalogModelId());
                boolean isExist = false;
                for (SkillModelItem item : existList) {
                    if (item.getSkillId().equalsIgnoreCase(entity.getSkillId())) {
                        isExist = true;
                        break;
                    }
                }
                if (!isExist) {
                    skillCatalogMap.get(entity.getCatalogModelId()).add(entity);
                }
            } else {
                List<SkillModelItem> list = new ArrayList<>();
                list.add(entity);
                skillCatalogMap.put(entity.getCatalogModelId(), list);
            }
            skillCatalogNameMap.put(entity.getCatalogModelId(), entity.getSkillCatalogName());
        }
        return skillCatalogNameMap;
    }

    @Nonnull
    private static Map<String, Map<String, List<PrjUserImportResultPO>>> getSkillScoreMap(
            List<PrjUserImportResultPO> importSkillData) {
        Map<String, Map<String, List<PrjUserImportResultPO>>> skillScoreMap = new HashMap<>(8);
        // 按照能力分组
        Map<String, List<PrjUserImportResultPO>> teampMap = importSkillData.stream()
                .collect(Collectors.groupingBy(PrjUserImportResultPO::getSkillId));
        for (Map.Entry<String, List<PrjUserImportResultPO>> entry : teampMap.entrySet()) {
            Map<String, List<PrjUserImportResultPO>> v = entry.getValue().stream()
                    .collect(Collectors.groupingBy(PrjUserImportResultPO::getUserId));
            skillScoreMap.put(entry.getKey(), v);
        }
        return skillScoreMap;
    }

    /**
     * 根据测评数据计算能力矩阵
     *
     * @param orgId           机构id
     * @param reportRecordVos 测评结果数据
     */
    private PrjOverviewSkillAverageVO getResult(String orgId, List<ReportRecordEvaluationVo> reportRecordVos) {
        // key-skillid value-成员得分
        Map<String, Map<String, List<BigDecimal>>> skillScoreMap = new HashMap<>(8);
        // key-skillid value-能力最大分
        Map<String, BigDecimal> maxScoreMap = new HashMap<>(8);
        // key-能力分类id value-skillids
        Map<String, List<SkillModelItem>> skillCatalogMap = new HashMap<>(8);
        Map<String, Integer> skillCatalogBzMap = new HashMap<>(8);
        Map<String, String> skillNameMap = new HashMap<>(8);
        Map<String, String> skillCatalogNameMap = new HashMap<>(8);
        // 团队能力得分平均数
        List<TeamSkillDetailVO> skillAverageList = new ArrayList<>();
        // 团队能力矩阵
        List<TeamSkillAnalyseVO> analyseList = new ArrayList<>();
        // 能力平均分最大值
        BigDecimal maxSkillScore = new BigDecimal(0);
        // 能力平均分最小值
        BigDecimal minSkillScore = null;
        // 离散度最大值
        BigDecimal maxDispersion = new BigDecimal(0);
        // 离散度最小值
        BigDecimal minDispersion = null;
        // 能力平均分平均值
        BigDecimal skillAverage = new BigDecimal(0);
        // 离散度平均值
        BigDecimal dispersionAverage = new BigDecimal(0);
        int skillCount = 0;

        Set<String> catalogList = getCatalogList(orgId, reportRecordVos, skillScoreMap, maxScoreMap, skillCatalogBzMap,
                skillCatalogMap, skillCatalogNameMap, skillNameMap);
        for (String catalogId : catalogList) {
            List<String> skillIds = getSkillIds(catalogId, skillCatalogBzMap, skillCatalogMap);
            // 开始计算团队能力得分平均数
            List<TeamSkillAvaDetailVO> details = new ArrayList<>(skillIds.size());
            for (String skillId : skillIds) {
                Map<String, List<BigDecimal>> userSkillScoreMap = skillScoreMap.get(skillId);
                if (null == userSkillScoreMap) {
                    // 能力无得分情况不参与计算
                    continue;
                }
                skillCount++;
                List<BigDecimal> scores = doHandleMultiScores(userSkillScoreMap);
                BigDecimal maxScore = maxScoreMap.get(skillId);
                if (CollectionUtils.isNotEmpty(scores)) {
                    TeamSkillAvaDetailBuildDetail result1 = getTeamSkillAvaDetailBuildDetail(skillId, skillNameMap,
                            scores, maxScore);
                    details.add(result1.detail());
                    // 开始计算团队能力矩阵
                    TeamSkillAnalyseVO analyse = new TeamSkillAnalyseVO();
                    analyse.setSkillId(skillId);
                    analyse.setSkillName(skillNameMap.get(skillId));
                    analyse.setAverageScore(result1.aveScore());
                    BigDecimal dispersion = getDispersion(scores, result1.aveScore(), maxScore);
                    analyse.setDispersion(dispersion);
                    skillAverage = skillAverage.add(result1.aveScore());
                    dispersionAverage = dispersionAverage.add(dispersion);

                    maxSkillScore =
                            maxSkillScore.compareTo(result1.aveScore()) < 0 ? result1.aveScore() : maxSkillScore;
                    minSkillScore = getMinSkillScore(minSkillScore, result1);
                    maxDispersion = maxDispersion.compareTo(dispersion) < 0 ? dispersion : maxDispersion;
                    minDispersion = getMinDispersion(minDispersion, dispersion);
                    analyseList.add(analyse);
                }
            }
            TeamSkillDetailVO teamSkillDetailVO = new TeamSkillDetailVO();
            teamSkillDetailVO.setCatalogId(catalogId);
            teamSkillDetailVO.setCatalogName(skillCatalogNameMap.get(catalogId));
            teamSkillDetailVO.setSkillAverage(details);
            skillAverageList.add(teamSkillDetailVO);
        }

        PrjOverviewSkillAverageVO result = new PrjOverviewSkillAverageVO();
        result.setSkillAverageList(skillAverageList);
        result.setAnalyse(analyseList);
        result.setMaxDispersion(maxDispersion);
        result.setMinDispersion(minDispersion);
        result.setMaxSkillScore(maxSkillScore);
        result.setMinSkillScore(minSkillScore);
        log.debug("LOG20670:{}", skillCount);
        if (0 != skillCount) {
            result.setSkillAverage(skillAverage.divide(BigDecimal.valueOf(skillCount), 2, RoundingMode.HALF_UP));
            result.setDispersionAverage(
                    dispersionAverage.divide(BigDecimal.valueOf(skillCount), 2, RoundingMode.HALF_UP));
        }
        return result;
    }

    private static BigDecimal getMinSkillScore(@Nullable BigDecimal minSkillScore,
            TeamSkillAvaDetailBuildDetail result1) {
        if (null != minSkillScore) {
            return minSkillScore.compareTo(result1.aveScore()) > 0 ? result1.aveScore() : minSkillScore;
        } else {
            return result1.aveScore();
        }
    }

    private static BigDecimal getMinDispersion(@Nullable BigDecimal minDispersion, BigDecimal dispersion) {
        if (null != minDispersion) {
            minDispersion = minDispersion.compareTo(dispersion) > 0 ? dispersion : minDispersion;
        } else {
            minDispersion = dispersion;
        }
        return minDispersion;
    }

    @Nonnull
    private TeamSkillAvaDetailBuildDetail getTeamSkillAvaDetailBuildDetail(String skillId,
            Map<String, String> skillNameMap, List<BigDecimal> scores, BigDecimal maxScore) {
        TeamSkillAvaDetailVO detail = new TeamSkillAvaDetailVO();
        detail.setSkillId(skillId);
        detail.setSkillName(skillNameMap.get(skillId));
        BigDecimal aveScore = getSkillAverage(scores, maxScore);
        detail.setAverageScore(aveScore);
        BigDecimal high = maxScore.multiply(BigDecimal.valueOf(60))
                .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        BigDecimal middle = maxScore.multiply(BigDecimal.valueOf(40))
                .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        long highCount = scores.stream().filter(score -> score.compareTo(high) >= 0).count();
        long middleCount = scores.stream()
                .filter(score -> score.compareTo(high) < 0 && score.compareTo(middle) >= 0)
                .count();
        long lowCount = scores.stream().filter(score -> score.compareTo(middle) < 0).count();
        TeamSkillMapDTO teamSkillMapDTO = new TeamSkillMapDTO();
        teamSkillMapDTO.setHigh((int) highCount);
        teamSkillMapDTO.setMiddle((int) middleCount);
        teamSkillMapDTO.setLow((int) lowCount);
        detail.setTeamSkillMap(teamSkillMapDTO);
        return new TeamSkillAvaDetailBuildDetail(detail, aveScore);
    }

    private record TeamSkillAvaDetailBuildDetail(TeamSkillAvaDetailVO detail, BigDecimal aveScore) {
    }

    @Nonnull
    private static List<BigDecimal> doHandleMultiScores(Map<String, List<BigDecimal>> userSkillScoreMap) {
        List<BigDecimal> scores = new ArrayList<>();
        // 处理一个人多个能力得分的情况
        for (Map.Entry<String, List<BigDecimal>> e : userSkillScoreMap.entrySet()) {
            List<BigDecimal> sList = e.getValue();
            if (CollectionUtils.isNotEmpty(sList)) {
                BigDecimal sum = BigDecimal.ZERO;
                for (BigDecimal s : sList) {
                    sum = sum.add(s);
                }
                // 求平均值返回
                BigDecimal aveValue = sum.divide(BigDecimal.valueOf(sList.size()), 4, RoundingMode.HALF_UP);
                scores.add(aveValue);
            } else {
                scores.add(BigDecimal.ZERO);
            }
        }
        return scores;
    }

    @Nonnull
    private static List<String> getSkillIds(String catalogId, Map<String, Integer> skillCatalogBzMap,
            Map<String, List<SkillModelItem>> skillCatalogMap) {
        List<String> skillIds;
        if (1 == skillCatalogBzMap.get(catalogId)) {
            // id编号转换
            skillIds = skillCatalogMap.get(catalogId).stream().map(SkillModelItem::getSkillNum)
                    .collect(Collectors.toList());
        } else {
            skillIds = skillCatalogMap.get(catalogId).stream().map(SkillModelItem::getSkillId)
                    .collect(Collectors.toList());
        }
        return skillIds;
    }

    @Nonnull
    private Set<String> getCatalogList(String orgId, List<ReportRecordEvaluationVo> reportRecordVos,
            Map<String, Map<String, List<BigDecimal>>> skillScoreMap, Map<String, BigDecimal> maxScoreMap,
            Map<String, Integer> skillCatalogBzMap, Map<String, List<SkillModelItem>> skillCatalogMap,
            Map<String, String> skillCatalogNameMap, Map<String, String> skillNameMap) {
        Set<String> catalogList = new HashSet<>();
        for (ReportRecordEvaluationVo reportRecordVo : reportRecordVos) {
            // 组装对象用于计算
            List<ReportRecordVo> evalResult = reportRecordVo.getList();
            if (CollectionUtils.isEmpty(evalResult)) {
                continue;
            }
            // 判断是否是倍智测评，做特殊兼容
            boolean isBz = StringUtils.equals("1", reportRecordVo.getProductType());
            doHandleEvalResult(evalResult, skillScoreMap, maxScoreMap, isBz);

            // 能力模型id获取
            String skillModelId = reportRecordVo.getModelId();
            SkillModelItemSearch modelItemSearch = new SkillModelItemSearch();
            modelItemSearch.setOrgId(orgId);
            modelItemSearch.setModelId(skillModelId);
            // 由于人才标注删除了老的接口，人才盘点也即将从老盘点迁移到新盘点，这些接口都不再需要，所以这里只简单注释掉无效方法使其不再报错，不做额外的兼容处理。 -- by zhangq 2025-01-06
//            List<SkillModelItem> skillModelMaps = gwnlFacade.getSkillModelList(modelItemSearch);
            List<SkillModelItem> skillModelMaps = new ArrayList<>();
            doHandleSkillModelMaps(skillModelMaps, isBz, skillCatalogBzMap, skillCatalogMap, skillCatalogNameMap);
            doHandleCatelogList(isBz, skillNameMap, skillModelMaps, catalogList);
        }
        return catalogList;
    }

    private static void doHandleCatelogList(boolean isBz, Map<String, String> skillNameMap,
            List<SkillModelItem> skillModelMaps, Set<String> catalogList) {
        if (isBz) {
            skillNameMap.putAll(
                    StreamUtil.list2map(skillModelMaps, SkillModelItem::getSkillNum, SkillModelItem::getSkillName));
        } else {
            skillNameMap.putAll(
                    StreamUtil.list2map(skillModelMaps, SkillModelItem::getSkillId, SkillModelItem::getSkillName));
        }
        catalogList.addAll(skillModelMaps.stream().map(SkillModelItem::getCatalogModelId).collect(Collectors.toSet()));
    }

    private static void doHandleSkillModelMaps(List<SkillModelItem> skillModelMaps, boolean isBz,
            Map<String, Integer> skillCatalogBzMap, Map<String, List<SkillModelItem>> skillCatalogMap,
            Map<String, String> skillCatalogNameMap) {
        for (SkillModelItem entity : skillModelMaps) {
            if (isBz) {
                skillCatalogBzMap.put(entity.getCatalogModelId(), 1);
            } else {
                skillCatalogBzMap.put(entity.getCatalogModelId(), 0);
            }
            if (null != skillCatalogMap.get(entity.getCatalogModelId())) {
                // 重复能力排除
                List<SkillModelItem> existList = skillCatalogMap.get(entity.getCatalogModelId());
                boolean isExist = false;
                for (SkillModelItem item : existList) {
                    if (item.getSkillId().equalsIgnoreCase(entity.getSkillId())) {
                        isExist = true;
                        break;
                    }
                }
                if (!isExist) {
                    skillCatalogMap.get(entity.getCatalogModelId()).add(entity);
                }
            } else {
                List<SkillModelItem> list = new ArrayList<>();
                list.add(entity);
                skillCatalogMap.put(entity.getCatalogModelId(), list);
            }
            skillCatalogNameMap.put(entity.getCatalogModelId(), entity.getSkillCatalogName());
        }
    }

    private static void doHandleEvalResult(List<ReportRecordVo> evalResult,
            Map<String, Map<String, List<BigDecimal>>> skillScoreMap, Map<String, BigDecimal> maxScoreMap,
            boolean isBz) {
        for (ReportRecordVo vo : evalResult) {
            List<ReportSkillIdScore> skillIdScores = vo.getSkillIdScore();

            getSkillIdScores(vo, skillIdScores, skillScoreMap);

            // 能力最大分是标准
            doHandleMaxScoreMap(skillIdScores, maxScoreMap, isBz);
        }
    }

    private static void doHandleMaxScoreMap(List<ReportSkillIdScore> skillIdScores, Map<String, BigDecimal> maxScoreMap,
            boolean isBz) {
        skillIdScores.forEach(skillIdScore -> {
            // 倍智最大分统一默认十分制
            if (null == maxScoreMap.get(skillIdScore.getSkillId()) && isBz) {
                maxScoreMap.put(skillIdScore.getSkillId(), BigDecimal.TEN);
            } else if (null == maxScoreMap.get(skillIdScore.getSkillId())) {
                maxScoreMap.put(skillIdScore.getSkillId(), skillIdScore.getMaxScore());
            }
        });
    }

    private static void getSkillIdScores(ReportRecordVo vo, List<ReportSkillIdScore> skillIdScores,
            Map<String, Map<String, List<BigDecimal>>> skillScoreMap) {
        Map<String, BigDecimal> scores = getStringBigDecimalMap(skillIdScores);
        for (Map.Entry<String, BigDecimal> entry : scores.entrySet()) {
            if (null != skillScoreMap.get(entry.getKey())) {
                Map<String, List<BigDecimal>> userSkillMap = skillScoreMap.get(entry.getKey());
                if (null != userSkillMap.get(vo.getUserId())) {
                    userSkillMap.get(vo.getUserId()).add(entry.getValue());
                } else {
                    List<BigDecimal> scoreList = new ArrayList<>();
                    scoreList.add(entry.getValue());
                    userSkillMap.put(vo.getUserId(), scoreList);
                }
            } else {
                List<BigDecimal> scoreList = new ArrayList<>();
                scoreList.add(entry.getValue());
                Map<String, List<BigDecimal>> userSkillMap = new HashMap<>(8);
                userSkillMap.put(vo.getUserId(), scoreList);
                skillScoreMap.put(entry.getKey(), userSkillMap);
            }
        }
    }

    private static @Nonnull Map<String, BigDecimal> getStringBigDecimalMap(
            List<ReportSkillIdScore> skillIdScores) {
        Map<String, BigDecimal> scores = new HashMap<>(8);
        for (ReportSkillIdScore reportSkillIdScore : skillIdScores) {
            if (null == reportSkillIdScore.getSynthesisScore() && null == scores.get(reportSkillIdScore.getSkillId())) {
                scores.put(reportSkillIdScore.getSkillId(), BigDecimal.ZERO);
            } else if (null == reportSkillIdScore.getSynthesisScore() && null != scores.get(
                    reportSkillIdScore.getSkillId())) {
                continue;
            } else {
                scores.put(reportSkillIdScore.getSkillId(), reportSkillIdScore.getSynthesisScore());
            }
        }
        return scores;
    }

    /**
     * 计算能力平均分
     *
     * @param scores 能力得分
     * @param max    能力最大分
     */
    private BigDecimal getSkillAverage(List<BigDecimal> scores, BigDecimal max) {
        BigDecimal result = new BigDecimal(0);
        if (CollectionUtils.isNotEmpty(scores)) {
            BigDecimal sum = scores.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            // 对统计结果做十分制转换
            // (a*10)/max+(b*10)/max = a*(10/max)+b*(10/max) =(a+b)*(10/max)
            sum = toTen(sum, max);
            result = sum.divide(BigDecimal.valueOf(scores.size()), 2, RoundingMode.HALF_UP);
        }
        return result;
    }

    /**
     * 计算能力平均分(不转十分制)
     *
     * @param scores
     */
    private BigDecimal getSkillAverage(List<BigDecimal> scores) {
        BigDecimal result = new BigDecimal(0);
        if (CollectionUtils.isNotEmpty(scores)) {
            BigDecimal sum = scores.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            // 对统计结果做十分制转换
            // (a*10)/max+(b*10)/max = a*(10/max)+b*(10/max) =(a+b)*(10/max)
            result = sum.divide(BigDecimal.valueOf(scores.size()), 2, RoundingMode.HALF_UP);
        }
        return result;
    }

    /**
     * 十分制转化，不丢进度
     *
     * @param score
     * @param max   能力最大分
     */
    private BigDecimal toTen(BigDecimal score, BigDecimal max) {
        if (max == null || max.compareTo(BigDecimal.ZERO) == 0 || max.compareTo(BigDecimal.ZERO) < 0) {
            throw new ApiException("能力最大分不能为空或者小于0");
        }
        return score.multiply(BigDecimal.valueOf(10)).divide(max, 2, RoundingMode.HALF_UP);
    }

    /**
     * 离散度
     *
     * @param scores 某一项能力人员的得分
     * @param ave    能力平均分
     */
    private BigDecimal getDispersion(List<BigDecimal> scores, BigDecimal ave, BigDecimal max) {
        BigDecimal result = new BigDecimal(0);
        if (CollectionUtils.isNotEmpty(scores)) {
            BigDecimal sum = new BigDecimal(0);
            for (BigDecimal s : scores) {
                // 差
                BigDecimal difference = toTen(s, max).subtract(ave);
                // 平方
                BigDecimal pow = pow(difference);
                // sum
                sum = sum.add(pow);
            }
            // 平均方差
            BigDecimal standardDeviation = sum.divide(BigDecimal.valueOf(scores.size()), 2, RoundingMode.HALF_UP);
            // 开根号得到离散度标准差
            result = BigDecimal.ZERO.compareTo(standardDeviation) == 0 ? BigDecimal.ZERO : sqrt(standardDeviation);
        }
        return result;
    }

    /**
     * 离散度（不转十分制）
     *
     * @param scores 某一项能力人员的得分
     * @param ave    能力平均分
     */
    private BigDecimal getDispersion(List<BigDecimal> scores, BigDecimal ave) {
        BigDecimal result = new BigDecimal(0);
        if (CollectionUtils.isNotEmpty(scores)) {
            BigDecimal sum = new BigDecimal(0);
            for (BigDecimal s : scores) {
                // 差
                BigDecimal difference = s.subtract(ave);
                // 平方
                BigDecimal pow = pow(difference);
                // sum
                sum = sum.add(pow);
            }
            // 平均方差
            BigDecimal standardDeviation = sum.divide(BigDecimal.valueOf(scores.size()), 2, RoundingMode.HALF_UP);
            // 开根号得到离散度标准差
            result = BigDecimal.ZERO.compareTo(standardDeviation) == 0 ? BigDecimal.ZERO : sqrt(standardDeviation);
        }
        return result;
    }

    /**
     * 算平方
     *
     * @param value
     */
    private BigDecimal pow(BigDecimal value) {
        return value.multiply(value);
    }

    /**
     * 牛顿迭代法计算平方根
     *
     * @param value 计算的值
     */
    private BigDecimal sqrt(BigDecimal value) {
        // 平方根
        BigDecimal sqrt = BigDecimal.valueOf(2);
        int precision = 100;
        MathContext mc = new MathContext(precision, RoundingMode.HALF_UP);
        BigDecimal deviation = value;
        int count = 0;
        while (count < precision) {
            deviation = (deviation.add(value.divide(deviation, mc))).divide(sqrt, mc);
            count++;
        }
        deviation = deviation.setScale(2, RoundingMode.HALF_UP);
        return deviation;
    }

    /**
     * 概览导出柱状图
     *
     * @param projectId
     * @param operator
     */
    public Map<String, String> exportResult(String projectId, UserCacheDetail operator) {
        String lockKey = format(RedisKeys.LK_PRJ_OVERVIEW_DEPT_GROUP_EXPT, operator.getOrgId(), projectId);
        String path;
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                PrjPO prj = prjMapper.selectByOrgIdAndId(operator.getOrgId(), projectId);
                if (prj == null) {
                    throw new ApiException(ExceptionKeys.PRJ_NOT_EXISTED);
                }
                DynamicExcelExportContent prjResult = handlePrjResult(operator.getOrgId(), projectId,
                        operator.getLocale());
                String fileName = "盘点项目人才分布表格" + DateTimeUtil.dateToString(new Date(),
                        DateTimeUtil.FORMATTER_YYYY_MM_DD_HH_MM_SS) + FileConstants.FILE_SUFFIX_XLSX;
                long taskId = dlcComponent.prepareExport(fileName, prjResultExportStrategy);
                path = dlcComponent.upload2Disk(fileName, prjResult, prjResultExportStrategy, taskId);
            } catch (Exception e) {
                log.error("LOG62790:盘点概览柱状图导出失败", e);
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
        Map<String, String> result = new HashMap<>(1);
        result.put("filePath", path);
        return result;
    }

    private DynamicExcelExportContent handlePrjResult(String orgId, String projectId, String lang) {
        // 组装导出结果
        DynamicExcelExportContent prjResult = new DynamicExcelExportContent();
        List<PrjResultVO> resultLabelList = prjResultService.getResultLabelList(orgId, lang);
        prjResult.setHeaders(initHeaders(resultLabelList));
        prjResult.setSheets(initSheets());
        prjResult.setData(initData(orgId, resultLabelList, projectId, lang));
        return prjResult;
    }

    private Map<String, List<Object>> initData(String orgId, List<PrjResultVO> resultLabelList, String projectId,
            String lang) {
        Map<String, List<Object>> result = new HashMap<>(8);
        List<Object> data = new ArrayList<>();

        List<PrjOverviewGroupDeptDTO> list = deptGroup(orgId, projectId, Collections.emptyList(), resultLabelList,
                lang);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(e -> {
                List<String> every = new ArrayList<>();
                every.add(e.getDeptName());
                List<PrjOverviewResultDTO> userResults = e.getUserResults();
                userResults.forEach(userResult -> {
                    every.add(String.valueOf(userResult.getLabelCount()));
                    every.add(userResult.getLabelPercent().setScale(2, RoundingMode.HALF_UP) + "%");
                });
                data.add(every);
            });
        }
        result.put(FileConstants.SHEET_1, data);
        return result;
    }

    private Map<String, List<List<String>>> initHeaders(List<PrjResultVO> resultLabelList) {
        Map<String, List<List<String>>> headerMap = new HashMap<>(8);
        List<List<String>> headers = new ArrayList<>();
        // 固定头
        Locale locale = authService.getLocale();
        //        Arrays.stream(HEADER_KEYS).forEach(key -> {
        //            String header = ApiUtil.getL10nString(msgSource, HEADER_PREFIX + key, locale);
        //            List<String> strings = new ArrayList<>();
        //            strings.add(header);
        //            headers.add(strings);
        //        });
        //部门
        List<String> deptHeader = new ArrayList<>();
        String dept = ApiUtil.getL10nString(msgSource, HEADER_PREFIX + "deptName", locale);
        deptHeader.add(dept);
        headers.add(deptHeader);
        //人才定义
        resultLabelList.forEach(label -> {
            Arrays.stream(HEADER_SUB_KEYS).forEach(key -> {
                String header = label.getLabelName() + ApiUtil.getL10nString(msgSource, HEADER_PREFIX + key, locale);
                List<String> strings = new ArrayList<>();
                strings.add(header);
                headers.add(strings);
            });
        });
        //其他人员数量
        List<String> others = new ArrayList<>();
        String header = ApiUtil.getL10nString(msgSource, HEADER_PREFIX + "others", locale);
        others.add(header);
        headers.add(others);
        //其他人员占比
        List<String> othersPct = new ArrayList<>();
        String headerPct = ApiUtil.getL10nString(msgSource, HEADER_PREFIX + "othersPct", locale);
        othersPct.add(headerPct);
        headers.add(othersPct);

        headerMap.put(FileConstants.SHEET_1, headers);
        return headerMap;
    }

    private List<IdName> initSheets() {
        List<IdName> sheets = new ArrayList<>();
        IdName idName = new IdName();
        idName.setId(FileConstants.SHEET_1);
        idName.setName("人才储备");
        sheets.add(idName);
        return sheets;
    }

    /**
     * 获取盘点项目下包含测评的维度数据
     *
     * @param orgId
     * @param projectId
     */
    public CommonList<IdNameSortVO> dimList(String orgId, String projectId) {
        CommonList<IdNameSortVO> result = new CommonList<>();
        // 获取所有的维度工具数据
        List<PrjDimConfPO> prjDimConfs = prjDimConfMapper.selectByOrgIdAndPrjId(orgId, projectId);
        if (CollectionUtils.isNotEmpty(prjDimConfs)) {
            List<PrjDimPO> prjDims = prjDimMapper.selectByOrgIdAndIfDimIdsOrderByCreateTime(orgId,
                    prjDimConfs.stream().map(PrjDimConfPO::getDimensionId).collect(Collectors.toList()));
            Map<String, String> dimMap = StreamUtil.list2map(prjDims, PrjDimPO::getId, PrjDimPO::getDimensionName);
            List<PrjDimConfToolPO> configTools = prjDimConfToolMapper.selectEvalBzImptByOrgIdAndDimConfIds(orgId,
                    prjDimConfs.stream().map(PrjDimConfPO::getId).collect(Collectors.toList()));
            Set<String> configIds = new HashSet<>();
            if (CollectionUtils.isNotEmpty(configTools)) {
                configTools = configTools.stream().filter(tool -> tool.getToolType() == 2 || tool.getToolType() == 4)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(configTools)) {
                    configIds = configTools.stream().map(PrjDimConfToolPO::getDimensionConfigId)
                            .collect(Collectors.toSet());
                }
            }
            Set<String> finalConfigIds = configIds;
            prjDimConfs.forEach(conf -> {
                if (finalConfigIds.contains(conf.getId()) || 3 == conf.getToolType()) {
                    // 查询维度
                    IdNameSortVO bean = new IdNameSortVO();
                    bean.setId(conf.getId());
                    bean.setName(null != dimMap.get(conf.getDimensionId()) ?
                            dimMap.get(conf.getDimensionId()) :
                            StringUtils.EMPTY);
                    bean.setSort(conf.getOrderIndex());
                    result.getDatas().add(bean);
                }
            });
        }
        return result;
    }
}
