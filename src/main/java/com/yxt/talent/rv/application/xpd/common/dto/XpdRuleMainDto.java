package com.yxt.talent.rv.application.xpd.common.dto;

import com.yxt.spsdk.common.component.ExpressionCalc;
import com.yxt.talent.rv.infrastructure.repository.xpd.DimGridLevelRuleDTO;
import com.yxt.talent.rv.infrastructure.repository.xpd.XpdLevelRuleDTO;
import lombok.Data;

import java.util.List;

/**
 * @Author: geyan
 * @Date: 14/12/24 09:51
 * @Description:
 **/
@Data
public class XpdRuleMainDto {
    private String id;

    /**
     * 计算方式:0-按维度结果计算 1-按指标结果计算
     */
    private Integer calcType;

    /**
     * 结果类型:0-维度分层结果 1-(维度/指标)得分 2-(维度/指标)达标率
     */
    private Integer resultType;

    /**
     * 计算规则:0-快捷配置 1-高级公式,当计算方式为<按指标结果计算>且结果类型为<指标得分>时有效
     */
    private Integer calcRule;

    /**
     * 计算规则表达式,当计算规则为<高级公式>时有效
     */
    private String formula;

    /**
     * 可视化的计算规则表达式,用于页面渲染,当计算规则为<高级公式>时有效
     */
    private String formulaDisplay;

    /**
     * 分层方式:0-按比例 1-按固定值
     */
    private Integer levelType;
    /**
     * 分层优先级:0-高等级优先 1-低等级优先
     */
    private Integer levelPriority;

    private ExpressionCalc formulaCalc;
}
