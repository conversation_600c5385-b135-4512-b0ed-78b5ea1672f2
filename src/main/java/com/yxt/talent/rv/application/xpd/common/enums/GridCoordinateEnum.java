package com.yxt.talent.rv.application.xpd.common.enums;

import com.yxt.common.exception.ApiException;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 宫格坐标枚举，用于处理不同类型宫格的格子编号与xy坐标的转换
 */
@Getter
public enum GridCoordinateEnum {

    NINE_GRID(GridTypeEnum.NINE_GRID.getValue(), new int[][]{
        {1, 1}, // 1号位
        {1, 2}, // 2号位
        {2, 1}, // 3号位
        {1, 3}, // 4号位
        {2, 2}, // 5号位
        {3, 1}, // 6号位
        {2, 3}, // 7号位
        {3, 2}, // 8号位
        {3, 3}  // 9号位
    }),

    FOUR_GRID(GridTypeEnum.FOUR_GRID.getValue(), new int[][]{
        {1, 1}, // 1号位
        {1, 2}, // 2号位
        {2, 1}, // 3号位
        {2, 2}  // 4号位
    }),

    SIXTEEN_GRID(GridTypeEnum.SIXTEEN_GRID.getValue(), new int[][]{
        {1, 1}, // 1号位
        {1, 2}, // 2号位
        {2, 1}, // 3号位
        {1, 3}, // 4号位
        {2, 2}, // 5号位
        {3, 1}, // 6号位
        {1, 4}, // 7号位
        {2, 3}, // 8号位
        {3, 2}, // 9号位
        {4, 1}, // 10号位
        {2, 4}, // 11号位
        {3, 3}, // 12号位
        {4, 2}, // 13号位
        {3, 4}, // 14号位
        {4, 3}, // 15号位
        {4, 4}  // 16号位
    });

    private final int gridType;
    private final int[][] numberToPosition;
    private final int rows;
    private final int cols;

    GridCoordinateEnum(int gridType, int[][] numberToPosition) {
        this.gridType = gridType;
        this.numberToPosition = numberToPosition;
        // 计算最大的x和y值作为行列数
        this.rows = getMaxValue(numberToPosition, 1);
        this.cols = getMaxValue(numberToPosition, 0);
    }

    /**
     * 根据gridType获取枚举
     * @return
     */
    public static GridCoordinateEnum getByGridType(int gridType) {
        for (GridCoordinateEnum grid : values()) {
            if (grid.gridType == gridType) {
                return grid;
            }
        }
        throw new ApiException("Invalid grid type: " + gridType);
    }

    /**
     * 返回格子的xy轴字符串拼接
     * @param gridType
     * @return
     */
    public static List<String> getXyCellJoin(int gridType, String joiner) {
        GridCoordinateEnum grid = getByGridType(gridType);
        int[][] ints = grid.getNumberToPosition();

        List<String> cellJoin = new ArrayList<>();
        for (int[] anInt : ints) {
            cellJoin.add(anInt[0] + joiner + anInt[1]);
        }
        return cellJoin;
    }

    private int getMaxValue(int[][] array, int index) {
        int max = 0;
        for (int[] pos : array) {
            max = Math.max(max, pos[index]);
        }
        return max;
    }

    public Position getPositionByNumber(int cellNumber) {
        if (cellNumber < 1 || cellNumber > numberToPosition.length) {
            throw new ApiException("Invalid cell number for grid type: " + this.name());
        }
        int[] pos = numberToPosition[cellNumber - 1];
        return new Position(pos[0], pos[1]);
    }

    public int getNumberByPosition(Position position) {
        if (position.x() < 1 || position.x() > cols || 
            position.y() < 1 || position.y() > rows) {
            throw new ApiException("Invalid coordinates for grid type: " + this.name());
        }

        for (int i = 0; i < numberToPosition.length; i++) {
            if (numberToPosition[i][0] == position.x() && 
                numberToPosition[i][1] == position.y()) {
                return i + 1;
            }
        }
        throw new ApiException("Invalid position for grid type: " + this.name());
    }

    public record Position(int x, int y) {
        @Override
        public String toString() {
            return String.format("(x=%d, y=%d)", x, y);
        }
    }

}
