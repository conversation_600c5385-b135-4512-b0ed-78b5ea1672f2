package com.yxt.talent.rv.infrastructure.repository.xpd;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdImportIndicatorUserMapper;
import com.yxt.talent.rv.infrastructure.repository.xpd.bean.XpdIndicatorResultDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Repository
public class XpdImportIndicatorUserRepo {
    private final XpdImportIndicatorUserMapper importIndicatorUserMapper;

    public List<XpdIndicatorResultDto> queryByUserIds(
        String orgId, String xpdId, String importId,
        List<String> userIds,List<String> indicatorIds) {
        if (StringUtils.isAnyEmpty(orgId, xpdId, importId)
            || CollectionUtils.isEmpty(userIds)
            || CollectionUtils.isEmpty(indicatorIds)) {
            return Lists.newArrayList();
        }
        return importIndicatorUserMapper.queryByUserIds(orgId, xpdId, importId, userIds, indicatorIds);
    }
}
