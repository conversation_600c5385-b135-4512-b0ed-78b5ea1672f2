package com.yxt.talent.rv.infrastructure.common.utilities.util;

import com.yxt.talent.rv.controller.manage.xpd.rule.enums.ScoreSystemEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static com.yxt.talent.rv.controller.manage.xpd.rule.enums.ScoreSystemEnum.*;

/**
 * 分制转换工具类
 *
 * <AUTHOR>
 */
public class ScoreSystemUtil {

    private static final Logger log = LoggerFactory.getLogger(ScoreSystemUtil.class);

    /**
     * 将原始分值转换为指定分制的分值
     *
     * @param originalScore     原始分值
     * @param maxOriginalScore  原始分值的最大值
     * @param targetScoreSystem 目标分制
     * @return 转换后的分值
     */
    public static BigDecimal convertToTargetSystem(
        BigDecimal originalScore, BigDecimal maxOriginalScore, Integer targetScoreSystem) {
        if (originalScore == null || maxOriginalScore == null || maxOriginalScore.compareTo(BigDecimal.ZERO) <= 0) {
            log.debug("LOG20773:");
            return originalScore;
        }

        // 如果是原始分值，直接返回
        if (targetScoreSystem == null || targetScoreSystem == ORIGINAL.getCode()) {
            log.debug("LOG20793:");
            return originalScore;
        }

        // 获取目标分制的最大分值
        BigDecimal maxTargetScore = getMaxScore(targetScoreSystem, maxOriginalScore);

        // 计算转换后的分值：原始分值 / 原始最大分值 * 目标最大分值
        return originalScore.multiply(maxTargetScore).divide(maxOriginalScore, 2, RoundingMode.HALF_UP);
    }

    /**
     * 将指定分制的分值转换为原始分值
     *
     * @param targetScore       目标分制的分值
     * @param maxOriginalScore  原始分值的最大值
     * @param sourceScoreSystem 源分制
     * @return 转换后的原始分值
     */
    public static BigDecimal convertToOriginalSystem(
        BigDecimal targetScore, BigDecimal maxOriginalScore, Integer sourceScoreSystem) {
        if (targetScore == null || maxOriginalScore == null || maxOriginalScore.compareTo(BigDecimal.ZERO) <= 0) {
            return targetScore;
        }

        // 如果是原始分值，直接返回
        if (sourceScoreSystem == null || sourceScoreSystem == ORIGINAL.getCode()) {
            return targetScore;
        }

        // 获取源分制的最大分值
        BigDecimal maxSourceScore = getMaxScore(sourceScoreSystem, maxOriginalScore);

        // 计算转换后的分值：目标分值 / 目标最大分值 * 原始最大分值
        return targetScore.multiply(maxOriginalScore).divide(maxSourceScore, 2, RoundingMode.HALF_UP);
    }

    /**
     * 检查分值是否超过指定分制的最大值
     *
     * @param score       分值
     * @param scoreSystem 分制
     * @return 是否超过最大值
     */
    public static boolean isScoreExceedMaxValue(BigDecimal score, Integer scoreSystem) {
        if (score == null) {
            return false;
        }

        BigDecimal maxScore = getMaxScore(scoreSystem, BigDecimal.valueOf(Integer.MAX_VALUE));
        return score.compareTo(maxScore) > 0;
    }
}
