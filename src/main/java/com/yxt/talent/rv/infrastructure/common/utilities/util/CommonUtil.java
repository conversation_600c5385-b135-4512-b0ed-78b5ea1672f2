package com.yxt.talent.rv.infrastructure.common.utilities.util;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.yxt.common.Constants;
import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.StringUtil;
import com.yxt.common.util.Validate;
import com.yxt.export.I18nComponent;
import com.yxt.modelhub.api.bean.dto.search.AmSearchDTO;
import com.yxt.modelhub.api.utils.QueryUtil;
import com.yxt.talent.rv.infrastructure.common.enums.KeywordTypeEnum;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@UtilityClass
public final class CommonUtil {

    public static final String PRODUCT_CODE = "xxv2";
    public static final String SQL_ORDER_ASC = "ASC";
    private static I18nComponent i18nComponent;

    @Nullable
    public static <T> Collection<T> filterNull(@Nullable Collection<T> coll) {
        if (CollectionUtils.isEmpty(coll)) {
            return coll;
        }
        try {
            coll.removeIf(Objects::isNull);
        } catch (UnsupportedOperationException e) {
            // 如果抛出UnsupportedOperationException，表示这是一个不可变集合，直接返回原集合
            return coll;
        }
        return coll;
    }

    /**
     * 过滤null值和空值
     *
     * @param coll
     * @return
     */
    @Nonnull
    public static Collection<String> filterNullAndBlank(Collection<String> coll) {
        if (coll == null) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(coll)) {
            return coll;
        }
        try {
            coll.removeIf(StringUtils::isBlank);
        } catch (UnsupportedOperationException e) {
            return coll;
        }
        return coll;
    }

    /**
     * 去重
     *
     * @param coll
     * @param <T>
     * @return
     */
    @Nonnull
    public static <T> Collection<T> distinct(Collection<T> coll) {
        if (coll == null) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(coll)) {
            return coll;
        }
        try {
            return coll.stream().distinct().collect(Collectors.toList());
        } catch (UnsupportedOperationException e) {
            log.error("LOG14135:", e);
            return coll;
        }
    }

    public static PageRequest getPageRequest(
            int limit, int offset, String orderBy, String direction) {
        if (limit > Constants.MAX_LIMIT) {
            limit = Constants.MAX_LIMIT;
        } else if (limit <= 0) {
            limit = Constants.DEFAULT_LIMIT;
        }

        int current;
        current = (offset + limit) / limit;
        if (current <= 0) {
            current = Constants.DEFAULT_CURRENT_PAGE;
        }
        direction = StringUtils.equalsIgnoreCase(direction, Constants.PARAM_NAME_DIRECTION_ASC) ?
                Constants.PARAM_NAME_DIRECTION_ASC : Constants.PARAM_NAME_DIRECTION_DESC;
        return new PageRequest(current, limit, orderBy, direction);
    }

    public static OrderItem generateOrderItem(PageRequest pageRequest) {
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn(StringUtil.camelToUnderline(pageRequest.getOrderBy()));
        if (StringUtils.isNotBlank(pageRequest.getDirection())) {
            orderItem.setAsc(
                    StringUtils.equalsIgnoreCase(pageRequest.getDirection(), SQL_ORDER_ASC));
        } else {
            orderItem.setAsc(Boolean.FALSE);
        }
        return orderItem;
    }

    public static void execAfterCommitIfHas(Runnable runnable) {
        if (runnable == null) {
            return;
        }
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            log.debug("LOG10740:");
                            runnable.run();
                        }
                    });
        } else {
            runnable.run();
        }
    }

    public static List<Long> str2Long(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return ids.stream()
                .filter(Objects::nonNull)
                .filter(StringUtils::isNotBlank)
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    public static UserCacheDetail getUserCache() {
        UserCacheDetail cacheDetail = new UserCacheDetail();
        cacheDetail.setOrgId("ac0a267c-044e-4a28-b489-6be16e0ab788");
        cacheDetail.setUserId("d1b80c4e-dc7f-4c6e-90d2-c244d972a4ae");
        return cacheDetail;
    }

    /**
     * 主动打印当前线程的堆栈
     */
    public static void printCurrentStackTrace() {
        StackTraceElement[] stackTraceElements = Thread.currentThread().getStackTrace();
        StringBuilder sb = new StringBuilder("Current stack trace:\n");
        for (StackTraceElement element : stackTraceElements) {
            sb.append("\tat ").append(element).append("\n");
        }
        log.info("LOG11555:{}", sb);
    }

    /**
     * 类型转换
     *
     * @param list
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T, R> Collection<R> typeExchange(Collection<T> list, Function<T, R> exchange) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(exchange).collect(Collectors.toList());
    }

    /**
     * 字典key类型转换
     *
     * @param sourceMap
     * @param converter
     * @param <K1>
     * @param <K2>
     * @param <V>
     * @return
     */
    public static <K1, K2, V> Map<K2, V> convertKey(
            Map<K1, V> sourceMap, Function<K1, K2> converter) {
        Map<K2, V> targetMap = new HashMap<>();
        sourceMap.forEach((k, v) -> targetMap.put(converter.apply(k), v));
        return targetMap;
    }

    public static <T, R> List<R> mapListThenFilterNull(
            Collection<T> datas, Function<T, R> mapFunction) {
        if (CollectionUtils.isEmpty(datas)) {
            return new ArrayList<>();
        }
        return datas.stream()
                .filter(Objects::nonNull)
                .map(mapFunction)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static <T, R> Set<R> mapSetThenFilterNull(
            Collection<T> datas, Function<T, R> mapFunction) {
        if (CollectionUtils.isEmpty(datas)) {
            return new HashSet<>();
        }
        return datas.stream()
                .filter(Objects::nonNull)
                .map(mapFunction)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 提取集合中的每个元素中的子集合元素
     *
     * @param entities
     * @param subListFn
     * @param <T>
     * @param <R>
     * @return
     */
    @Nonnull
    public static <T, R> Collection<R> mapSubList(
            @Nonnull Collection<T> entities, @Nonnull Function<T, Collection<R>> subListFn) {
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }
        return entities.stream()
                .map(subListFn)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }


    /**
     * 提取集合中每个元素对象中的子元素集合对象中的指定属性
     *
     * @param entities
     * @param subListFn
     * @param mapSubFn
     * @param <T>
     * @param <R>
     * @param <O>
     * @return
     */
    @Nonnull
    public static <T, R, O> Collection<O> mapSubListSub(
            @Nonnull Collection<T> entities, @Nonnull Function<T, Collection<R>> subListFn,
            @Nonnull Function<R, O> mapSubFn) {
        return mapListThenFilterNull(mapSubList(entities, subListFn), mapSubFn);
    }

    /**
     * 提取集合中每个元素对象中的子元素对象中的指定属性
     *
     * @param entities
     * @param subListFn
     * @param mapSubFn
     * @param <T>
     * @param <R>
     * @param <O>
     * @return
     */
    @Nonnull
    public static <T, R, O> Collection<O> mapSubSub(
            @Nonnull Collection<T> entities, @Nonnull Function<T, R> subListFn,
            @Nonnull Function<R, O> mapSubFn) {
        return entities.stream()
                .map(subListFn)
                .filter(Objects::nonNull)
                .map(mapSubFn)
                .collect(Collectors.toList());
    }


    /**
     * 提取集合中的每个元素中的子集合元素
     *
     * @param datas  数据，为空时返回空Map
     * @param mapKey
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T, R> Map<R, T> listMap(@Nullable Collection<T> datas, Function<T, R> mapKey) {
        if (CollectionUtils.isEmpty(datas)) {
            return new LinkedHashMap<>();
        }
        return datas.stream()
                .collect(Collectors.toMap(mapKey, Function.identity(), (key1, key2) -> key2));
    }

    @Nullable
    public static Integer double2Int(Double value) {
        if (value == null) {
            return null;
        }
        return value.intValue();
    }

    /**
     * 检查指定拼接字符串中是否包含指定的字符串片段
     *
     * @param source   拼接字符串
     * @param split    字符串分隔符
     * @param fragment 要查找的字符片段
     */
    public static boolean contains(String source, String split, String fragment) {
        if (StringUtils.isBlank(source) || StringUtils.isBlank(fragment)) {
            return false;
        }
        String[] splitArray = source.split(split);
        for (String s : splitArray) {
            if (StringUtils.equals(s, fragment)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 分割列表
     *
     * @param list 待分割列表
     * @param len  分割长度
     */
    public static <T> List<List<T>> splitList(List<T> list, int len) {
        if (list == null || list.isEmpty() || len < 1) {
            return new ArrayList<>();
        }

        List<List<T>> result = new ArrayList<>();

        int size = list.size();
        int count = (size + len - 1) / len;

        for (int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * len, (Math.min((i + 1) * len, size)));
            result.add(subList);
        }

        return result;
    }

    /**
     * 记录异常堆栈信息，只包含项目代码
     *
     * @param e
     * @return
     */
    public static String extractErrMsg(Exception e) {
        try {
            StringBuilder stackTrace = new StringBuilder();
            if (e instanceof NullPointerException) {
                stackTrace.append("NPE").append("\n");
            } else {
                stackTrace.append(e.getMessage()).append("\n");
            }
            StackTraceElement[] stackElements = e.getStackTrace();
            int maxStackTraceElements = Math.min(stackElements.length, 50);
            int serializedLength = 0;

            for (int i = 0; i < maxStackTraceElements; i++) {
                String stackElement = stackElements[i].toString();
                if (stackElement.startsWith("com.yxt")) {
                    stackTrace.append(stackElement).append("\n");
                    serializedLength += stackElement.length() + 1; // +1 for newline character

                    if (serializedLength >= 500) {
                        break;
                    }
                }
            }
            return stackTrace.toString();
        } catch (Exception e1) {
            log.error("LOG60390:", e1);
        }
        return "";
    }

    public static boolean checkIsNotBlank4Json(String customJson) {
        return !StringUtils.isEmpty(customJson) && !"[{}]".equals(customJson) &&
               !"[]".equals(customJson);
    }

    /**
     * 获取第一个不为null的参数, 采用可变参数
     */
    @Nullable
    public static <T> T getFirstNotNull(T... args) {
        for (T arg : args) {
            if (arg != null) {
                return arg;
            }
        }
        return null;
    }

    public static String null2BlankTrim(String content) {
        return content == null ? "" : content.trim();
    }

    public static Object null2Blank(Object content) {
        return content == null ? "" : content;
    }

    public static Paging getPaging() {
        HttpServletRequest request = ApiUtil.getRequestByContext();
        Validate.isNotNull(request, "request is null");

        int limit = StringUtil.str2Int(request.getParameter(Constants.PARAM_NAME_LIMIT), Constants.DEFAULT_LIMIT);

        if (limit <= 0) {
            limit = Constants.DEFAULT_LIMIT;
        }

        int current;
        if (StringUtils.isNotBlank(request.getParameter(Constants.PARAM_NAME_OFFSET))) {
            int offset = StringUtil.str2Int(request.getParameter(Constants.PARAM_NAME_OFFSET), 0);
            current = (offset + limit) / limit;
        } else {
            current = StringUtil
                .str2Int(request.getParameter(Constants.PARAM_NAME_CURRENT), Constants.DEFAULT_CURRENT_PAGE);
        }
        if (current <= 0) {
            current = Constants.DEFAULT_CURRENT_PAGE;
        }
        String orderBy = request.getParameter(Constants.PARAM_NAME_ORDERBY);
        String direction = request.getParameter(Constants.PARAM_NAME_DIRECTION);
        direction = StringUtils.equalsIgnoreCase(direction, Constants.PARAM_NAME_DIRECTION_ASC) ?
            Constants.PARAM_NAME_DIRECTION_ASC :
            Constants.PARAM_NAME_DIRECTION_DESC;

        Paging paging = new Paging();
        paging.setLimit(limit);
        paging.setOffset((long) (current - 1) * limit);
        return paging;
    }


    // 获取交集或处理空列表情况

    /**
     * 获取两个List交集
     *
     * @param list1
     * @param list2
     * @return
     */
    public static List<String> getIntersectionOrList(List<String> list1, List<String> list2) {
        if (list1 == null || list1.isEmpty()) {
            return list2 != null ? new ArrayList<>(list2) : new ArrayList<>();
        }
        if (list2 == null || list2.isEmpty()) {
            return new ArrayList<>(list1);
        }
        // 复制第一个列表
        List<String> result = new ArrayList<>(list1);
        // 保留与第二个列表相同的元素
        result.retainAll(list2);
        return result;
    }

    public static I18nComponent getI18nComponent() {
        if (i18nComponent == null) {
            i18nComponent = SpringContextHolder.getBean(I18nComponent.class);
        }
        return i18nComponent;
    }

    /**
     * 获取搜索关键字类型
     *
     * @see KeywordTypeEnum
     * @param search
     * @return
     */
    public static int getKeywordType(QueryUtil.Search search) {
        String keyworkType = null;
        AmSearchDTO query = search.getSearch();
        if (query == null) {
            return KeywordTypeEnum.UNKNOWN.getCode();
        }
        List<AmSearchDTO.MetaField> relatedField = query.getRelatedField();
        if (CollectionUtils.isNotEmpty(relatedField)) {
            AmSearchDTO.MetaField metaField = relatedField.iterator().next();
            if (CollectionUtils.isNotEmpty(metaField.getRelatedMetaCode())) {
                keyworkType = metaField.getRelatedMetaCode().iterator().next();
            }
        }
        return KeywordTypeEnum.of(keyworkType).getCode();
    }
}
