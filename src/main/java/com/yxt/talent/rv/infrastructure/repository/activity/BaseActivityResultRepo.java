package com.yxt.talent.rv.infrastructure.repository.activity;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.RvActivityArrangeItemMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.RvBaseActivityResultMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;

import java.util.List;

@RequiredArgsConstructor
@Repository
public class BaseActivityResultRepo {
    private final RvBaseActivityResultMapper rvBaseActivityResultMapper;
    private final RvActivityArrangeItemMapper rvActivityArrangeItemMapper;

    public List<String> doneUserIds(String orgId, String aomProjId, String refId, List<String> userIds) {
        if (StringUtils.isAnyEmpty(orgId, aomProjId, refId) || CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        Long itemId = rvActivityArrangeItemMapper.getItemIdByRefId(orgId, aomProjId, refId);
        if (itemId == null) {
            return Lists.newArrayList();
        }
        return rvBaseActivityResultMapper.doneUserIdsByItemId(orgId, aomProjId, itemId, userIds);
    }

    public List<String> doneUserIds(
        String orgId,
        int actvType, String actvId,
        List<String> userIds) {
        if (StringUtils.isAnyEmpty(orgId, actvId) || CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        return rvBaseActivityResultMapper.doneUserIds(orgId, actvType, actvId, userIds);
    }
}
