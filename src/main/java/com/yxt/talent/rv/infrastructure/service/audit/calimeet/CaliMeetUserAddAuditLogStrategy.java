package com.yxt.talent.rv.infrastructure.service.audit.calimeet;

import com.yxt.auditlog.AuditLog;
import com.yxt.auditlog.AuditLogContext;
import com.yxt.auditlog.bean.AuditDetail;
import com.yxt.auditlog.bean.EntityChange;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetUserBatchAddCmd;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;

@Slf4j
@Service
@Scope("prototype")
@RequiredArgsConstructor
public class CaliMeetUserAddAuditLogStrategy implements AuditLogStrategy {
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final CaliMeetMapper caliMeetMapper;


    @Override
    public void doExecute(AuditLog auditLog, String[] argsNames, Object[] args, Object result) {
        if (log.isInfoEnabled()) {
            log.info("LOG63390:{}", BeanHelper.bean2Json(argsNames, ALWAYS));
        }
        if (args == null || args.length == 0) {
            log.error("LOG63420:{}", this.getClass().getName() + " : 日志记录失败，参数列表为空");
            return;
        }

        CaliMeetUserBatchAddCmd bean = (CaliMeetUserBatchAddCmd) args[1];
        if (bean == null) {
            log.error("LOG63410:{}", this.getClass().getName() + " : 日志记录失败，参数列表为空");
            return;
        }
        CaliMeetPO cm = caliMeetMapper.selectByIdAndOrgId(bean.getMeetingId(), auditLog.getOrgId());
        log.debug("LOG63400:cls={}, 业务Id={}, orgId={}", this.getClass().getName(),
                bean.getMeetingId(),
                auditLog.getOrgId());
        String bizName = bean.getMeetingId(); // 极端情况，从库无数据，就用业务Id做名字
        if (cm != null) {
            bizName = cm.getMeetName();
        }

        // 组装日志
        List<EntityChange> list = getEntityChangeList(bean, auditLog.getOrgId());
        AuditDetail ad = getCreateAuditDetail(AuditLogHelper.Module.CALIBRATION.getCode());
        ad.setEntityId(bean.getMeetingId());
        ad.setEntityName(
                AuditLogHelper.Module.CALIBRATION.getName() + "-" + bizName + "-添加人员");
        ad.setChanges(list);
        auditLog.setDetails(Collections.singletonList(ad));
        AuditLogContext.asyncCommit(auditLog);
    }

    private List<EntityChange> getEntityChangeList(CaliMeetUserBatchAddCmd bean, String orgId) {
        List<EntityChange> result = new ArrayList<>();
        // 人才委员会
        result.add(new EntityChange().setFieldName("盘点人员")
                .setNewValue(getUserString(orgId, bean.getUserIdList())));
        return result;
    }

    private String getUserString(String orgId, List<String> userIds) {
        List<UdpLiteUserPO> tcList = udpLiteUserMapper.selectByUserIds(orgId, userIds);
        StringBuilder tcSb = new StringBuilder();
        tcList.forEach(a -> tcSb.append(a.getFullname())
                .append("（")
                .append(a.getUsername())
                .append("）")
                .append("，"));

        if (!tcSb.isEmpty()) {
            tcSb.delete(tcSb.length() - 1, tcSb.length());
        }

        return tcSb.toString();
    }
}
