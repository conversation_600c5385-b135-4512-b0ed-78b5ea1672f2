package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity;

import cn.hutool.core.lang.Opt;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfResultConfPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityPerfResultConfMapper extends CommonMapper<ActivityPerfResultConfPO> {
    int insert(ActivityPerfResultConfPO record);

    int insertOrUpdate(ActivityPerfResultConfPO record);

    ActivityPerfResultConfPO selectByPrimaryKey(String id);

    int insertList(@Param("list") List<ActivityPerfResultConfPO> list);

    List<ActivityPerfResultConfPO> selectByActivityId(@Param("orgId") String orgId,
            @Param("actvPerfId") String actvPerfId);

    void deleteByActivityId(@Param("orgId") String orgId, @Param("actvPerfId") String perfActivityId);

    List<ActivityPerfResultConfPO> findByOrgIdAndIds(@Param("orgId") String orgId,
            @Param("perfConfIds") List<String> perfConfIds);
}