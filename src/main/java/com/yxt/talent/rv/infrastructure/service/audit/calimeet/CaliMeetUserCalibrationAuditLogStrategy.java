package com.yxt.talent.rv.infrastructure.service.audit.calimeet;

import com.yxt.auditlog.AuditLog;
import com.yxt.auditlog.AuditLogContext;
import com.yxt.auditlog.bean.AuditDetail;
import com.yxt.auditlog.bean.EntityChange;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.application.calimeet.legacy.CaliMeetAppService;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetUserUpdateCmd;
import com.yxt.talent.rv.domain.prj.entity.user.PrjUserResult;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogStrategy;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static java.util.Objects.requireNonNull;

@Slf4j
@Service
@Scope("prototype")
@RequiredArgsConstructor
public class CaliMeetUserCalibrationAuditLogStrategy implements AuditLogStrategy {
    private final AuthService authService;
    private final CaliMeetAppService caliMeetAppService;
    private final PrjDimMapper prjDimMapper;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final CaliMeetMapper caliMeetMapper;
    private final CaliMeetUserMapper caliMeetUserMapper;
    @jakarta.annotation.Nullable
    private CaliMeetUserPO oldCaliMeetUser;
    @jakarta.annotation.Nullable
    private List<CaliMeetUserResultPO> oldCmResult;

    @Override
    public void doPrepare(String[] argsNames, Object[] args) {

        HttpServletRequest request = (HttpServletRequest) args[0];
        UserCacheBasic operator = authService.getUserCacheBasic(request);

        CaliMeetUserUpdateCmd bean = (CaliMeetUserUpdateCmd) args[1];
        if (bean == null || operator == null) {
            log.warn("LOG20110:{} : 日志记录失败，参数列表为空", this.getClass().getName());
            return;
        }

        oldCaliMeetUser = caliMeetUserMapper.selectByIdAndOrgId(bean.getId(), operator.getOrgId());
        if (oldCaliMeetUser == null) {
            log.warn("LOG20100:{} : oldMeetingUser is null", this.getClass().getName());
            return;
        }
        oldCmResult = caliMeetAppService.findByMeetingIdAndUserIds(
                operator.getOrgId(),
                oldCaliMeetUser.getMeetingId(),
                Collections.singletonList(oldCaliMeetUser.getUserId()));
    }

    @Override
    public void doExecute(AuditLog auditLog, String[] argsNames, Object[] args, Object result) {
        if (log.isInfoEnabled()) {
            log.info("LOG63430:{}", BeanHelper.bean2Json(argsNames, ALWAYS));
        }
        if (args == null || args.length == 0) {
            log.error("LOG20120:{} : 日志记录失败，参数列表为空", this.getClass().getName());
            return;
        }

        if (oldCaliMeetUser == null) {
            log.error("LOG20130:{} : oldMeetingUser is null", this.getClass().getName());
            return;
        }

        String orgId = oldCaliMeetUser.getOrgId();
        CaliMeetPO cm = caliMeetMapper.selectByIdAndOrgId(oldCaliMeetUser.getMeetingId(), orgId);
        log.debug("LOG20140:{} : 业务Id = {} orgId = {}", this.getClass().getName(),
                Objects.requireNonNull(oldCaliMeetUser).getMeetingId(), auditLog.getOrgId());
        String bizName = oldCaliMeetUser.getMeetingId(); // 极端情况，从库无数据，就用业务Id做名字
        if (cm != null) {
            bizName = cm.getMeetName();
        }

        List<String> userIds = Collections.singletonList(oldCaliMeetUser.getUserId());
        List<UdpLiteUserPO> userByIds = udpLiteUserMapper.selectByUserIds(orgId, userIds);

        if (CollectionUtils.isEmpty(userByIds)) {
            log.error("LOG20150:{}", requireNonNull(oldCaliMeetUser).getUserId());
            return;
        }

        // 组装日志
        List<EntityChange> list = getEntityChangeList();
        removeUnchangedProperty(list);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<EntityChange> suggestionList =
                list.stream().filter(a -> "suggestion".equals(a.getField()))
                        .collect(Collectors.toList());
        List<EntityChange> userCalibrationList =
                list.stream().filter(a -> "userCalibration".equals(a.getField()))
                        .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(suggestionList)) {
            AuditDetail ad = getUpdateAuditDetail(AuditLogHelper.Module.CALIBRATION.getCode());
            ad.setEntityId(oldCaliMeetUser.getMeetingId());
            ad.setEntityName(
                    AuditLogHelper.Module.CALIBRATION.getName() + "-" + bizName + "-发展建议-" +
                    userByIds.get(0).getFullname());
            ad.setChanges(suggestionList);
            auditLog.addDetail(ad);
        }

        if (CollectionUtils.isNotEmpty(userCalibrationList)) {
            AuditDetail ad = getUpdateAuditDetail(AuditLogHelper.Module.CALIBRATION.getCode());
            String meetName = "";
            if (cm != null) {
                ad.setEntityId(cm.getId());
                meetName = cm.getMeetName();
            } else {
                log.warn("LOG10420:");
            }
            ad.setEntityName(
                    String.format("%s-%s-人员校准-%s", AuditLogHelper.Module.CALIBRATION.getName(),
                            meetName, userByIds.get(0).getFullname()));
            ad.setChanges(userCalibrationList);
            auditLog.addDetail(ad);
        }

        AuditLogContext.asyncCommit(auditLog);
    }

    @DbHintMaster
    private List<EntityChange> getEntityChangeList() {

        CaliMeetUserPO newCaliMeetUser = null;
        if (oldCaliMeetUser != null) {
            newCaliMeetUser = caliMeetUserMapper.selectByIdAndOrgId(
                    oldCaliMeetUser.getId(),
                    oldCaliMeetUser.getOrgId());
        }
        if (newCaliMeetUser == null) {
            log.warn("LOG20160:");
            return Collections.emptyList();
        }
        List<CaliMeetUserResultPO> newCmResult =
                caliMeetAppService.findByMeetingIdAndUserIds(
                        oldCaliMeetUser.getOrgId(),
                        oldCaliMeetUser.getMeetingId(),
                        Collections.singletonList(oldCaliMeetUser.getUserId()));

        Set<String> dimIdSet = newCmResult.stream()
                .map(CaliMeetUserResultPO::getDimensionId)
                .collect(Collectors.toSet());
        List<PrjDimPO> prjDimList =
                prjDimMapper.selectByOrgIdAndIfDimIdsOrderByCreateTime(
                        oldCaliMeetUser.getOrgId(), dimIdSet);
        Map<String, String> dimNameMap = prjDimList.stream()
                .collect(Collectors.toMap(PrjDimPO::getId, PrjDimPO::getDimensionName,
                        (k1, k2) -> k1));

        List<EntityChange> result = new ArrayList<>();
        result.add(new EntityChange().setFieldName("发展建议")
                .setField("suggestion")
                .setFieldPath(oldCaliMeetUser.getClass().getName() + ".suggestion")
                .setNewValue(newCaliMeetUser.getSuggestion())
                .setOldValue(oldCaliMeetUser.getSuggestion()));

        String newDimensionValue = getDimensionValue(dimNameMap, newCmResult);
        String oldDimensionValue = getDimensionValue(dimNameMap, oldCmResult);
        result.add(new EntityChange().setFieldName("人员校准")
                .setField("userCalibration")
                .setFieldPath(oldCaliMeetUser.getClass().getName() + ".userCalibration")
                .setNewValue(newDimensionValue)
                .setOldValue(oldDimensionValue));

        return result;
    }

    private String getDimensionValue(
            Map<String, String> dimNameMap, @Nullable List<CaliMeetUserResultPO> result) {
        if (CollectionUtils.isEmpty(result)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        Map<Integer, String> nameEnumMap = PrjUserResult.Level.getCodeNameMap();
        for (int i = 0; i < result.size(); i++) {
            CaliMeetUserResultPO temp = result.get(i);
            String dName = dimNameMap.get(temp.getDimensionId());
            String value = nameEnumMap.get(temp.getCalibrationLevel());
            sb.append(dName).append("：").append(value);
            if (i < result.size() - 1) {
                sb.append("；");
            }
        }
        return sb.toString();
    }
}
