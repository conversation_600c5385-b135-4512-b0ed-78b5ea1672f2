package com.yxt.talent.rv.infrastructure.common.constant;

/**
 * @Description rv日志
 * <AUTHOR>
 * @Date 2024/8/6 11:24
 **/
public class RvAuditLogConstants {

    public static final String PRJ_CREATE = "PRJ_CREATE";

    public static final String PRJ_UPDATE = "PRJ_UPDATE";

    public static final String PRJ_STATUS_UPDATE = "PRJ_STATUS_UPDATE";

    // 删除项目
    public static final String PRJ_DELETE = "PRJ_DELETE";

    // 盘点项目加人
    public static final String PRJ_ADD_USER = "PRJ_ADD_USER";

    // 盘点删除人员
    public static final String PRJ_DELETE_USER = "PRJ_DELETE_USER";

    // 盘点导入人员
    public static final String PRJ_IMPORT_USER = "PRJ_IMPORT_USER";

    // 导出柱状图
    public static final String PRJ_OVERVIEW_EXPORT = "PRJ_OVERVIEW_EXPORT";


    // 编辑九宫格
    public static final String PRJ_EDIT_LABELS = "PRJ_EDIT_LABELS";

    public static final String PRJ_RULE_CREATE = "PRJ_RULE_CREATE";

    public static final String PRJ_BIND_020 = "PRJ_BIND_020";

    public static final String PRJ_UNBIND_020 = "PRJ_UNBIND_020";

    // 创建盘点维度
    public static final String PRJ_CREATE_DIM = "PRJ_CREATE_DIM";

    // 编辑盘点维度
    public static final String PRJ_UPDATE_DIM = "PRJ_UPDATE_DIM";

    // 盘点维度启用
    public static final String PRJ_DIM_ENABLE = "PRJ_DIM_ENABLE";

    // 删除维度
    public static final String PRJ_DELETE_DIM = "PRJ_DELETE_DIM";

    /**
     * 人员盘点结果校准
     */
    public static final String CAlI_USER_RESULT_UPDATE = "CAlI_USER_RESULT_UPDATE";

    /**
     * 创建，动态盘点项目
     */
    public static final String DMP_CREATE = "DMP_CREATE";

    /**
     * 更新动态盘点项目
     */
    public static final String DMP_UPDATE = "DMP_UPDATE";

    // 盘点删除
    public static final String DMP_DELETE = "DMP_DELETE";

    // 消息催促
    public static final String DMP_URGE = "DMP_URGE";

    // 暂停
    public static final String DMP_PAUSE = "DMP_PAUSE";

    // 重启
    public static final String DMP_RESTART = "DMP_RESTART";

    // 完成
    public static final String DMP_FINSH = "DMP_FINSH";

    // 发布
    public static final String DMP_PUBLISH = "DMP_PUBLISH";

    // 撤回
    public static final String DMP_WITHDRAW = "DMP_WITHDRAW";

    // 撤回
    public static final String DMP_TASK_WITHDRAW = "DMP_TASK_WITHDRAW";

    // 任务方案启用
    public static final String DMP_TASK_ENABLE = "DMP_TASK_ENABLE";

    // 任务方案启用
    public static final String DMP_TASK_DELETE = "DMP_TASK_DELETE";

    // 新增校准会基础信息
    public static final String CALI_MEET_ADD = "CALI_MEET_ADD";

    // 新增校准会基础信息
    public static final String CALI_MEET_UPDATE = "CALI_MEET_UPDATE";

    // 校准会删除
    public static final String CALI_DELETE = "CALI_DELETE";

    // 校准会关闭
    public static final String CALI_CLOSE = "CALI_CLOSE";

    // 校准会加人
    public static final String CALI_ADD_USER = "CALI_ADD_USER";

    // 校准会删除人
    public static final String CALI_DELETE_USER = "CALI_DELETE_USER";


    // 新建盘点分类
    public static final String CATE_CREATE = "CATE_CREATE";

    // 新建盘点分类
    public static final String CATE_UPDATE = "CATE_UPDATE";

    // 新建盘点分类
    public static final String CATE_STATUS_ENABLE = "CATE_STATUS_ENABLE";

    // 删除分类
    public static final String CATE_DELETE = "CATE_DELETE";

    // 删除自定义绩效等级
    public static final String PERF_GRADE_DELETE = "PERF_GRADE_DELETE";

    // 绩效周期新增
    public static final String PERF_PERIOD_ADD = "PERF_PERIOD_ADD";

    // 绩效周期编辑
    public static final String PERF_PERIOD_UPDATE = "PERF_PERIOD_UPDATE";

    // 绩效周期删除
    public static final String PERF_PERIOD_DELETE = "PERF_PERIOD_DELETE";

    /**
     * 动态匹配加人
     */
    public static final String DMP_ADD_USER = "DMP_ADD_USER";

    // 盘点导入人员
    public static final String DMP_IMPORT_USER = "DMP_IMPORT_USER";

    /*// 撤回项目
    public static final String DMP_WITHDRAW = "DMP_WITHDRAW";*/

    // 编辑规则
    public static final String DMP_UPDATE_RULE = "DMP_UPDATE_RULE";

    // 编辑自动加人规则
    public static final String DMP_UPDATE_AUTO_GROUP = "DMP_UPDATE_AUTO_GROUP";

    // 编辑消息发送
    public static final String DMP_UPDATE_MSG_SEND = "DMP_UPDATE_MSG_SEND";

    // 动态盘点，删除人员
    public static final String DMP_DELETE_USER = "DMP_DELETE_USER";

    public static final String XPD_CREATE = "XPD_CREATE";

    public static final String XPD_UPDATE = "XPD_UPDATE";

    public static final String XPD_CALC = "XPD_CALC";

    public static final String XPD_EXPORT = "XPD_EXPORT";

    public static final String XPD_ADD_TRAINING = "XPD_ADD_TRAINING";

    public static final String XPD_CREATE_TRAINING = "XPD_CREATE_TRAINING";

    public static final String XPD_DELETE_TRAINING = "XPD_DELETE_TRAINING";

    public static final String XPD_ADD_POOL = "XPD_ADD_POOL";

    public static final String XPD_USERREPORT_EXPORT = "XPD_USERREPORT_EXPORT";

    public static final String XPD_RULECONFIG_CREATE = "XPD_RULECONFIG_CREATE";

    public static final String XPD_RULECONFIG_EXECCREATE = "XPD_RULECONFIG_EXECCREATE";

    public static final String XPD_RULECONFIG_UPDATE = "XPD_RULECONFIG_UPDATE";

    public static final String XPD_DIM_COMB_EXPORT = "XPD_DIM_COMB_EXPORT";

    public static final String XPD_DIM_LAYER_EXPORT = "XPD_DIM_LAYER_EXPORT";

    public static final String XPD_TALENT_USER_EXPORT = "XPD_TALENT_USER_EXPORT";

    public static final String XPD_RULE_PERF_UPDATE = "XPD_RULE_PERF_UPDATE";

    public static final String XPD_RULE_OTHER_UPDATE = "XPD_RULE_OTHER_UPDATE";

    public static final String XPD_RULE_XPD_UPDATE = "XPD_RULE_XPD_UPDATE";

    public static final String XPD_GRID_DIM_COMB_EDIT = "XPD_GRID_DIM_COMB_EDIT";

    public static final String DIM_COMB_ADD = "DIM_COMB_ADD";

    public static final String DIM_COMB_EDIT = "DIM_COMB_EDIT";

    public static final String DIM_COMB_DEL = "DIM_COMB_DEL";

    public static final String XPD_GRID_CREATE = "XPD_GRID_CREATE";


    public static final String XPD_GRID_EDIT = "XPD_GRID_EDIT";


    public static final String XPD_GRID_PUBLISH = "XPD_GRID_PUBLISH";

    public static final String XPD_GRID_WITHDRAW = "XPD_GRID_WITHDRAW";

    public static final String XPD_GRID_DELETE= "XPD_GRID_DELETE";

    public static final String XPD_IMPT_ACT_CREATE = "XPD_IMPT_ACT_CREATE";

    public static final String XPD_GRID_CELL_EDIT= "XPD_GRID_CELL_EDIT";



    public static final String XPD_LEVEL_CREATE= "XPD_LEVEL_CREATE";

    public static final String XPD_LEVEL_UPDATE= "XPD_LEVEL_UPDATE";

    public static final String XPD_LEVEL_DELETE= "XPD_LEVEL_DELETE";

    public static final String XPD_GRID_LEVEL_EDIT= "XPD_GRID_LEVEL_EDIT";

    public static final String XPD_GRID_RATIO_EDIT= "XPD_GRID_RATIO_EDIT";

    public static final String XPD_REPORT_EXPORT= "XPD_REPORT_EXPORT";

}
