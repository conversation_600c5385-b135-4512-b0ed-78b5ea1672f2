package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;

public interface XpdDimMapper extends CommonMapper<XpdDimPO> {

    int insert(XpdDimPO record);

    XpdDimPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(XpdDimPO record);

    int updateBatch(@Param("list") List<XpdDimPO> list);

    int batchInsert(@Param("list") List<XpdDimPO> list);

    void batchUpdateScore(List<XpdDimPO> list);

    @Update("update rv_xpd_dim set score_total = null where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0")
    int clearScoreByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    /**
     * 过滤删除的数据
     *
     * @param id 主键
     * @return XpdDimPO
     */
    XpdDimPO selectById(String id);

    XpdDimPO selectByXpdIdAndSdDimId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("sdDimId") String sdDimId);

    List<XpdDimPO> listByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    Collection<XpdDimPO> selectByDimIds(
        @Param("orgId") String orgId, @Param("sdDimIds") Collection<String> sdDimIds);

    /**
     * 逻辑删除
     *
     * @param orgId
     * @param sdDimIds
     */
    void deleteBySdDimIds(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId,
        @Param("sdDimIds") List<String> sdDimIds,
        @Param("userId") String userId);

    void deleteByXpdId(
        @Param("orgId") String orgId,
        @Param("userId") String userId,
        @Param("xpdId") String xpdId);

    List<XpdDimPO> selectByXpdId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("excludePerf") Integer excludePerf);

    IPage<XpdDimPO> selectByXpdIdPage(
        @Param("page") IPage<XpdDimPO> page, @Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("excludePerf") Integer excludePerf,
        @Param("searchDimIds") List<String> searchDimIds);

    int countByOrgIdAndXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);
}