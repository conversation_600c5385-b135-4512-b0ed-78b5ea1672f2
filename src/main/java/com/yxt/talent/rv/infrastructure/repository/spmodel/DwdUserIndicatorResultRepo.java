package com.yxt.talent.rv.infrastructure.repository.spmodel;

import com.yxt.common.util.StreamUtil;
import com.yxt.spsdk.common.utils.IArrayUtils;
import com.yxt.spsdk.udpbase.QueryUdpUtils;
import com.yxt.spsdk.udpbase.bean.UdpUserBriefBean;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.spmodel.DwdUserIndicatorResultMapper;
import com.yxt.talent.rv.infrastructure.repository.xpd.bean.XpdIndicatorResultDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Repository
@RequiredArgsConstructor
public class DwdUserIndicatorResultRepo {
    private final DwdUserIndicatorResultMapper dwdUserIndicatorResultMapper;

    public List<XpdIndicatorResultDto> listUserIndicator(String orgId,List<String> userIds,List<String> indicatorIds) {
        if (StringUtils.isEmpty(orgId) || CollectionUtils.isEmpty(userIds) || CollectionUtils.isEmpty(indicatorIds)) {
            return Lists.newArrayList();
        }
        List<UdpUserBriefBean> userList = QueryUdpUtils.getSpUserInfo(orgId,
            userIds.stream().collect(Collectors.toSet()), UdpUserBriefBean::getThirdUserId);
        IArrayUtils.remove(userList, item -> StringUtils.isEmpty(item.getThirdUserId()));
        if (userList.isEmpty()) {
            return Lists.newArrayList();
        }
        Map<String, String> thirdUserIdMap = StreamUtil.list2map(userList,
            UdpUserBriefBean::getThirdUserId, UdpUserBriefBean::getId);
        List<XpdIndicatorResultDto> retList = dwdUserIndicatorResultMapper.listUserIndicator(orgId, thirdUserIdMap.keySet(), indicatorIds);
        //转换为udp userId
        retList.forEach(item -> item.setUserId(thirdUserIdMap.get(item.getUserId())));
        return retList;
    }
}
