package com.yxt.talent.rv.infrastructure.common.constant.enums;


import com.yxt.auditlog.consts.AuditConsts;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.base.AuditLogPointBase;
import com.yxt.spsdk.audit.enums.AuditLogicTypeEnum;
import com.yxt.talent.rv.controller.manage.calimeet.log.*;
import com.yxt.talent.rv.controller.manage.common.log.CategoryCreateProvider;
import com.yxt.talent.rv.controller.manage.common.log.CategoryDeleteProvider;
import com.yxt.talent.rv.controller.manage.common.log.CategoryStatusProvider;
import com.yxt.talent.rv.controller.manage.common.log.CategoryUpdateProvider;
import com.yxt.talent.rv.controller.manage.dmp.log.*;
import com.yxt.talent.rv.controller.manage.perf.log.PerfGradeDeleteProvider;
import com.yxt.talent.rv.controller.manage.perf.log.PerfPeriodAddProvider;
import com.yxt.talent.rv.controller.manage.perf.log.PerfPeriodDeleteProvider;
import com.yxt.talent.rv.controller.manage.perf.log.PerfPeriodUpdateProvider;
import com.yxt.talent.rv.controller.manage.prj.dim.log.PrjCreateDimProvider;
import com.yxt.talent.rv.controller.manage.prj.dim.log.PrjDeleteDimProvider;
import com.yxt.talent.rv.controller.manage.prj.dim.log.PrjDimEnableProvider;
import com.yxt.talent.rv.controller.manage.prj.prj.log.*;
import com.yxt.talent.rv.controller.manage.prj.rule.log.PrjDimRuleCreateProvider;
import com.yxt.talent.rv.controller.manage.prj.train.log.PrjUnbindTrainLogProvider;
import com.yxt.talent.rv.controller.manage.prj.user.log.PrjAddUserLogProvider;
import com.yxt.talent.rv.controller.manage.prj.user.log.PrjDeleteUserLogProvider;
import com.yxt.talent.rv.controller.manage.prj.user.log.PrjImportUserProvider;
import com.yxt.talent.rv.controller.manage.xpd.log.*;
import com.yxt.talent.rv.infrastructure.common.constant.RvAuditLogConstants;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import com.yxt.talent.rv.infrastructure.service.audit.RvEditLogProvider;

public enum RvAuditLogPointEnum implements AuditLogPointBase {

    //项目创建
    PRJ_CREATE(RvAuditLogConstants.PRJ_CREATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, PrjCreateLogProvider.class, "盘点项目-{盘点名称}"),

    // 编辑项目基础信息
    PRJ_UPDATE(RvAuditLogConstants.PRJ_UPDATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, PrjUpdateLogProvider.class,
            AuditLogHelper.Module.PROJECT.getName() + "-{盘点名称}"),

    PRJ_STATUS_UPDATE(RvAuditLogConstants.PRJ_STATUS_UPDATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.UPDATE, PrjStatusLogProvider.class,
            AuditLogHelper.Module.PROJECT.getName() + "-{盘点名称}" + "-{项目状态}"),

    // 盘点加人
    PRJ_ADD_USER(RvAuditLogConstants.PRJ_ADD_USER, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, PrjAddUserLogProvider.class,
            AuditLogHelper.Module.PROJECT.getName() + "-" + "{盘点名称}" + "-添加人员"),

    // 盘点删人
    PRJ_DELETE_USER(RvAuditLogConstants.PRJ_DELETE_USER, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE, PrjDeleteUserLogProvider.class,
            AuditLogHelper.Module.PROJECT.getName() + "-" + "{盘点名称}" + "-删除人员-"),

    // 盘点导入人员
    PRJ_IMPORT_USER(RvAuditLogConstants.PRJ_IMPORT_USER, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.CREATE, PrjImportUserProvider.class,
            AuditLogHelper.Module.PROJECT.getName() + "-" + "{盘点名称}" + "-导入人员"),

    // 创建盘点维度数据
    PRJ_RULE_CREATE(RvAuditLogConstants.PRJ_RULE_CREATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, PrjDimRuleCreateProvider.class, "盘点-{盘点名称}"),

    // 图标数据导出（柱状图）
    PRJ_OVERVIEW_EXPORT(RvAuditLogConstants.PRJ_OVERVIEW_EXPORT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.EXPORT, PrjOverviewExpLogProvider.class,
            AuditLogHelper.Module.PROJECT.getName() + "-" + "{盘点名称}" + "-下载人才分布"),

    // 批量更新盘点项目九宫格标签
    PRJ_EDIT_LABELS(RvAuditLogConstants.PRJ_EDIT_LABELS, AuditLogHelper.Module.SETTING.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, PrjGridEditLogProvider.class, "盘点-{盘点名称}"),

    // 盘点绑定项目
    PRJ_BIND_020(RvAuditLogConstants.PRJ_BIND_020, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, RvEditLogProvider.class, "盘点-%s-发起培训"),

    PRJ_UNBIND_020(RvAuditLogConstants.PRJ_UNBIND_020, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE, PrjUnbindTrainLogProvider.class,
            "盘点-{盘点名称}-移除培训-{培训名称}"),

    // 创建盘点维度
    PRJ_CREATE_DIM(RvAuditLogConstants.PRJ_CREATE_DIM, AuditLogHelper.Module.SETTING.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, PrjCreateDimProvider.class,
            AuditLogHelper.Module.SETTING.getName() + "-维度名称-"),

    // 编辑盘点维度
    PRJ_UPDATE_DIM(RvAuditLogConstants.PRJ_UPDATE_DIM, AuditLogHelper.Module.SETTING.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, PrjCreateDimProvider.class,
            AuditLogHelper.Module.SETTING.getName() + "-维度名称-"),

    // 启用 禁用盘点维度
    PRJ_DIM_ENABLE(RvAuditLogConstants.PRJ_DIM_ENABLE, AuditLogHelper.Module.SETTING.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.UPDATE, PrjDimEnableProvider.class,
            AuditLogHelper.Module.SETTING.getName() + "-维度名称-"),

    // 删除盘点维度
    PRJ_DELETE_DIM(RvAuditLogConstants.PRJ_DELETE_DIM, AuditLogHelper.Module.SETTING.getCode(),
            AuditLogicTypeEnum.BEFORE.getCode(), AuditConsts.DELETE, PrjDeleteDimProvider.class,
            AuditLogHelper.Module.SETTING.getName() + "-维度名称"),

    //

    PRJ_DELETE(RvAuditLogConstants.PRJ_DELETE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE, PrjDeleteLogProvider.class,
            AuditLogHelper.Module.PROJECT.getName() + "-" + "盘点名称"),

    CAlI_USER_RESULT_UPDATE(RvAuditLogConstants.CAlI_USER_RESULT_UPDATE, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, CaliUserResultLogProvider.class,
            "盘点校准会-%s-人员校准-%s"),

    // 新增校准会基础信息
    CALI_MEET_ADD(RvAuditLogConstants.CALI_MEET_ADD, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, CaliAddProvider.class,
            AuditLogHelper.Module.CALIBRATION.getName() + "-" + "{校准会名称}"),

    // 编辑校准会基础信息 todo
    CALI_MEET_UPDATE(RvAuditLogConstants.CALI_MEET_UPDATE, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, CaliUpdateProvider.class,
            AuditLogHelper.Module.CALIBRATION.getName() + "-" + "{校准会名称}"),

    // 删除校准
    CALI_DELETE(RvAuditLogConstants.CALI_DELETE, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE, CaliDeleteProvider.class,
            AuditLogHelper.Module.CALIBRATION.getName() + "-" + "{校准会名称}"),

    // 关闭校准
    CALI_CLOSE(RvAuditLogConstants.CALI_CLOSE, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.UPDATE, CaliCloseProvider.class,
            AuditLogHelper.Module.CALIBRATION.getName() + "-" + "{校准会名称}"),

    // 校准会加人
    CALI_ADD_USER(RvAuditLogConstants.CALI_ADD_USER, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, CaliAddUserProvider.class,
            AuditLogHelper.Module.CALIBRATION.getName() + "-" + "{校准会名称}" + "-添加人员"),

    // 校准会删人
    CALI_DELETE_USER(RvAuditLogConstants.CALI_DELETE_USER, AuditLogHelper.Module.CALIBRATION.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE, CaliDeleteUserProvider.class,
            AuditLogHelper.Module.CALIBRATION.getName() + "-" + "{校准会名称}" + "-删除人员"),

    // 盘点创建分类
    CATE_CREATE(RvAuditLogConstants.CATE_CREATE, AuditLogHelper.Module.SETTING.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, CategoryCreateProvider.class,
            AuditLogHelper.Module.SETTING.getName() + "-盘点分类-" + "{分类名称}"),

    // 盘点更新分类
    CATE_UPDATE(RvAuditLogConstants.CATE_UPDATE, AuditLogHelper.Module.SETTING.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, CategoryUpdateProvider.class,
            AuditLogHelper.Module.SETTING.getName() + "-盘点分类-" + "{分类名称}"),

    // 盘点更新分类
    CATE_STATUS_ENABLE(RvAuditLogConstants.CATE_STATUS_ENABLE, AuditLogHelper.Module.SETTING.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.UPDATE, CategoryStatusProvider.class,
            AuditLogHelper.Module.SETTING.getName() + "-盘点分类-" + "{分类名称}" + "状态"),


    // 删除分类
    CATE_DELETE(RvAuditLogConstants.CATE_DELETE, AuditLogHelper.Module.SETTING.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE, CategoryDeleteProvider.class,
            AuditLogHelper.Module.SETTING.getName() + "-盘点分类-" + "{分类名称}"),

    // 删除绩效等级
    PERF_GRADE_DELETE(RvAuditLogConstants.PERF_GRADE_DELETE, AuditLogHelper.Module.PERFORMANCE.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE, PerfGradeDeleteProvider.class,
            AuditLogHelper.Module.PERFORMANCE.getName() + "-" + "{绩效等级}"),


    // 绩效周期新增
    PERF_PERIOD_ADD(RvAuditLogConstants.PERF_PERIOD_ADD, AuditLogHelper.Module.PERFORMANCE.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, PerfPeriodAddProvider.class,
            AuditLogHelper.Module.PERFORMANCE.getName() + "-" + "{绩效周期}"),

    // 绩效周期编辑
    PERF_PERIOD_UPDATE(RvAuditLogConstants.PERF_PERIOD_UPDATE, AuditLogHelper.Module.PERFORMANCE.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, PerfPeriodUpdateProvider.class,
            AuditLogHelper.Module.PERFORMANCE.getName() + "-" + "{绩效周期}"),

    // 绩效周期删除
    PERF_PERIOD_DELETE(RvAuditLogConstants.PERF_PERIOD_DELETE, AuditLogHelper.Module.PERFORMANCE.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE, PerfPeriodDeleteProvider.class,
            AuditLogHelper.Module.PERFORMANCE.getName() + "-" + "{绩效周期}"),


    DMP_CREATE(RvAuditLogConstants.DMP_CREATE, AuditLogHelper.Module.DMP.getCode(), AuditLogicTypeEnum.AFTER.getCode(),
            AuditConsts.CREATE, DmpCreateLogProvider.class, "人岗匹配项目-%s"),

    DMP_UPDATE(RvAuditLogConstants.DMP_UPDATE, AuditLogHelper.Module.DMP.getCode(), AuditLogicTypeEnum.FULL.getCode(),
            AuditConsts.UPDATE, DmpUpdateLogProvider.class, "人岗匹配项目-%s"),

    DMP_DELETE(RvAuditLogConstants.DMP_DELETE, AuditLogHelper.Module.DMP.getCode(), AuditLogicTypeEnum.NONE.getCode(),
            AuditConsts.DELETE, DmpDeleteLogProvider.class, "人岗匹配项目-%s"),

    DMP_URGE(RvAuditLogConstants.DMP_URGE, AuditLogHelper.Module.DMP.getCode(), AuditLogicTypeEnum.NONE.getCode(),
            AuditConsts.UPDATE, DmpUrgeProvider.class, "人岗匹配项目-%s-催促项目"),

    DMP_PAUSE(RvAuditLogConstants.DMP_PAUSE, AuditLogHelper.Module.DMP.getCode(), AuditLogicTypeEnum.NONE.getCode(),
            AuditConsts.UPDATE, DmpPauseProvider.class, "人岗匹配项目-%s-暂停项目"),

    DMP_RESTART(RvAuditLogConstants.DMP_RESTART, AuditLogHelper.Module.DMP.getCode(), AuditLogicTypeEnum.NONE.getCode(),
            AuditConsts.UPDATE, DmpRestartProvider.class, "人岗匹配项目-%s-重启项目"),

    DMP_FINSH(RvAuditLogConstants.DMP_FINSH, AuditLogHelper.Module.DMP.getCode(), AuditLogicTypeEnum.NONE.getCode(),
            AuditConsts.UPDATE, DmpFinishProvider.class, "人岗匹配项目-%s-结束项目"),

    DMP_PUBLISH(RvAuditLogConstants.DMP_PUBLISH, AuditLogHelper.Module.DMP.getCode(), AuditLogicTypeEnum.NONE.getCode(),
            AuditConsts.UPDATE, DmpPublishProvider.class, "人岗匹配项目-%s-发布项目"),

    DMP_WITHDRAW(RvAuditLogConstants.DMP_WITHDRAW, AuditLogHelper.Module.DMP.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.UPDATE, DmpPrjWithdrawProvider.class,
            "人岗匹配项目-%s-撤回项目"),

    DMP_ADD_USER(RvAuditLogConstants.DMP_ADD_USER, AuditLogHelper.Module.DMP.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, DmpAddUserLogProvider.class,
            "人岗匹配项目-%s-添加学员"),

    DMP_IMPORT_USER(RvAuditLogConstants.DMP_IMPORT_USER, AuditLogHelper.Module.DMP.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.CREATE, DmpImportUserLogProvider.class,
            "人岗匹配项目-%s-导入学员"),

    DMP_TASK_WITHDRAW(RvAuditLogConstants.DMP_TASK_WITHDRAW, AuditLogHelper.Module.DMP.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.UPDATE, DmpWithdrawLogProvider.class,
            "人岗匹配项目-%s-撤回方案设计"),

    DMP_TASK_ENABLE(RvAuditLogConstants.DMP_TASK_ENABLE, AuditLogHelper.Module.DMP.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.UPDATE, DmpTaskEnableProvider.class,
            "人岗匹配项目-%s-启用方案设计"),

    DMP_TASK_DELETE(RvAuditLogConstants.DMP_TASK_DELETE, AuditLogHelper.Module.DMP.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.UPDATE, DmpTaskDeleteProvider.class,
            "人岗匹配项目-%s-删除方案设计"),

    DMP_UPDATE_RULE(RvAuditLogConstants.DMP_UPDATE_RULE, AuditLogHelper.Module.DMP.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, DmpUpdateRuleLogProvider.class,
            "人岗匹配项目-%s-编辑匹配规则"),

    DMP_UPDATE_AUTO_GROUP(RvAuditLogConstants.DMP_UPDATE_AUTO_GROUP, AuditLogHelper.Module.DMP.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, DmpUpdateGroupLogProvider.class,
            "人岗匹配项目-%s-自动加人"),

    DMP_UPDATE_MSG_SEND(RvAuditLogConstants.DMP_UPDATE_MSG_SEND, AuditLogHelper.Module.DMP.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, DmpUpdateMsgSendProvider.class,
            "人岗匹配项目-%s-消息配置"),

    DMP_DELETE_USER(RvAuditLogConstants.DMP_DELETE_USER, AuditLogHelper.Module.DMP.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.DELETE, DmpDeleteUserLogProvider.class,
            "人岗匹配项目-%s-删除学员"),

    XPD_CREATE(RvAuditLogConstants.XPD_CREATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdCreateLogProvider.class, "盘点-%s"),

    XPD_UPDATE(RvAuditLogConstants.XPD_UPDATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdUpdateLogProvider.class, "盘点-%s"),

    XPD_CALC(RvAuditLogConstants.XPD_CALC, AuditLogHelper.Module.PROJECT.getCode(), AuditLogicTypeEnum.NONE.getCode(),
            AuditConsts.UPDATE, XpdCalcLogProvider.class, "盘点-%s-开始计算"),

    XPD_EXPORT(RvAuditLogConstants.XPD_EXPORT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.BEFORE.getCode(), AuditConsts.EXPORT, XpdExportlogProvider.class, "盘点-%s-项目报告"),

    XPD_ADD_TRAINING(RvAuditLogConstants.XPD_ADD_TRAINING, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdTrainingLogProvider.class, "盘点-%s-发起培训"),

    XPD_CREATE_TRAINING(RvAuditLogConstants.XPD_CREATE_TRAINING, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdPrjLaunchTrainingLogProvider.class,
            "盘点-%s-发起培训"),

    XPD_DELETE_TRAINING(RvAuditLogConstants.XPD_DELETE_TRAINING, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.BEFORE.getCode(), AuditConsts.DELETE, XpdPrjDelTrainingLogProvider.class,
            "盘点-%s-移除培训"),

    XPD_ADD_POOL(RvAuditLogConstants.XPD_ADD_POOL, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdAddPoolLogProvider.class, "盘点-%s-加入人才池"),

    XPD_USERREPORT_EXPORT(RvAuditLogConstants.XPD_USERREPORT_EXPORT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.EXPORT, XpdUserReportExportlogProvider.class,
            "盘点-%s-人员盘点报告"),

    XPD_RULECONFIG_CREATE(RvAuditLogConstants.XPD_RULECONFIG_CREATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdRuleConifgCreateLogProvider.class,
            "盘点-%s-配置全局规则"),

    XPD_RULECONFIG_EXECCREATE(RvAuditLogConstants.XPD_RULECONFIG_EXECCREATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdRuleConifgExecCreateLogProvider.class,
            "盘点-%s-快速生成计算规则"),

    XPD_RULECONFIG_UPDATE(RvAuditLogConstants.XPD_RULECONFIG_UPDATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdRuleConifgUpdateLogProvider.class,
            "盘点-%s-配置全局规则"),

    XPD_DIM_COMB_EXPORT(RvAuditLogConstants.XPD_DIM_COMB_EXPORT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.EXPORT, XpdDimCombExportLogProvider.class,
            "盘点-%s-落位结果-%s"),

    XPD_DIM_LAYER_EXPORT(RvAuditLogConstants.XPD_DIM_LAYER_EXPORT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.EXPORT, XpdDimLayerExportLogProvider.class,
            "盘点-%s-维度分层-%s"),

    XPD_TALENT_USER_EXPORT(RvAuditLogConstants.XPD_TALENT_USER_EXPORT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.EXPORT, XpdTalentUserExportLogProvider.class,
            "盘点-%s-人才分层"),

    XPD_RULE_PERF_UPDATE(RvAuditLogConstants.XPD_RULE_PERF_UPDATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdRulePerfCreateLogProvider.class,
            "盘点-%s-编辑维度规则-%s"),

    XPD_RULE_OTHER_UPDATE(RvAuditLogConstants.XPD_RULE_OTHER_UPDATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdRulOtherCreateLogProvider.class,
            "盘点-%s-编辑维度规则-%s"),

    XPD_RULE_XPD_UPDATE(RvAuditLogConstants.XPD_RULE_XPD_UPDATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdRulCreateLogProvider.class,
            "盘点-%s-编辑项目计算规则"), DIM_COMB_ADD(RvAuditLogConstants.DIM_COMB_ADD,
            AuditLogHelper.Module.PROJECT.getCode(), AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE,
            DimCombCreateLogProvider.class, "维度组合-%s"),

    DIM_COMB_EDIT(RvAuditLogConstants.DIM_COMB_EDIT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, DimCombEditLogProvider.class, "维度组合-%s"),

    DIM_COMB_DEL(RvAuditLogConstants.DIM_COMB_DEL, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.DELETE, DimCombDelLogProvider.class, "维度组合-%s"),

    XPD_GRID_CREATE(RvAuditLogConstants.XPD_GRID_CREATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdGridCrateLogProvider.class, "宫格模板-%s"),

    XPD_GRID_EDIT(RvAuditLogConstants.XPD_GRID_EDIT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdGridEditLogProvider.class, "宫格模板-%s"),

    XPD_GRID_PUBLISH(RvAuditLogConstants.XPD_GRID_PUBLISH, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.UPDATE, XpdGridPublishLogProvider.class, "宫格模板发布-%s"),

    XPD_GRID_WITHDRAW(RvAuditLogConstants.XPD_GRID_WITHDRAW, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.UPDATE, XpdGridWithdrawLogProvider.class, "宫格模板发布-%s"),

    XPD_GRID_DELETE(RvAuditLogConstants.XPD_GRID_DELETE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE, XpdGridDeleteLogProvider.class, "宫格模板删除-%s"),

    XPD_GRID_CELL_EDIT(RvAuditLogConstants.XPD_GRID_CELL_EDIT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdGridCellEditLogProvider.class, "模板宫格编辑-%s"),

    XPD_LEVEL_CREATE(RvAuditLogConstants.XPD_LEVEL_CREATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.AFTER.getCode(), AuditConsts.CREATE, XpdLevelCreateLogProvider.class, "创建人才分层-%s"),


    XPD_LEVEL_UPDATE(RvAuditLogConstants.XPD_LEVEL_UPDATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdLevelUpdateLogProvider.class, "编辑人才分层-%s"),

    XPD_LEVEL_DELETE(RvAuditLogConstants.XPD_LEVEL_DELETE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.DELETE, XpdLevelDeleteLogProvider.class, "删除人才分层-%s"),

    XPD_GRID_LEVEL_EDIT(RvAuditLogConstants.XPD_GRID_LEVEL_EDIT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdGridLevelEditLogProvider.class,
            "编辑分层标准-%s"),

    XPD_GRID_RATIO_EDIT(RvAuditLogConstants.XPD_GRID_RATIO_EDIT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.FULL.getCode(), AuditConsts.UPDATE, XpdGridRatioEditLogProvider.class,
            "编辑落位比例-%s"),

    XPD_REPORT_EXPORT(RvAuditLogConstants.XPD_REPORT_EXPORT, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.EXPORT, XpdReportExportProvider.class, "项目报告导出-%s"),

    XPD_IMPT_ACT_CREATE(RvAuditLogConstants.XPD_IMPT_ACT_CREATE, AuditLogHelper.Module.PROJECT.getCode(),
            AuditLogicTypeEnum.NONE.getCode(), AuditConsts.CREATE, XpdImptActCreatLogProvider.class,
            "创建导入活动-%s");

    RvAuditLogPointEnum(String pointCode, String module, int logicType, String auditAction, Class dataProvider,
            String pointName) {
        this.pointCode = pointCode;
        this.module = module;
        this.logicType = logicType;
        this.auditAction = auditAction;
        this.dataProvider = dataProvider;
        this.pointName = pointName;
    }

    private String pointCode;
    private String module;
    /**
     * ref AuditLogicTypeEnum
     */
    private int logicType;
    private String auditAction;
    private Class<? extends AuditLogDataProvider> dataProvider;
    private String pointName;

    @Override
    public String getPointCode() {
        return pointCode;
    }

    @Override
    public String getModule() {
        return module;
    }

    @Override
    public int getLogicType() {
        return logicType;
    }

    @Override
    public String getAuditAction() {
        return auditAction;
    }

    @Override
    public Class<? extends AuditLogDataProvider> getDataProvider() {
        return dataProvider;
    }

    @Override
    public String getPointName() {
        return pointName;
    }
}
