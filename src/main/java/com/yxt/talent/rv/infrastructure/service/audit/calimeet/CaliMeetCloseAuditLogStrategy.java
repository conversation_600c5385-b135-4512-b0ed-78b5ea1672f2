package com.yxt.talent.rv.infrastructure.service.audit.calimeet;

import com.yxt.auditlog.AuditLog;
import com.yxt.auditlog.AuditLogContext;
import com.yxt.auditlog.bean.AuditDetail;
import com.yxt.enums.DeleteEnum;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetPO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.Collections;

@Slf4j
@Service
@Scope("prototype")
@RequiredArgsConstructor
public class CaliMeetCloseAuditLogStrategy implements AuditLogStrategy {

    private final CaliMeetMapper caliMeetMapper;

    @Override
    public void doExecute(AuditLog auditLog, String[] argsNames, Object[] args, Object result) {
        if (args == null || args.length == 0) {
            log.error("LOG63300:{}", this.getClass().getName() + " : 日志记录失败，参数列表为空");
            return;
        }

        String id = (String) args[1];
        CaliMeetPO cm =
                caliMeetMapper.selectByIdAndOrgIdAndDeleted(
                        id, auditLog.getOrgId(), DeleteEnum.NOT_DELETED.getCode());
        if (cm == null) {
            log.error("LOG63310:{}", this.getClass().getName() + " : 日志记录失败，id:" + id);
            return;
        }

        // 组装日志
        AuditDetail ad = getUpdateAuditDetail(AuditLogHelper.Module.CALIBRATION.getCode());
        ad.setEntityName(
                AuditLogHelper.Module.CALIBRATION.getName() + "-" + cm.getMeetName() + "-结束");
        ad.setEntityId(cm.getId());
        auditLog.setDetails(Collections.singletonList(ad));
        AuditLogContext.asyncCommit(auditLog);
    }
}
