package com.yxt.talent.rv.infrastructure.common.constant;

import lombok.experimental.UtilityClass;

@UtilityClass
public final class ExceptionKeys {
    public static final String GRID_RULE_LEVEL_COUNT = "apis.sptalentrv.grid.rule.level.count";
    public static final String PERF_RULE_ALL_PERIOD = "apis.sptalentrv.perf.rule.all.period";
    public static final String PERF_RULE_ANY_PERIOD = "apis.sptalentrv.perf.rule.any.period";
    public static final String PERF_RULE_PERIOD_QTY = "apis.sptalentrv.perf.rule.period.qty";

    public static final String SYS_OPERATING_FREQUENTLY = "apis.sptalentrv.sys.operating.frequently";
    public static final String SYS_CONCURRENT_ERROR = "apis.sptalentrv.sys.concurrent.error";

    public static final String GLOBAL_ENUM_VALUE_EXCEED_RANGE = "apis.sptalentrv.global.enum.exceed";

    public static final String AUTH_PARAM_ERROR = "apis.sptalentrv.auth.param.error";
    public static final String AUTH_PERM_NOT_FOUND = "apis.sptalentrv.data.auth.prem.not.found";

    public static final String SCOPE_DEPT_PRJ_TARGET_ID_NOT_BLANK = "apis.sptalentrv.user.focus.target_id.necessary";
    public static final String SCOPE_DEPT_PRJ_ACTION_RANGE = "apis.sptalentrv.user.focus.action.range";
    public static final String SCOPE_DEPT_PRJ_TARGET_TYPE_RANGE = "apis.sptalentrv.user.focus.target_type.range";
    public static final String SCOPE_DEPT_PRJ_STATUS_RANGE = "apis.sptalentrv.prj.mine.status.range";

    public static final String PROFILE_TEAM_DEPT_ID_ONLY_ONE = "apis.sptalentrv.auth.team.dept_ids.only_one";

    public static final String USER_NOT_EXISTED = "apis.sptalentrv.user.not.existed";
    public static final String USER_THIRD_USER_ID_NECESSARY = "apis.sptalentrv.user.third_user_id.necessary";
    public static final String USER_GRADE_LEVEL_LENGTH = "apis.sptalentrv.user.gradeLevel.length.200";
    public static final String USER_PROF_CERT_LENGTH = "apis.sptalentrv.user.profCerts.length.200";
    public static final String USER_ID_BLANK = "apis.sptalentrv.user.id.blank";
    public static final String USER_CAREER_HISTORY_ID_NECESSARY = "apis.sptalentrv.user.career_history_id.necessary";
    public static final String USER_CAREER_HISTORY_OCCURRENCE_TIME_NECESSARY = "apis.sptalentrv.user.career_history.occurrence_time.necessary";
    public static final String USER_REWARD_PUNISHMENT_HISTORY_RPTYPE_INVALID = "apis.sptalentrv.user.reward_punishment_history.rpType.invalid";
    public static final String USER_REWARD_PUNISHMENT_HISTORY_RPNAME_NECESSARY = "apis.sptalentrv.user.reward_punishment_history.rpName.necessary";

    public static final String ORG_ID_BLANK = "apis.sptalentrv.org.id.blank";
    public static final String ORG_ID_NOT_MATCH = "apis.sptalentrv.org.id.notMatch";

    public static final String DEPT_NOT_EXIST = "apis.sptalentrv.dept.not.exist";

    public static final String TRANSFER_IMPORT_FILE_ID_INVALID = "apis.sptalentrv.transmit.import.fileId.invalid";
    public static final String TRANSFER_IMPORT_FILE_READ_ERROR = "apis.sptalentrv.transmit.import.file.read.error";

    public static final String PRJ_EXISTED = "apis.sptalentrv.prj.existed";
    public static final String PRJ_NOT_EXISTED = "apis.sptalentrv.prj.notExists";
    public static final String PRJ_DIM_CONF_NOT_EXISTS = "apis.sptalentrv.prj.dim.conf.not.exists";
    public static final String PRJ_DIM_NOT_CONFIG = "apis.sptalentrv.prj.dim.not.config";
    public static final String PRJ_ADD_USER_NOT_NULL = "apis.sptalentrv.prj.user.add.must.not.null";
    public static final String PRJ_REMOVE_USER_NOT_NULL = "apis.sptalentrv.prj.user.remove.not.null";
    public static final String PRJ_USER_NOT_NULL = "apis.sptalentrv.prj.user.must.not.null";
    public static final String PRJ_SHOULD_SET_EVAL_RELATION = "apis.sptalentrv.prj.eval.relation.necessary";
    public static final String PRJ_CHARGE_EVAL_OVERFLOW = "apis.sptalentrv.prj.eval.overflow";
    public static final String PRJ_CALC_IMPORT_TYPE_OVERFLOW = "apis.sptalentrv.prj.user.result.calc.type.import.overflow";
    public static final String PRJ_CALC_UPDATE_IMPORT_TYPE_OVERFLOW = "apis.sptalentrv.prj.user.result.calc.update.import.overflow";
    public static final String PRJ_DEL_STATUS = "apis.sptalentrv.prj.status";
    public static final String PRJ_LABEL_NAME_CONFLICT = "apis.sptalentrv.prj.label.name.conflict";
    public static final String PRJ_MANAGER_NOT_EMPTY = "apis.sptalentrv.prj.manager.not.empty";
    public static final String PRJ_MANAGER_SIZE_EXCEED = "apis.sptalentrv.prj.manager.size.exceeded";
    public static final String PRJ_EVAL_ID_IS_NULL = "apis.sptalentrv.prj.eval.id.null";
    public static final String PRJ_BIND_TRAIN_USER_ID_EMPTY = "apis.sptalentrv.prj.training.user_id.empty";
    public static final String PRJ_BIND_TRAIN_ID_EMPTY = "apis.sptalentrv.prj.training.training_id.empty";
    public static final String PRJ_DIM_CONF_TOOL_NOT_EXISTED = "apis.sptalentrv.prj.dim.conf.tool.notExists";
    public static final String PRJ_USER_SUGGESTION_SIZE = "apis.sptalentrv.prj.user.suggestion.size";
    public static final String PRJ_DIM_RULE_NOT_EXISTED = "apis.sptalentrv.prj.dim.rule.not.existed";
    public static final String PRJ_DIM_NOT_EXISTED = "apis.sptalentrv.prj.dim.not.existed";
    public static final String PRJ_LABEL_RULE_NOT_EMPTY = "apis.sptalentrv.prj.label.rule.not.empty";
    public static final String PRJ_LOGIC_NOT_NULL = "apis.sptalentrv.prj.logic.not.null";
    public static final String PRJ_LOGIC_RANGE_OVER = "apis.sptalentrv.prj.logic.range.over";
    public static final String PRJ_LABEL_RULE_TARGET_ID_NOT_NULL = "apis.sptalentrv.prj.label.rule.target.id.not.null";
    public static final String PRJ_LABEL_RULE_OPERATORS_NOT_EMPTY = "apis.sptalentrv.prj.label.rule.operators.not.empty";
    public static final String PRJ_LABEL_RULE_OPERATOR_NOT_BLANK = "apis.sptalentrv.prj.label.rule.operator.not.blank";
    public static final String PRJ_LABEL_RULE_VALUE_NOT_NULL = "apis.sptalentrv.prj.label.rule.value.not.null";
    public static final String PRJ_RESULT_NOT_EXISTED = "apis.sptalentrv.prj.result.notExists";
    public static final String PRJ_RESULT_LABEL_CONFLICT = "apis.sptalentrv.prj.result.label.conflict";
    public static final String PRJ_RESULT_LABEL_MAX_SIZE = "apis.sptalentrv.prj.result.label.max.size";
    public static final String PRJ_RESULT_LABEL_ID_NOT_NULL = "apis.sptalentrv.prj.result.label.id.not.null";
    public static final String PRJ_PERF_PERIOD_EMPTY = "apis.sptalentrv.perf.period.empty";
    public static final String PRJ_PERF_LEVEL_NO_RIGHT = "apis.sptalentrv.perf.level.no.right";
    public static final String PRJ_DIMENSION_NOT_CONFIG = "apis.sptalentrv.prj.dimension.not.config";

    public static final String CALI_MEET_NOT_EXISTED = "apis.sptalentrv.calimeet.not.existed";
    public static final String CALI_MEET_DEL_REJECT = "apis.sptalentrv.calimeet.delete.forbidden";
    public static final String CALI_MEET_USER_NOT_EXISTED = "apis.sptalentrv.calimeet.user.not.existed";
    public static final String CALI_MEET_NAME_EXISTED = "apis.sptalentrv.calimeet.name.existed";
    public static final String CALI_MEET_USER_BATCH_ADD_LIST_EMPTY = "apis.sptalentrv.calimeet.user.batch_add.list.empty";
    public static final String CALI_MEET_USER_NOT_IN_PRJ_USER = "apis.sptalentrv.calimeet.user.not.exists";
    public static final String CALI_SCORE_OVER_MAXS = "apis.sptalentrv.calimeet.score.over.maxs";

    public static final String CATEGORY_USED = "apis.sptalentrv.category.used";
    public static final String CATEGORY_NOT_EXISTED = "apis.sptalentrv.category.not.existed";
    public static final String CATEGORY_EXISTED = "apis.sptalentrv.category.existed";
    public static final String CATEGORY_MAX_COUNT = "apis.sptalentrv.category.max.count";

    public static final String PERF_IMPORT_NO_USERNAME = "apis.sptalentrv.prj.perf.import.no.username";
    public static final String PERF_IMPORT_NO_PERIOD = "apis.sptalentrv.prj.perf.import.no.period";
    public static final String PERF_IMPORT_NO_LEVEL = "apis.sptalentrv.prj.perf.import.no.level";
    public static final String PERF_PERIOD_NAME_CONFLICT = "apis.sptalentrv.prj.perf.period.name.conflict";
    public static final String PERF_PERIOD_NOT_EXIST = "apis.sptalentrv.prj.perf.period.not.exist";
    public static final String PERF_PERIOD_HAS_DATA = "apis.sptalentrv.prj.perf.period.has.data";
    public static final String PERF_PERIOD_CYCLE_NECESSARY = "apis.sptalentrv.prj.perf.cycle.necessary";
    public static final String PERF_PERIOD_NECESSARY = "apis.sptalentrv.prj.perf.period.necessary";
    public static final String PERF_DIM_LIMIT = "apis.sptalentrv.prj.dim.max.exceed";
    public static final String PERF_DIM_NAME_CONFLICT = "apis.sptalentrv.prj.dim.name.conflict";
    public static final String PERF_DIM_NOT_EXIST = "apis.sptalentrv.prj.dim.not.exist";
    public static final String PERF_DIM_IS_ENABLED = "apis.sptalentrv.prj.dim.enabled";
    public static final String PERF_DIM_IS_DISABLED = "apis.sptalentrv.prj.dim.disabled";
    public static final String PERF_DIM_BEEN_USED = "apis.sptalentrv.prj.dim.been.used";
    public static final String PERF_GRADE_NAME_CONFLICT = "apis.sptalentrv.prj.perf.grade.name.conflict";
    public static final String PERF_GRADE_NOT_EXIST = "apis.sptalentrv.prj.perf.grade.not.exist";
    public static final String PERF_DIM_SYSTEM_NOT_DISABLED = "apis.sptalentrv.prj.dim.system.notDisabled";
    public static final String PERF_SYNC_YEARLY_NECESSARY = "apis.sptalentrv.prj.perf.yearly.necessary";
    public static final String PREF_SYNC_PERIOD_INVALID_1 = "apis.sptalentrv.prj.perf.sync.period.invalid_1";
    public static final String PREF_SYNC_PERIOD_INVALID_2 = "apis.sptalentrv.prj.perf.sync.period.invalid_2";
    public static final String PREF_SYNC_PERIOD_INVALID_3 = "apis.sptalentrv.prj.perf.sync.period.invalid_3";
    public static final String PREF_SYNC_PERIOD_INVALID_4 = "apis.sptalentrv.prj.perf.sync.period.invalid_4";
    public static final String PREF_SYNC_ERROR_1 = "apis.sptalentrv.prj.perf.sync.error_1";
    public static final String PREF_SYNC_ERROR_2 = "apis.sptalentrv.prj.perf.sync.error_2";
    public static final String PERF_CYCLE_RANGE = "apis.sptalentrv.perf.cycle.range";
    public static final String PERF_YEARLY_RANGE = "apis.sptalentrv.perf.yearly.range";
    public static final String PERF_PROJECT_USED = "apis.sptalentrv.perf.used";
    public static final String PERF_LEVEL_USED = "apis.sptalentrv.perf.level.used";
    public static final String PERF_LEVEL_STATE_USED = "apis.sptalentrv.perf.level.state.used";
    public static final String PERF_PROJECT_KEY_EXIST = "apis.sptalentrv.perf.key.exist";
    public static final String PERF_SCORE_TOTAL_RANGE = "apis.sptalentrv.perf.score.total.range";
    public static final String PERF_CLEAR_ACTIVE_PROJECTS = "apis.sptalentrv.perf.clear.active.projects";
    public static final String PERF_USER_IDS_EMPTY = "apis.sptalentrv.perf.user.ids.empty";
    public static final String PERF_PERIOD_IDS_EMPTY = "apis.sptalentrv.perf.period.ids.empty";
    public static final String PERF_USER_IDS_TOO_MANY = "apis.sptalentrv.perf.user.ids.too.many";
    public static final String PERF_PERIOD_IN_USE = "apis.sptalentrv.perf.period.in.use";

    public static final String DMP_TASK_TYPE_NOT_NULL = "apis.sptalentrv.dmp.task_type.not.null";
    public static final String DMP_ID_NOT_BLANK = "apis.sptalentrv.dmp.id.not.blank";
    public static final String DMP_TASK_NAME_NOT_BLANK = "apis.sptalentrv.dmp.task_name.not.blank";
    public static final String DMP_TASK_NAME_CONFLICT = "apis.sptalentrv.dmp.task_name.conflict";
    public static final String DMP_NOT_EXISTED = "apis.sptalentrv.dmp.not.existed";
    public static final String DMP_TASK_NOT_EXISTED = "apis.sptalentrv.dmp.task.not.existed";
    public static final String DMP_TASK_ID_NOT_BLANK = "apis.sptalentrv.dmp.task_id.not.blank";
    public static final String DMP_TASK_DIM_TYPE_NOT_NULL = "apis.sptalentrv.dmp.task.dim.type.not.null";
    public static final String DMP_JQ_DIM_NOT_EXISTED = "apis.sptalentrv.dmp.jq_dim.not.existed";
    public static final String DMP_JQ_DIM_ID_NOT_EMPTY = "apis.sptalentrv.dmp.task.dim.jq_dim.id.not.empty";
    public static final String DMP_FORM_CONFIG_EMPTY = "apis.sptalentrv.dmp.form.config.empty";
    public static final String DMP_TASK_DIM_EMPTY = "apis.sptalentrv.dmp.task.dim.empty";
    public static final String DMP_TASK_ACTIVE_STATUS_NOT_NUL = "apis.sptalentrv.dmp.task.active_status.not.null";
    public static final String DMP_TASK_ACTIVE_STATUS_RANGE_EXCEEDED = "apis.sptalentrv.dmp.task.active_status.range.exceeded";
    public static final String DMP_MATCH_RULE_NOT_FOUND = "apis.sptalentrv.dmp.match.rule.not.found";
    public static final String DMP_TASK_NOT_START = "apis.sptalentrv.dmp.task.not_start";
    public static final String DMP_RULE_NOT_EXISTS = "apis.sptalentrv.dmp.rule.notExists";
    public static final String DMP_TASK_EMPTY = "apis.sptalentrv.dmp.task.empty";
    public static final String DMP_DIM_ID_IS_NULL = "apis.sptalentrv.dmp.project.dim.id.is.null";
    public static final String DMP_RULE_LAYER_NOT_FOUND = "apis.sptalentrv.dmp.rule.layer.not.found";
    public static final String DMP_NAME_REPEAT = "apis.sptalentrv.dmp.name.repeat";
    public static final String DMP_COPY_STATUS = "apis.sptalentrv.dmp.copy.status.error";
    public static final String DMP_FINISHED_CANNOT_MODIFY = "apis.sptalentrv.dmp.finished.cannot.modify";
    public static final String DMP_PLAN_ID_NOT_BLANK = "apis.sptalentrv.dmp.plan.id.not.blank";
    public static final String DMP_USER_CHECKIN_MAX_SIZE = "apis.sptalentrv.dmp.user.checkin.max.size";
    public static final String DMP_PLAN_USED = "apis.sptalentrv.dmp.plan.used";
    public static final String DMP_PLAN_EMPTY = "apis.sptalentrv.dmp.plan.empty";
    public static final String DMP_DISABLE_EVAL_RANGE = "apis.sptalentrv.dmp.disableEval.range";
    public static final String DMP_DISABLE_EVAL_ERROR = "apis.sptalentrv.dmp.disableEval.error";
    public static final String DMP_STOP_TIME_ERROR = "apis.sptalentrv.dmp.stopTime.error";
    public static final String DMP_CONF_NOT_EXISTS = "apis.sptalentrv.dmp.conf.not.exists";
    public static final String DMP_END_TIME_EXPIRED = "apis.sptalentrv.dmp.endTime.expired";
    public static final String DMP_CANNOT_PUBLISH_OR_TIMED = "apis.sptalentrv.dmp.cannot.publish.or.timed";
    public static final String DMP_LAUNCH_TIME_MUST_AFTER_NOW = "apis.sptalentrv.dmp.launch_time.expired";
    public static final String DMP_STATUS_NOT_TIMED_PUBLISH = "apis.sptalentrv.dmp.status.notTimedPublish";
    public static final String DMP_TASK_CREATE_FAILED = "apis.sptalentrv.dmp.task.create.failed";
    public static final String DMP_TASK_JOB_NOT_EXISTS = "apis.sptalentrv.dmp.task.job.not.exists";
    public static final String DMP_SCOPE_DEPT_IDS_NO_PERMISSION = "apis.sptalentrv.auth.dept_ids.no_permission";
    public static final String DMP_COPY_DIM_ID_NULL = "apis.sptalentrv.dmp.copy.dimId.null";
    public static final String DMP_COPY_RULE_NULL = "apis.sptalentrv.dmp.copy.rule.null";
    public static final String DMP_RULE_WEIGHT_ERROR = "apis.sptalentrv.dmp.rule_weight.error";
    public static final String DMP_COPY_RULE_LAYER_NULL = "apis.sptalentrv.dmp.copy.rule.layer.null";
    public static final String DMP_COPY_TASK_DIM_NULL = "apis.sptalentrv.dmp.copy.task.dim.null";
    public static final String DMP_COPY_TASK_NULL = "apis.sptalentrv.dmp.copy.task.null";
    public static final String DMP_RULE_SCORE_DETAIL_EMPTY = "apis.sptalentrv.dmp.rule.score.detail.empty";
    public static final String DMP_POS_INFO_NOT_EMPTY = "apis.sptalentrv.user.pos.infos.not.empty";
    public static final String DMP_NOT_EXISTS = "apis.sptalentrv.dmp.notExists";
    public static final String DMP_USER_NOT_EXISTS = "apis.sptalentrv.dmp.user.not.exists";
    public static final String DMP_RULE_NOT_EXISTED = "apis.sptalentrv.dmp.rule.not.existed";
    public static final String DMP_TASK_NAME_TOO_LONG = "apis.sptalentrv.dmp.task.nam e.too.long";
    public static final String DMP_TASK_DESC_TOO_LONG = "apis.sptalentrv.dmp.task.desc.too.long";
    public static final String DMP_USER_AUTO_GROUP_NOT_EXISTS = "apis.sptalentrv.dmp.user.auto.group.not.exists";
    public static final String DMP_START_END_TIME_INVALID = "apis.sptalentrv.dmp.startEndTime.invalid";

    public static final String AUTH_TIANHE_REQUEST_FAILED = "apis.sptalentrv.auth.tianhe.request.failed";
    public static final String AUTH_TIANHE_TOKEN_INVALID = "apis.sptalentrv.auth.tianhe.token.invalid";

    public static final String AI_HELPER_CHAT_PAIR_CURR_NODE_INSTANCE_ID_NOT_NULL = "apis.sptalentrv.ai.helper.chat.pair.currNodeInstanceId.not.null";
    public static final String AI_HELPER_CHAT_PAIR_MODEL_USER_CONTENT_NOT_NULL = "apis.sptalentrv.ai.helper.chat.pair.modelUserContent.not.null";
    public static final String AI_HELPER_CHAT_SESSION_ID_NOT_BLANK = "apis.sptalentrv.ai.helper.chat.sessionId.not.blank";
    public static final String AI_HELPER_CHAT_MESSAGE_USEFUL_EXCEED = "apis.sptalentrv.ai.helper.chat.message.useful.exceed";
    public static final String AI_HELPER_CHAT_ANSWER_NOT_NULL = "apis.sptalentrv.ai.helper.chat.answer.not.null";
    public static final String AI_HELPER_CHAT_QUESTION_NOT_NULL = "apis.sptalentrv.ai.helper.chat.question.not.null";
    public static final String AI_HEALPER_CHAT_SESSION_NOT_FOUND = "apis.sptalentrv.ai.helper.chat.session.not.found";

    public static final String PROFILE_PARAM_ID_EMPTY = "apis.sptalentrv.profile.id.notEmpty";
    public static final String PROFILE_NOT_FOUND = "apis.sptalentrv.profile.notFound";
    public static final String PROFILE_INDICATOR_NOT_FOUND = "apis.sptalentrv.profile.indicator.notFound";
    public static final String PROFILE_INTERFACE_CALCULATE_LOCK = "apis.sptalentrv.profile.interface.calculate.lock";

    // xpd项目
    public static final String XPD_NOT_EXIST = "apis.sptalentrv.xpd.notExist";
    public static final String XPD_ID_EMPTY = "apis.sptalentrv.xpd.id.empty";
    public static final String XPD_MODELID_NOT_EXIST = "apis.sptalentrv.xpd.modelId.notEmpty";
    public static final String XPD_MODEL_NOT_EXIST = "apis.sptalentrv.xpd.model.notExist";
    public static final String XPD_MODEL_DIM_NOT_EXIST = "apis.sptalentrv.xpd.model.dim.notExist";
    public static final String XPD_MODEL_DIM_SIZE = "apis.sptalentrv.xpd.model.dim.size";
    public static final String XPD_MODEL_PERF_DIM_NOT_EXIST = "apis.sptalentrv.perf.activity.model.perf.dim.notExist";
    public static final String XPD_PROJECT_CAN_NOT_FINISH = "apis.sptalentrv.xpd.project.can.not.finish";
    public static final String XPD_PROJECT_NAME_NOT_BLANK = "apis.sptalentrv.xpd.project.name.not.blank";
    public static final String XPD_PROJECT_NAME_SIZE = "apis.sptalentrv.xpd.project.name.size";
    public static final String XPD_PROJECT_CATEGORYID_NOT_BLANK = "apis.sptalentrv.xpd.project.categoryId.not.blank";
    public static final String XPD_PROJECT_MANAGER_NOT_EMPTY = "apis.sptalentrv.xpd.project.manager.not.empty";
    public static final String XPD_PROJECT_DESCRIPTION_SIZE = "apis.sptalentrv.xpd.project.description.size";
    public static final String XPD_PROJECT_STARTTIME_NOT_NULL = "apis.sptalentrv.xpd.project.startTime.not.null";
    public static final String XPD_PROJECT_ENDTIME_NOT_NULL = "apis.sptalentrv.xpd.project.endTime.not.null";
    public static final String XPD_PROJECT_MODELID_NOT_EMPTY = "apis.sptalentrv.xpd.project.modelId.not.empty";
    public static final String XPD_PROJECT_MANAGER_MAX = "apis.sptalentrv.xpd.project.manager.max";
    public static final String ACTV_NOT_EXIST = "apis.sptalentrv.actv.notexist";
    public static final String APIS_AUDIT_NOT_EXIST = "apis.sptalentrv.audit.error";
    public static final String XPD_DATE_RANGE_ERROR = "apis.sptalentrv.date.range.error";
    // xpd落位规则
    public static final String XPD_RULE_CONF_NOT_FOUND = "apis.sptalentrv.xpd.rule.conf.not.found";
    public static final String XPD_RULE_CONF_CHECK_FAIL = "apis.sptalentrv.xpd.rule.conf.check.fail";
    public static final String PARTICIPATION_NOT_EXIST = "apis.sptalentrv.xpd.part.not.exist";

    // xpd落位结果
    public static final String XPD_RESULT_QUERY_TARGET_ID_NOT_BLANK = "apis.sptalentrv.xpd.result.query.targetId.not.blank";
    public static final String XPD_RESULT_QUERY_DIM_COMB_ID_NOT_BLANK = "apis.sptalentrv.xpd.result.query.dimCombId.not.blank";
    public static final String XPD_RESULT_QUERY_TYPE_INVALID = "apis.sptalentrv.xpd.result.query.queryType.invalid";

    // xpd盘点设置
    public static final String XPD_GRID_NOT_FOUND = "apis.sptalentrv.xpd.grid.not.found";
    public static final String XPD_GRID_CELL_NOT_FOUND = "apis.sptalentrv.xpd.grid.cell.not.found";
    public static final String XPD_GRID_RATIO_NOT_FOUND = "apis.sptalentrv.xpd.grid.ratio.not.found";
    public static final String XPD_GRID_TYPE_INVALID = "apis.sptalentrv.xpd.grid.type.invalid";
    public static final String XPD_DIM_COMB_NOT_FOUND = "apis.sptalentrv.xpd.dimComb.not.found";
    public static final String XPD_DIM_COMB_XPD_ID_INVALID = "apis.sptalentrv.xpd.dimComb.xpdId.invalid";
    public static final String XPD_DIM_COMB_ID_EMPTY = "apis.sptalentrv.xpd.dimComb.id.empty";
    public static final String XPD_GRID_ID_EMPTY = "apis.sptalentrv.xpd.grid.id.empty";
    public static final String XPD_CELL_INDEX_EMPTY = "apis.sptalentrv.xpd.cell.index.empty";

    // xpd盘点人员
    public static final String XPD_USER_RESULT_EXPORT_AOM_ACT_ID_NOT_EXIST = "apis.sptalentrv.xpd.user.result.export.aom.actId.not.exist";
    public static final String XPD_USER_RESULT_EXPORT_ACTIVITY_ARRANGE_NOT_EXIST = "apis.sptalentrv.xpd.user.result.export.activity.arrange.not.exist";
    public static final String XPD_USER_RESULT_EXPORT_ACTIVITY_ARRANGE_EXT_NOT_EXIST = "apis.sptalentrv.xpd.user.result.export.activity.arrange.ext.not.exist";
    public static final String XPD_USER_RESULT_EXPORT_PERIODIDS_NOT_EXIST = "apis.sptalentrv.xpd.user.result.export.periodids.not.exist";
    public static final String XPD_USER_MEMBERID_NULL = "apis.sptalentrv.xpd.user.memberId.null";

    // xpd其他
    public static final String XPD_ACTION_PLAN_COMMAND_TARGET_TYPE_INVALID = "apis.sptalentrv.xpd.actionplan.command.targetType.invalid";
    public static final String XPD_ACTION_PLAN_COMMAND_DIM_INVALID = "apis.sptalentrv.xpd.actionplan.command.dim.invalid";
    public static final String XPD_ACTION_PLAN_COMMAND_TRAINING_ID_EMPTY = "apis.sptalentrv.xpd.actionplan.command.trainingId.empty";
    public static final String XPD_ACTION_PLAN_COMMAND_POOL_ID_EMPTY = "apis.sptalentrv.xpd.actionplan.command.poolId.empty";
    public static final String XPD_ACTION_PLAN_COMMAND_DIMCOMB_INVALID = "apis.sptalentrv.xpd.actionplan.command.dimcomb.invalid";
    public static final String XPD_ACTION_PLAN_COMMAND_GRID_LEVEL_ID_INVALID = "apis.sptalentrv.xpd.actionplan.command.gridlevelId.invalid";
    public static final String XPD_ACTION_PLAN_COMMAND_CELL_ID_INVALID = "apis.sptalentrv.xpd.actionplan.command.cellId.invalid";
    public static final String XPD_ACTION_PLAN_COMMAND_LEVEL_ID_INVALID = "apis.sptalentrv.xpd.actionplan.command.levelId.invalid";
    public static final String XPD_ACTION_PLAN_COMMAND_RADIO_TYPE_INVALID = "apis.sptalentrv.xpd.action.plan.command.radio.type.invalid";
    public static final String XPD_ACTION_PLAN_USER_ID_EMPTY = "apis.sptalentrv.xpd.actionplan.userId.empty";

    // activity绩效评估活动
    public static final String ACTIVITY_PERF_PERIOD_EMPTY = "apis.sptalentrv.perf.activity.period.empty";
    public static final String ACTIVITY_PERF_IMPORT_NO_USERNAME = "apis.sptalentrv.prj.activity.import.no.username";
    public static final String ACTIVITY_PERF_IMPORT_PERIOD_ERROR = "apis.sptalentrv.prj.activity.import.period.error";
    public static final String ACTIVITY_PERF_IMPORT_PERIOD_IMPORTERROR = "apis.sptalentrv.perf.activity.period.importerror";
    public static final String PERF_RESULT_ORDERNUM_NULL = "apis.sptalentrv.perf.conf.ordernum.notnull";
    public static final String PERF_RESULT_RULE_SCORE_MAX = "apis.sptalentrv.perf.conf.score.maxlength";
    public static final String PERF_RESULT_CONF_RULECONF_NULL = "apis.sptalentrv.perf.conf.ruleconf.notnull";
    public static final String XPD_STATUS_CAN_NOT_EDIT = "apis.sptalentrv.xpd.status.can.not.edit";
    public static final String XPD_STATUS_CAN_NOT_PUBLISH = "apis.sptalentrv.xpd.status.can.not.publish";
    public static final String XPD_STATUS_CAN_NOT_DELETE = "apis.sptalentrv.xpd.status.can.not.delete";
    public static final String XPD_STATUS_CAN_NOT_FINISH = "apis.sptalentrv.xpd.status.can.not.finish";
    public static final String PERF_INDICATOR_EXITE = "apis.sptalentrv.perf.activity.indicator.exists";
    public static final String ACTIVITY_PROFILE_PARAM_ERROR = "apis.sptalentrv.progile.activity.param.error";
    public static final String ACTV_ID_NOT_BLANK = "apis.sptalentrv.perf.activity.actvid.error";
    public static final String ACTIVITY_NOT_EXIST = "apis.sptalentrv.aom.activity.null";
    public static final String XPD_STATUS_CAN_NOT_EDIT_FOR_DOING = "apis.sptalentrv.xpd.status.can.not.edit.for.doing";
    public static final String PERF_ACTIVITY_PERIODID_NOTEXIST = "apis.sptalentrv.perf.activity.periodid.notexist";
    public static final String PERF_ACTIVITY_PERIODID_TOTAL_SCORE_NOTEXIST = "apis.sptalentrv.perf.activity.periodid.totalscore.notexist";
    public static final String PERF_ACTIVITY_PERIODID_NOT_NULL = "apis.sptalentrv.perf.activity.periods.notnull";
    public static final String ACTIVITY_FORM_DATA_ERROR = "apis.sptalentrv.perf.activity.form.data.error";


    // activity动态人才评估

    //-----------盘点规则 start------------
    public static final String XPD_RULE_NOT_EXIST = "apis.sptalentrv.xpd.rule.notexist";
    public static final String XPD_RULE_CONF_ID_EMPTY = "apis.sptalentrv.xpd.rule.conf.id.empty";
    public static final String XPD_RULE_CONF_NOT_EXIST = "apis.sptalentrv.xpd.rule.conf.notexist";
    public static final String XPD_RULE_CONF_EXIST = "apis.sptalentrv.xpd.rule.conf.exist";
    public static final String XPD_RULE_CONF_MODIFIED = "apis.sptalentrv.xpd.rule.conf.modified";
    public static final String XPD_RULE_CONF_GENERATING = "apis.sptalentrv.xpd.rule.conf.generating";
    public static final String XPD_RULE_CONF_VERSION_EMPTY = "apis.sptalentrv.xpd.rule.conf.version.empty";
    public static final String XPD_RULE_CONF_DIM_EMPTY = "apis.sptalentrv.xpd.rule.conf.dim.empty";
    public static final String XPD_RULE_CONF_DIMIDS_EMPTY = "apis.sptalentrv.xpd.rule.conf.dimids.empty";
    public static final String XPD_RULE_CONF_RESULTTYPE_EMPTY = "apis.sptalentrv.xpd.rule.conf.resulttype.empty";
    public static final String XPD_RULE_CONF_GRIDID_EMPTY = "apis.sptalentrv.xpd.rule.conf.gridid.empty";
//    public static final String XPD_RULE_DIM_NOTEXIST = "apis.sptalentrv.xpd.rule.dim.notexist";
    public static final String XPD_RULE_CALC_EMPTY = "apis.sptalentrv.xpd.rule.calc.empty";
    public static final String XPD_RULE_CALC_DIM_IMPORT_ERROR = "apis.sptalentrv.xpd.rule.calc.dim.import.error";
    public static final String XPD_RULE_CALC_INDICATOR_EMPTY = "apis.sptalentrv.xpd.rule.calc.indicator.empty";
    public static final String XPD_RULE_CALC_INDICATOR_NOTEXIST = "apis.sptalentrv.xpd.rule.calc.indicator.notexist";
    public static final String XPD_RULE_CALC_INDICATOR_REF_EMPTY = "apis.sptalentrv.xpd.rule.calc.indicator.ref.empty";
    public static final String XPD_RULE_CALC_INDICATOR_REF_ERROR = "apis.sptalentrv.xpd.rule.calc.indicator.ref.error";
    public static final String XPD_RULE_CALC_INDICATOR_CALMETHOD_ERROR = "apis.sptalentrv.xpd.rule.calc.indicator.calcmethod.error";
    public static final String XPD_RULE_LEVEL_EMPTY = "apis.sptalentrv.xpd.rule.level.empty";
    public static final String XPD_RULE_LEVEL_ERROR = "apis.sptalentrv.xpd.rule.level.error";
    public static final String XPD_RULE_LEVEL_MAXLENGTH_ERROR = "apis.sptalentrv.xpd.rule.level.maxlength.error";
    public static final String XPD_RULE_LEVELVALUE_OUTOFRANGE = "apis.sptalentrv.xpd.rule.levelvalue.outofrange";
    public static final String XPD_RULE_LEVEL_PERCENT_SUM_ERROR = "apis.sptalentrv.xpd.rule.level.percent.sum.error";
    public static final String XPD_RULE_LEVEL_VALUE_DECREASE_ERROR = "apis.sptalentrv.xpd.rule.level.value.decrease.error";
    public static final String XPD_RULE_LEVEL_VALUE_MIN_ZERO_ERROR = "apis.sptalentrv.xpd.rule.level.value.min.zero.error";
    public static final String XPD_RULE_RESULTTYPE_ERROR = "apis.sptalentrv.xpd.rule.resulttype.error";
    public static final String XPD_RULE_CALC_DIM_DUPLICATE = "apis.sptalentrv.xpd.rule.calc.dim.duplicate";
    public static final String XPD_RULE_INDICATOR_DUPLICATE = "apis.sptalentrv.xpd.rule.indicator.duplicate";
    public static final String XPD_RULE_CALC_DIM_NOTEXIST = "apis.sptalentrv.xpd.rule.calc.dim.notexist";
    public static final String XPD_RULE_CALC_DIM_WEIGHT_EMPTY = "apis.sptalentrv.xpd.rule.calc.weight.empty";
    public static final String XPD_RULE_CALC_DIM_WEIGHT_SUM_ERROR = "apis.sptalentrv.xpd.rule.calc.weight.sum.error";
    public static final String XPD_RULE_CALC_FORMULA_ERROR = "apis.sptalentrv.xpd.rule.calc.formular.error";
    public static final String XPD_DIM_NOT_EXIST = "apis.sptalentrv.xpd.dim.notexist";
    public static final String XPD_DIM_RULE_NOT_EXIST = "apis.sptalentrv.xpd.dim.rule.notexist";
    public static final String XPD_DIM_RULE_CALC_EMPTY = "apis.sptalentrv.xpd.dim.rule.calc.empty";
    public static final String XPD_DIM_RULE_ACTV_PERF_NOTEXIST = "apis.sptalentrv.xpd.dim.rule.actv.perf.notexist";
    public static final String XPD_DIM_RULE_REF_NOTEXIST = "apis.sptalentrv.xpd.dim.rule.ref.notexist";
    public static final String XPD_DIM_RULE_CALCTYPE_ERROR = "apis.sptalentrv.xpd.dim.rule.calctype.error";
    public static final String XPD_DIM_RULE_RESULTTYPE_ERROR = "apis.sptalentrv.xpd.dim.rule.resulttype.error";
    public static final String XPD_DIM_RULE_FORMULA_EMPTY = "apis.sptalentrv.xpd.dim.rule.formula.empty";
    public static final String XPD_DIM_RULE_FORMULA_ERROR = "apis.sptalentrv.xpd.dim.rule.formula.error";
    public static final String XPD_DIM_RULE_LEVEL_EMPTY = "apis.sptalentrv.xpd.dim.rule.level.empty";
    public static final String XPD_DIM_RULE_LEVEL_RESULT_EMPTY = "apis.sptalentrv.xpd.dim.rule.level.result.empty";
    public static final String XPD_DIM_RULE_LEVEL_RESULT_ERROR = "apis.sptalentrv.xpd.dim.rule.level.result.error";
    public static final String XPD_DIM_RULE_SUBDIM_ERROR = "apis.sptalentrv.xpd.dim.rule.subdim.error";
    public static final String XPD_DIM_RULE_SUBDIM_WEIGHT_EMPTY = "apis.sptalentrv.xpd.dim.rule.subdim.weight.empty";
    public static final String XPD_DIM_RULE_CALC_WEIGHT_SUM_ERROR = "apis.sptalentrv.xpd.dim.rule.calc.weight.sum.error";
    public static final String XPD_DIM_RULE_INDICATOR_DUPLICATE = "apis.sptalentrv.xpd.dim.rule.indicator.duplicate";
    public static final String XPD_DIM_RULE_INDICATOR_NOTEXIST = "apis.sptalentrv.xpd.dim.rule.indicator.notexist";
    public static final String XPD_DIM_RULE_INDICATOR_REF_EMPTY = "apis.sptalentrv.xpd.dim.rule.indicator.ref.empty";
    public static final String XPD_DIM_RULE_INDICATOR_WIGHT_EMPTY = "apis.sptalentrv.xpd.dim.rule.indicator.weight.empty";
    public static final String XPD_DIM_RULE_INDICATOR_CALCMETOD_EMPTY = "apis.sptalentrv.xpd.dim.rule.indicator.calcmethod.empty";
    public static final String XPD_DIM_RULE_INDICATOR_CALCMETOD_ERROR = "apis.sptalentrv.xpd.dim.rule.indicator.calcmethod.error";
    public static final String XPD_RULE_CONF_DIMIDS_MIN = "apis.sptalentrv.xpd.rule.conf.dimids.min";
    public static final String XPD_SCORE_EXCEED_MAX_VALUE = "apis.sptalentrv.xpd.score.exceed.max.value";
    //-----------盘点规则 end------------


    // 宫格设置
    // 宫格名称重复
    public static final String XPD_GRID_NAME_REPEAT = "apis.sptalentrv.xpd.grid.name.repeat";
    public static final String XPD_GRID_DEFAULT_SHOW_ONE = "apis.sptalentrv.xpd.grid.show.type.one";
    public static final String XPD_GRID_DEFAULT_SHOW_ZERO = "apis.sptalentrv.xpd.grid.show.type.zero";
    public static final String XPD_GRID_DIM_COMB_REPEAT = "apis.sptalentrv.xpd.grid.dim.comb.repeat";

    public static final String XPD_CALC_RESULT_ING = "apis.sptalentrv.xpd.calc.result.ing";
    public static final String XPD_DIMIDS_EMPTY = "apis.sptalentrv.xpd.dimids.empty";
    public static final String XPD_IMPT_GRID_LEVEL_NOT_EXIST = "apis.sptalentrv.xpd.impt.grid.level.not.exist";

}
