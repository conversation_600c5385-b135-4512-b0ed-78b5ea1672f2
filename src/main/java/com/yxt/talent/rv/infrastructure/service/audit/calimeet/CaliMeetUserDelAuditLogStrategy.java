package com.yxt.talent.rv.infrastructure.service.audit.calimeet;

import com.yxt.auditlog.AuditLog;
import com.yxt.auditlog.AuditLogContext;
import com.yxt.auditlog.bean.AuditDetail;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calmeet.CaliMeetUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CaliMeetUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogStrategy;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;

@Slf4j
@Service
@Scope("prototype")
@RequiredArgsConstructor
public class CaliMeetUserDelAuditLogStrategy implements AuditLogStrategy {
    private final AuthService authService;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final CaliMeetMapper caliMeetMapper;
    private final CaliMeetUserMapper caliMeetUserMapper;

    private List<String> delUserIds;

    @Override
    public void doPrepare(String[] argsNames, Object[] args) {

        HttpServletRequest request = (HttpServletRequest) args[0];
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        String mtId = (String) args[1];
        List<String> idList = (ArrayList) args[2];

        List<CaliMeetUserPO> mtUser =
                caliMeetUserMapper.listByOrgIdAndMeetingIdAndIdIn(
                        operator.getOrgId(), mtId, idList);
        delUserIds = mtUser.stream().map(CaliMeetUserPO::getUserId).collect(Collectors.toList());
    }

    @Override
    public void doExecute(AuditLog auditLog, String[] argsNames, Object[] args, Object result) {
        if (log.isInfoEnabled()) {
            log.info("LOG63440:{}", BeanHelper.bean2Json(argsNames, ALWAYS));
        }
        if (args == null || args.length == 0) {
            log.error("LOG63460:{}", this.getClass().getName() + " : 日志记录失败，参数列表为空");
            return;
        }

        String mtId = (String) args[1];
        CaliMeetPO cm = caliMeetMapper.selectByIdAndOrgId(mtId, auditLog.getOrgId());
        log.debug(
                "LOG63450:cls={}, 业务Id={}, orgId={}", this.getClass().getName(), mtId,
                auditLog.getOrgId());
        String bizName = mtId; // 极端情况，从库无数据，就用业务Id做名字
        if (cm != null) {
            bizName = cm.getMeetName();
        }

        // 组装日志
        String userString = getUserString(auditLog.getOrgId(), delUserIds);
        AuditDetail ad = getDeleteAuditDetail(AuditLogHelper.Module.CALIBRATION.getCode());
        ad.setEntityId(bizName);
        ad.setEntityName(
                AuditLogHelper.Module.CALIBRATION.getName() + "-" + bizName + "-删除人员-" +
                userString);
        auditLog.setDetails(Collections.singletonList(ad));
        AuditLogContext.asyncCommit(auditLog);
    }

    private String getUserString(String orgId, List<String> userIds) {
        List<UdpLiteUserPO> tcList = udpLiteUserMapper.selectByUserIds(orgId, userIds);
        StringBuilder tcSb = new StringBuilder();
        tcList.forEach(a -> tcSb.append(a.getFullname())
                // .append("（").append(a.getUsername()).append("）")
                .append("，"));

        if (!tcSb.isEmpty()) {
            tcSb.delete(tcSb.length() - 1, tcSb.length());
        }

        return tcSb.toString();
    }
}
