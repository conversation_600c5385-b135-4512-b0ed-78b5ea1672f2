package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRulePO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface XpdDimRuleMapper extends CommonMapper<XpdDimRulePO> {
    int insert(XpdDimRulePO record);

    int insertOrUpdate(XpdDimRulePO record);

    XpdDimRulePO selectByPrimaryKey(String id);

    XpdDimRulePO selectById(String id);

    List<XpdDimRulePO> listByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    List<XpdDimRulePO> listBySdDimIds(@Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("sdDimIds") Collection<String> sdDimIds);

    /**
     * 逻辑删
     *
     * @param orgId    机构ID
     * @param xpdId    盘点项目ID
     * @param sdDimIds 维度IDs
     * @param userId   操作人ID
     */
    void deleteBySdDimIds(@Param("orgId") String orgId, @Param("xpdId") String xpdId,
                          @Param("sdDimIds") Collection<String> sdDimIds, @Param("userId") String userId);

    /**
     * 逻辑删
     *
     * @param userId 操作人ID
     * @param ids    ids
     */
    void deleteByIds(@Param("userId") String userId, @Param("ids") Collection<String> ids);

    /**
     * 查具体维度的规则
     *
     * @param orgId   机构ID
     * @param xpdId   盘点项目ID
     * @param sdDimId 维度ID-标准
     * @return XpdDimRulePO
     */
    XpdDimRulePO getByXpdIdAndSdDimId(@Param("orgId") String orgId, @Param("xpdId") String xpdId,
                                      @Param("sdDimId") String sdDimId);

    void updateBatch(@Param("list") List<XpdDimRulePO> list);

    void setRuleDisable(@Param("orgId") String orgId, @Param("xpdId") String xpdId,
                        @Param("sdDimId") String sdDimId, @Param("userId") String userid);

    void setRuleEnable(@Param("orgId") String orgId, @Param("xpdId") String xpdId,
        @Param("sdDimId") String sdDimId, @Param("userId") String userid);

    void batchInsert(List<XpdDimRulePO> newDimRuleList);

    void deleteByXpdId(
            @Param("orgId") String orgId,
            @Param("userId") String userId,
            @Param("xpdId") String xpdId);

}