package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityObjectiveResultPO;
import com.yxt.talent.rv.infrastructure.repository.xpd.bean.XpdIndicatorResultDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RvActivityObjectiveResultMapper extends CommonMapper<ActivityObjectiveResultPO> {

    int insert(ActivityObjectiveResultPO record);

    ActivityObjectiveResultPO selectByPrimaryKey(Long id);

    int updateByPrimaryKey(ActivityObjectiveResultPO record);

    int updateBatch(@Param("list") List<ActivityObjectiveResultPO> list);

    int batchInsert(@Param("list") List<ActivityObjectiveResultPO> list);

    List<XpdIndicatorResultDto> queryByUserIds(
        @Param("orgId") String orgId,
        @Param("actvId") String actvId,
        @Param("userIds") List<String> userIds,
        @Param("objectiveIds") List<String> objectiveIds);
}