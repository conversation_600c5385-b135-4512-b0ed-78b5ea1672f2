package com.yxt.talent.rv.infrastructure.service.audit.calimeet;

import com.yxt.auditlog.AuditLog;
import com.yxt.auditlog.AuditLogContext;
import com.yxt.auditlog.bean.AuditDetail;
import com.yxt.auditlog.bean.EntityChange;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetCreateCmd;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetCreateResultVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.PrjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.PrjPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.common.util.BeanHelper.bean2Json;

@Slf4j
@Service
@Scope("prototype")
@RequiredArgsConstructor
public class CaliMeetCreateAuditLogStrategy implements AuditLogStrategy {
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final PrjMapper prjMapper;


    @Override
    public void doExecute(AuditLog auditLog, String[] argsNames, Object[] args, Object result) {
        log.info("LOG63320:{}", bean2Json(argsNames, ALWAYS));
        if (args == null || args.length == 0) {
            log.error("LOG63330:{}", this.getClass().getName() + " : 日志记录失败，参数列表为空");
            return;
        }

        CaliMeetCreateCmd bean = (CaliMeetCreateCmd) args[1];
        CaliMeetCreateResultVO beanResult = (CaliMeetCreateResultVO) result;

        if (bean == null || beanResult == null) {
            log.warn("LOG20050:");
            return;
        }

        // 组装日志
        List<EntityChange> list =
                getEntityChangeList(bean, auditLog.getOrgId(), auditLog.getOperator().getId());
        AuditDetail ad = getCreateAuditDetail(AuditLogHelper.Module.CALIBRATION.getCode());
        ad.setEntityId(beanResult.getId());
        ad.setEntityName(AuditLogHelper.Module.CALIBRATION.getName() + "-" + bean.getMeetName());
        ad.setChanges(list);
        auditLog.setDetails(Collections.singletonList(ad));
        log.info("LOG61620:{}", bean2Json(list, ALWAYS));
        AuditLogContext.asyncCommit(auditLog);
    }

    private List<EntityChange> getEntityChangeList(
            CaliMeetCreateCmd bean, String orgId,
            String userId) {
        List<EntityChange> result = new ArrayList<>();

        result.add(new EntityChange().setFieldName("会议名称").setNewValue(bean.getMeetName()));
        String format = FastDateFormat.getInstance("yyyy-MM-dd HH:mm").format(bean.getMeetTime());
        result.add(new EntityChange().setFieldName("会议时间").setNewValue(format));

        // 人才委员会
        if (CollectionUtils.isNotEmpty(bean.getTalentCommitteeList())) {
            result.add(new EntityChange().setFieldName("人才委员会")
                    .setNewValue(getUserString(orgId, bean.getTalentCommitteeList())));
        }

        // 会议组织者
        if (CollectionUtils.isEmpty(bean.getOrganizerList())) {
            bean.setOrganizerList(Collections.singletonList(userId));
        }
        result.add(
                new EntityChange().setFieldName("会议组织者")
                        .setNewValue(getUserString(orgId, bean.getOrganizerList())));

        // 项目名称
        PrjPO prj = prjMapper.selectByOrgIdAndId(orgId, bean.getProjectId());
        String projectName = prj != null ? prj.getProjectName() : "";
        result.add(new EntityChange().setFieldName("所属盘点项目").setNewValue(projectName));

        return result;
    }

    private String getUserString(String orgId, List<String> userIds) {
        List<UdpLiteUserPO> tcList = udpLiteUserMapper.selectByUserIds(orgId, userIds);
        StringBuilder tcSb = new StringBuilder();
        tcList.forEach(a -> tcSb.append(a.getFullname())
                .append("（")
                .append(a.getUsername())
                .append("）")
                .append("，"));

        if (!tcSb.isEmpty()) {
            tcSb.delete(tcSb.length() - 1, tcSb.length());
        }

        return tcSb.toString();
    }
}
