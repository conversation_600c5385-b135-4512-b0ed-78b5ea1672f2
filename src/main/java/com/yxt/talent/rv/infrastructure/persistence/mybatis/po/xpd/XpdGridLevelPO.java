package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 盘点宫格分级标准
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_grid_level")
public class XpdGridLevelPO {
    /**
    * id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.XPD_GRID_LEVEL_ID)
    private String id;

    /**
    * 机构ID, 全局模板存00000000-0000-0000-0000-000000000000
    */
    private String orgId;

    /**
    * 项目ID, 机构模板存00000000-0000-0000-0000-000000000000
    */
    private String xpdId;

    /**
    * 宫格ID, 指向rv_xpd_grid.id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.XPD_GRID_ID)
    private String gridId;

    /**
    * 分级名称
    */
    private String levelName;

    /**
    * 分级名称国际化
    */
    private String levelNameI18n;

    /**
    * 排序
    */
    private Integer orderIndex;

    /**
    * 0:未删除 1:已删除
    */
    private Integer deleted;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 创建人ID
    */
    private String createUserId;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;

    /**
    * 更新人ID
    */
    private String updateUserId;
}