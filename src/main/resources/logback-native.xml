<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true" scanPeriod="5 seconds">

    <!-- 应用名 -->
    <springProperty scope="context" name="appname" source="spring.application.name" defaultValue="api"/>
    <!-- debug msg截断长度 -->
    <springProperty scope="context" name="truncateLength_Debug" source="base.service.logback.debug.truncate.length" defaultValue="50000"/>
    <!-- info msg截断长度 -->
    <springProperty scope="context" name="truncateLength_Info" source="base.service.logback.info.truncate.length" defaultValue="10000"/>
    <!-- other(access/event/error) msg截断长度 -->
    <springProperty scope="context" name="truncateLength_Other" source="base.service.logback.other.truncate.length" defaultValue="50000"/>
    <!--debug 的最大日志保存量 默认 7GB ，每个文件按照1GB切割-->
    <springProperty scope="context" name="totalSizeCap_Debug" source="base.service.logback.debug.totalSizeCap" defaultValue="7GB"/>
    <springProperty scope="context" name="maxFileSize_Debug" source="base.service.logback.debug.maxFileSize" defaultValue="1024MB"/>
    <!--info 的最大日志保存量 默认 7GB ，每个文件按照1GB切割-->
    <springProperty scope="context" name="totalSizeCap_Info" source="base.service.logback.info.totalSizeCap" defaultValue="7GB"/>
    <springProperty scope="context" name="maxFileSize_Info" source="base.service.logback.info.maxFileSize" defaultValue="1024MB"/>
    <!--other(access/event/error) 的最大日志保存量 默认 2GB ，每个文件按照512MB切割-->
    <springProperty scope="context" name="totalSizeCap_Other" source="base.service.logback.other.totalSizeCap" defaultValue="2GB"/>
    <springProperty scope="context" name="maxFileSize_Other" source="base.service.logback.other.maxFileSize" defaultValue="512MB"/>

    <springProperty scope="context" name="basedir" source="base.logback.basedir" defaultValue="${WAF_LOG_BASE_DIR:-/data/logback}"/>

    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
    <property name="CONSOLE_LOG_PATTERN" value="%clr(%d{HH:mm:ss.SSS}){faint} %clr(%5p) %clr([%21.21t]){faint} %clr([%-31.31logger{31}:%-3L]){cyan} %clr(--){faint} %m%n"/>

    <!--本地调试时,将控制台日志调整为等宽,方便调试-->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>
                ${CONSOLE_LOG_PATTERN}
            </pattern>
        </encoder>
    </appender>

    <appender name="DEBUG"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${basedir}/${appname}_debug.%d{yyyyMMdd}.%i.log
            </FileNamePattern>
            <!--最多保留10天-->
            <MaxHistory>10</MaxHistory>
            <!-- 日志总保存量为7GB -->
            <totalSizeCap>${totalSizeCap_Debug}</totalSizeCap>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!--文件达到 最大1024MB时会被压缩和切割 -->
                <maxFileSize>${maxFileSize_Debug}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%t] %-5p [%F:%L] [%X{yxtTraceId}] [%X{X-B3-TraceId} %X{X-B3-SpanId} %X{X-B3-ParentSpanId}] - %.-${truncateLength_Debug}m%n
            </pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="INFO"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${basedir}/${appname}_info.%d{yyyyMMdd}.%i.log
            </FileNamePattern>
            <!--最多保留10天-->
            <MaxHistory>10</MaxHistory>
            <!-- 日志总保存量为20GB -->
            <totalSizeCap>${totalSizeCap_Info}</totalSizeCap>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!--文件达到 最大1024MB时会被压缩和切割 -->
                <maxFileSize>${maxFileSize_Info}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%t] %-5p [%F:%L] [%X{yxtTraceId}] [%X{X-B3-TraceId} %X{X-B3-SpanId} %X{X-B3-ParentSpanId}] - %.-${truncateLength_Info}m%n
            </pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="ERROR"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${basedir}/${appname}_error.%d{yyyyMMdd}.%i.log
            </FileNamePattern>
            <!--最多保留10天-->
            <MaxHistory>10</MaxHistory>
            <!-- 日志总保存量为2GB -->
            <totalSizeCap>${totalSizeCap_Other}</totalSizeCap>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!--文件达到 最大512MB时会被压缩和切割 -->
                <maxFileSize>${maxFileSize_Other}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%t] %-5p [%F:%L] [%X{yxtTraceId}] [%X{X-B3-TraceId} %X{X-B3-SpanId} %X{X-B3-ParentSpanId}] - %.-${truncateLength_Other}m%n
            </pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
    </appender>

    <appender name="EVENTLOG"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${basedir}/${appname}_eventlog.%d{yyyyMMdd}.%i.log
            </FileNamePattern>
            <!--最多保留10天-->
            <MaxHistory>10</MaxHistory>
            <!-- 日志总保存量为20GB -->
            <totalSizeCap>${totalSizeCap_Other}</totalSizeCap>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!--文件达到 最大1024MB时会被压缩和切割 -->
                <maxFileSize>${maxFileSize_Other}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%X{yxtTraceId}] [%X{X-B3-TraceId} %X{X-B3-SpanId} %X{X-B3-ParentSpanId}] %.-${truncateLength_Other}m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>TRACE</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="ACCLOG"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${basedir}/${appname}_acclog.%d{yyyyMMdd}.%i.log</FileNamePattern>
            <!--最多保留10天-->
            <MaxHistory>10</MaxHistory>
            <!-- 日志总保存量为2GB -->
            <totalSizeCap>${totalSizeCap_Other}</totalSizeCap>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!--文件达到 最大512MB时会被压缩和切割 -->
                <maxFileSize>${maxFileSize_Other}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%X{yxtTraceId}] [%X{X-B3-TraceId} %X{X-B3-SpanId} %X{X-B3-ParentSpanId}] %.-${truncateLength_Other}m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>TRACE</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <logger name="com.yxt.common.util.TraceUtil" level="TRACE" additivity="false">
        <appender-ref ref="ACCLOG"/>
    </logger>

    <logger name="com.yxt.common.aop.ControllerAspect" level="TRACE" additivity="false">
        <appender-ref ref="EVENTLOG"/>
    </logger>

    <logger name="com.alibaba.cloud" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="DEBUG"/>
        <appender-ref ref="INFO"/>
        <appender-ref ref="ERROR"/>
    </logger>

    <logger name="com.xxl.job" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="DEBUG"/>
        <appender-ref ref="INFO"/>
        <appender-ref ref="ERROR"/>
    </logger>

    <logger name="org.apache.rocketmq" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="DEBUG"/>
        <appender-ref ref="INFO"/>
        <appender-ref ref="ERROR"/>
    </logger>

    <logger name="com.yxt" level="DEBUG" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="DEBUG"/>
        <appender-ref ref="INFO"/>
        <appender-ref ref="ERROR"/>
    </logger>

    <logger name="ch.qos.logback" level="WARN" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="DEBUG"/>
        <appender-ref ref="INFO"/>
        <appender-ref ref="ERROR"/>
    </logger>

    <logger name="org.springframework" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="DEBUG"/>
        <appender-ref ref="INFO"/>
        <appender-ref ref="ERROR"/>
    </logger>

    <logger name="org.apache.juli.logging" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="DEBUG"/>
        <appender-ref ref="INFO"/>
        <appender-ref ref="ERROR"/>
    </logger>

    <logger name="com.yxt.common.util.RedisUtil.LettuceNettyCustomizer" level="WARN" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="DEBUG"/>
        <appender-ref ref="INFO"/>
        <appender-ref ref="ERROR"/>
    </logger>

    <root level="ERROR">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="DEBUG"/>
        <appender-ref ref="INFO"/>
        <appender-ref ref="ERROR"/>
        <appender-ref ref="EVENTLOG"/>
    </root>
</configuration>
