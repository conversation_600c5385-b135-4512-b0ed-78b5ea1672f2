spring.application.name=sptalentrvapi
spring.jmx.enabled=true
spring.mvc.async.request-timeout=120000

spring.messages.encoding=UTF-8
spring.messages.cache-duration=60s
spring.messages.use-code-as-default-message=true
spring.messages.always-use-message-format=false
spring.messages.fallback-to-system-locale=true
spring.messages.basename=message,message-base,i18n

## actuator
management.endpoints.web.base-path=/act
management.endpoints.web.exposure.include=health,info,ucache,usdk-thread-pool,env,loggers,threaddump,metrics,configprops,conditions,service-registry,prometheus,sentinel,beans,mappings
management.endpoint.health.show-details=always
management.info.git.mode=full
management.health.redis.enabled=false
management.health.db.enabled=false
management.health.rabbit.enabled=false
management.health.elasticsearch.enabled=false
management.endpoint.health.group.readiness.include=ping,diskSpace
management.endpoint.health.group.liveness.include=ping,diskSpace
management.endpoint.env.post.enabled=true
management.endpoint.env.show-values=always
management.endpoint.configprops.show-values=always

#server
app.build.time=@build.time@
app.version=@project.version@

#logging
logging.config=classpath:logback-native.xml
logging.level.com.yxt=DEBUG
logging.level.com.yxt.common.util.RedisUtil=ERROR
logging.level.com.yxt.usdk.framework.webapi.config.FeignConfiguration=INFO
logging.level.com.yxt.leaf.snowflake.RecyclableRedisHolder=ERROR
feign.client.config.default.logger-level=full
spring.cloud.openfeign.client.config.default.logger-level=full

#sprv
sprv.close-user-label-cache=true
sprv.demo-org-id=56506dab-57a2-47e0-b715-1d0a88b43895
sprv.cali-meet-msg-url=/spgwnl/#/web/rvcalibration
sprv.log-config.enabled=false
sprv.log-config.ding-robot-url=https://oapi.dingtalk.com/robot/send?access_token=3310ccdf1c5460004024f8ef0badebb8db0550c7ab0055b6a1d14f5600de6bf2
sprv.log-config.save-style=1
sprv.log-config.biz-log-pkg=com.yxt.talent
sprv.log-config.observer=18550088473
sprv.auto-issue-report=0
sprv.id-map-keys=sprv_dmp_id,sprv_dmp_task_id,sprv_prj_id
prj.result.label.high={ "conditions": [ { "rules": [ { "columnType": 1, "id": "highCnt", "name": "高等级维度数量", "operators": [ { "operateType": 5, "operateRadio": 1, "operatorCnName": "pc_spmodel_eq_5", "value": 1 } ], "state": 0 }, { "columnType": 1, "id": "lowCnt", "name": "低等级维度数量", "operators": [ { "operateType": 1, "operateRadio": 2, "operatorCnName": "pc_gwnl_lbl_dimension_equal", "value": 0 } ], "state": 0 } ], "logic": 1  , "state": 0 } ], "logic": 2, "state": 0 }
prj.result.label.mid={ "conditions": [ { "rules": [ { "columnType": 1, "id": "highCnt", "name": "高等级维度数量", "operators": [ { "operateType": 5, "operateRadio": 1, "operatorCnName": "pc_spmodel_eq_5", "value": 1 } ], "state": 0 }, { "columnType": 1, "id": "lowCnt", "name": "低等级维度数量", "operators": [ { "operateType": 5, "operateRadio": 1, "operatorCnName": "pc_spmodel_eq_5", "value": 1 } ], "state": 0 } ], "logic": 1  , "state": 0 },{ "rules": [ { "columnType": 1, "id": "highCnt", "name": "高等级维度数量", "operators": [ { "operateType": 1, "operateRadio": 1, "operatorCnName": "pc_gwnl_lbl_dimension_equal", "value": 0 } ], "state": 0 }, { "columnType": 1, "id": "midCnt", "name": "中等级维度数量", "operators": [ { "operateType": 3, "operateRadio": 2, "operatorCnName": "pc_gwnl_lbl_dimension_more_than", "value": "lowCnt" } ], "state": 0 } ], "logic": 1  , "state": 0 } ], "logic": 2, "state": 0 }
prj.result.label.low={ "conditions": [ { "rules": [ { "columnType": 1, "id": "highCnt", "name": "高等级维度数量", "operators": [ { "operateType": 1, "operateRadio": 1, "operatorCnName": "pc_gwnl_lbl_dimension_equal", "value": 0 } ], "state": 0 }, { "columnType": 1, "id": "lowCnt", "name": "低等级维度数量", "operators": [ { "operateType": 5, "operateRadio": 2, "operatorCnName": "pc_spmodel_eq_5", "value": "midCnt" } ], "state": 0 } ], "logic": 1, "state": 0 } ], "logic": 2, "state": 0 }
sprv.aibox.base-url=https://api-paas.yunxuetang.cn/aibox

# 小西天土地
#sprv.aibox.appId=********************************
# 人发问答机器人
sprv.aibox.appId=********************************
sprv.aibox.ak=spAiHelper
sprv.aibox.sk=********************************
sprv.aibox.utilAppId=********************************
sprv.tianhe.base-url=https://api-info-di.yunxuetang.com.cn/tianhe
sprv.tianhe.appId=aihelper
sprv.tianhe.ak=q5j2v7T5
sprv.tianhe.sk=q5j2v7T5k3g3Q3c6w7M6p7k4a7u7q8f6
sprv.jwt.secret=${base.jwt.secret:2/TRdKkiyHZuy-imWA*44iJM(1$z_viF}
sprv.jwt.timeout=${base.jwt.expire:1209600}


#base
base.aop.enabled=true
aggregate.aop.enabled=true
base.jackson.config.enabled=true
waf.webkit.enabled=true
waf.webkit.smooth-down.enabled=false
base.lock.key-prefix=sprv:idempotent:
base.zone.date.serializer=false
base.db.hint.enabled=true
base.db.hint.master.keyword=force_master
yxt.id.worker.auto.enable=true
feign.httpclient.enabled=true


base.jwt.secret=2/TRdKkiyHZuy-imWA*44iJM(1$z_viF
base.jwt.expire=1209600
base.jwt.client.expire=1209600
base.auth.redis.cluster.nodes=***********9:6379
base.auth.redis.port=6379
base.auth.redis.password=XKH4lCe3Tr9Tpiil$
base.auth.redis.database=0
base.auth.redis.timeout=2000
base.auth.redis.jedis.pool.min-idle=4
base.auth.redis.jedis.pool.max-idle=4
base.auth.redis.jedis.pool.max-active=4
base.auth.redis.jedis.pool.max-wait=10000

#downloadcenter
downloadcenter.path=/data/downloadcenter/
downloadcenter.uperr.path=/data/downloadcenter/uperr/
downloadcenter.uperr.domain=https://uperr-phx-di-hw.yunxuetang.com.cn/

#recommend
data.recommend.kng.url=https://sg2.yunxuetang.com/std/recommendapi/vxx2/gwnl/admin/kngs
data.recommend.course.url=https://sg2.yunxuetang.com/std/recommendapi/vxx2/gwnl/user/kngs
big.data.url.ak=ebee5e7493c0527863042334604c92303c2342eb51726295839e52f390fc7110
big.data.url.sk=40f03034e02ee225e9936ff2b217a53029d3ee54733572a17c0136e85d61b586

#audit
sprv.audit.log.enable=true
base.audit-log.enabled=true
base.audit-log.service-id=sptalentrvapi
base.audit-log.trace-id=X-B3-TraceId
base.audit-log.url=https://api-phx-di-hw.yunxuetang.com.cn/sls/auditlog

# aksk
base.skmap.gwnlapi=xhXYeAk89oGnRgtN88kpgeKEGxUtJoyH
base.skmap.talentapi=FbE982wrBD6ijRlUCFp21Hauac2m6E9n
base.skmap.talentrvapi=a0dfsaFk9HN66iLm6fN5TzU2t2e63lhd
base.skmap.polestartapi=a0dfsaFk9HN66iLm6fN5TzU2t2e63lhd
base.skmap.sptalentapi=06034afef9e211ed8a9e1c34da4f7358
base.skmap.spevalapi=1edcc95af9e211ed-8a9e1c34da4f7358
base.skmap.sptalentbkapi=06034afef9e211ed8a9e1c34da4f7358
base.skmap.spmodelapi=ob1xenc6ezqwtytbv74a0u5mwozmagdu
base.skmap.sptalentrvapi=8p6omwgp6erebkecs67q6aycdgtnwkpm

#facade url
sptalentbkapi.facade.url=https://api-phx-di-hw.yunxuetang.com.cn/sptalentbk/
spevalapi.facade.url=https://api-phx-di-hw.yunxuetang.com.cn/speval/
polestarapi.facade.url=https://api-phx-di.yunxuetang.com.cn/polestar/
sptalent.facade.url=https://api-phx-di-hw.yunxuetang.com.cn/sptalent/
o2oapi.facade.url=https://api-phx-di-hw.yunxuetang.com.cn/o2o/
udp.facade.url=https://api-phx-di-hw.yunxuetang.com.cn/udp/
coreapi.facade.url=https://api-phx-di-hw.yunxuetang.com.cn/core/
msgapi.facade.url=https://api-phx-di-hw.yunxuetang.com.cn/msg/
orginit.facade.url=https://api-phx-di-hw.yunxuetang.com.cn/orginit/
udpes.facade.url=https://api-phx-di-hw.yunxuetang.com.cn/udpes/
qida.center.api.url=http://api-qidacenter.yunxuetang.com.cn/v1
udp.api.url=http://devinner.yunxuetang.com.cn/v1/udp/
feign.csmapi.url=https://api-info-di.yunxuetang.com.cn/csm/
file.facade.url=https://api-phx-di.yunxuetang.com.cn/file/
down.facade.url=https://api-phx-di-hw.yunxuetang.com.cn/down/
meeting.url=https://meetdev.yxt.com
feign.spmodelapi.url=https://api-phx-di-hw.yunxuetang.com.cn/spmodel/
global.facade.url=https://api-phx-di.yunxuetang.com.cn/global/
sptalentsd.facade.url=https://api-phx-di-hw.yunxuetang.com.cn/sptalentsd/
uacd.facade.url=https://api-phx-di-hw.yunxuetang.com.cn/uacd/
auditapi.facade.url=https://api-phx-di-hw.yunxuetang.com.cn/audit/

#datasource
spring.datasource.druid.rv.url=***************************************************************************************************************************************************************************************************************************
spring.datasource.druid.rv.username=yxt
spring.datasource.druid.rv.password=afg)gppOs22k
spring.datasource.druid.rv.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.druid.rv.initial-size=4
spring.datasource.druid.rv.max-active=8
spring.datasource.druid.rv.min-idle=4
spring.datasource.druid.rv.max-wait=60000
spring.datasource.druid.rv.socket-timeout=30000
spring.datasource.druid.rv.pool-prepared-statements=false
spring.datasource.druid.rv.validation-query=SELECT 'x' from dual
spring.datasource.druid.rv.validation-query-timeout=60
spring.datasource.druid.rv.test-on-borrow=false
spring.datasource.druid.rv.test-on-return=false
spring.datasource.druid.rv.test-while-idle=true
spring.datasource.druid.rv.time-between-eviction-runs-millis=60000
spring.datasource.druid.rv.min-evictable-idle-time-millis=300000
spring.datasource.druid.rv.max-evictable-idle-time-millis=600000

#redis
#sptalentapi.redis.cluster.nodes=***********9:6379
sptalentapi.redis.host=***********9
sptalentapi.redis.port=6379
sptalentapi.redis.password=XKH4lCe3Tr9Tpiil$
sptalentapi.redis.database=9
sptalentapi.redis.timeout=2000
sptalentapi.redis.lettuce.pool.min-idle=4
sptalentapi.redis.lettuce.pool.max-idle=4
sptalentapi.redis.lettuce.pool.max-active=4
sptalentapi.redis.lettuce.pool.max-wait=10000

#rocketmq
rocketmq.enabled=true
rocketmq.consumer-enabled=false
rocketmq.name-server=************:9876;***********:9876;************:9876
rocketmq.producer.group=sptalentrv-producer-group
rocketmq.producer.maxMessageSize=4194304
rocketmq.producer.compress-message-body-threshold=4096
rocketmq.producer.sendMessageTimeout=3000
rocketmq.producer.retryTimesWhenSendFailed=2
rocketmq.producer.retryTimesWhenSendAsyncFailed=0
rocketmq.consumer.consumeThreadMin=2
rocketmq.consumer.consumeThreadMax=3
rocketmq.consumer.consumeMessageBatchMaxSize=1
server.tomcat.threads.max=20
server.tomcat.max-connections=2000
server.tomcat.accept-count=2000
server.tomcat.uri-encoding=UTF-8

#es
spring.data.elasticsearch.repositories.enabled=false
spring.elasticsearch.rest.uris=http://10.130.7.182:9200
spring.elasticsearch.rest.username=elastic
spring.elasticsearch.rest.password=m5yC6Av3VS9Y

#job
xxl.job.enabled=false
xxl.job.executor-appname=sptalentrvapi-executor

#ucache
ucache.enabled=true
ucache.remote.dft.type=redis#waf-redisProperties
ucache.local.enabled=false
ucache.local.type=caffeine
ucache.local.maximumSize=5000
ucache.local.expireAfterWrite=3000
ucache.bigKey.enableSizeLimit=true
ucache.bigKey.warnSize=10k
ucache.bigKey.forbiddenSize=1m
ucache.bigKey.forbiddenException=false
ucache.pierceDefend.cacheNullValue=true

aom.datasource.defaultds=rvDataSource
#aom.datasource.dsmap.proj_rcpd=rvDataSource
aom.datasource.dsmap.actv_perf=rvDataSource
aom.datasource.dsmap.actv_prof=rvDataSource

aom.datasource.tblprefix=rv_
aom.datasource.tblprefixmap.proj_rcpd=rv_
aom.datasource.tblprefixmap.actv_perf=rv_
aom.datasource.tblprefixmap.actv_prof=rv_

## UTree
ubiz.utree.enabled=true
ubiz.utree.table-name-prefix=rv
# 以下配置如果已经开启, 则忽略
waf.lock-service.enabled=true
base.swagger.base-package=com.yxt.ubiz.tree.adapter.controller.api

leaf.port=${server.port}

sprv.gridColorMap.FFF1F1=#FF4D4F
sprv.gridColorMap.FFF6ED=#FA8C16
sprv.gridColorMap.EFF1FC=#2E4DD9
sprv.gridColorMap.FDEFF7=#EB2F96
sprv.gridColorMap.EEF4EB=#237804
sprv.gridColorMap.EEECF2=#22075E
sprv.gridColorMap.F7EDE6=#8D5025
sprv.gridColorMap.DBE8F7=#47596C
sprv.gridColorMap.F4F6DE=#737555
sprv.gridColorMap.F4F4F4=#757575