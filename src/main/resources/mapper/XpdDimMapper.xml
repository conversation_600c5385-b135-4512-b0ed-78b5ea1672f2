<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_dim-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="sd_dim_id" property="sdDimId" />
    <result column="dim_type" property="dimType" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
    <result column="score_total" property="scoreTotal" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, sd_dim_id, dim_type, deleted, create_user_id, create_time, update_user_id, 
    update_time, score_total
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_dim
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimPO">
    <!--@mbg.generated-->
    insert into rv_xpd_dim (id, org_id, xpd_id, sd_dim_id, dim_type, deleted, create_user_id, 
      create_time, update_user_id, update_time, score_total)
    values (#{id}, #{orgId}, #{xpdId}, #{sdDimId}, #{dimType}, #{deleted}, #{createUserId}, 
      #{createTime}, #{updateUserId}, #{updateTime}, #{scoreTotal})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimPO">
    <!--@mbg.generated-->
    update rv_xpd_dim
    set org_id = #{orgId},
      xpd_id = #{xpdId},
      sd_dim_id = #{sdDimId},
      dim_type = #{dimType},
      deleted = #{deleted},
      create_user_id = #{createUserId},
      create_time = #{createTime},
      update_user_id = #{updateUserId},
      update_time = #{updateTime},
      score_total = #{scoreTotal}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_xpd_dim
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="xpd_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.xpdId}
        </foreach>
      </trim>
      <trim prefix="sd_dim_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.sdDimId}
        </foreach>
      </trim>
      <trim prefix="dim_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.dimType}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="score_total = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.scoreTotal}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_dim
    (id, org_id, xpd_id, sd_dim_id, dim_type, deleted, create_user_id, create_time, update_user_id, 
      update_time, score_total)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.sdDimId}, #{item.dimType}, #{item.deleted}, 
        #{item.createUserId}, #{item.createTime}, #{item.updateUserId}, #{item.updateTime}, 
        #{item.scoreTotal})
    </foreach>
  </insert>

  <update id="batchUpdateScore">
    <foreach collection="list" item="item" separator=";">
      update rv_xpd_dim set score_total = #{item.scoreTotal} where id = #{item.id}
    </foreach>
  </update>

    <select id="selectById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from rv_xpd_dim
        where id = #{id}
          and deleted = 0
    </select>

    <select id="listByXpdId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_xpd_dim
        where org_id = #{orgId}
        and deleted = 0 and xpd_id = #{xpdId}
    </select>

  <select id="selectByDimIds" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from rv_xpd_dim
      where org_id = #{orgId}
      and deleted = 0
      <choose>
          <when test="sdDimIds != null and sdDimIds.size() != 0">
              and sd_dim_id in
              <foreach close=")" collection="sdDimIds" index="index" item="item" open="(" separator=",">
                  #{item}
              </foreach>
          </when>
          <otherwise>
              <!--@ignoreSql-->
              and 1 != 1
          </otherwise>
      </choose>
    </select>

  <update id="deleteBySdDimIds">
    update rv_xpd_dim
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
    <choose>
      <when test="sdDimIds != null and sdDimIds.size() != 0">
        and sd_dim_id in
        <foreach close=")" collection="sdDimIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </update>

  <update id="deleteByXpdId">
    update rv_xpd_dim
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
    </update>

  <select id="selectByXpdIdAndSdDimId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_dim
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and sd_dim_id = #{sdDimId}
      and deleted = 0
  </select>

  <select id="selectByXpdId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_dim
    where org_id = #{orgId}
    and deleted = 0
    and xpd_id = #{xpdId}
    <if test="excludePerf != null and excludePerf == 1">
      and dim_type != 5
    </if>


  </select>


  <select id="selectByXpdIdPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_dim
    where org_id = #{orgId}
    and deleted = 0
    and xpd_id = #{xpdId}
    <if test="excludePerf != null and excludePerf == 1">
      and dim_type != 5
    </if>
    <if test="searchDimIds != null and searchDimIds.size() > 0">
      and sd_dim_id in
      <foreach close=")" collection="searchDimIds" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>

  </select>

  <select id="countByOrgIdAndXpdId" resultType="java.lang.Integer">
    select count(*)
    from rv_xpd_dim
    where  org_id = #{orgId}
      AND xpd_id = #{xpdId}
      AND deleted = 0
  </select>


</mapper>