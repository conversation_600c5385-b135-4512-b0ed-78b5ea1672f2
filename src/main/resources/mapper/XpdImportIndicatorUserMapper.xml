<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdImportIndicatorUserMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportIndicatorUserPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_import_indicator_user-->
    <id column="id" property="id" />
    <result column="import_id" property="importId" />
    <result column="xpd_id" property="xpdId" />
    <result column="org_id" property="orgId" />
    <result column="user_id" property="userId" />
    <result column="score_total" property="scoreTotal" />
    <result column="sd_indicator_id" property="sdIndicatorId" />
    <result column="sd_indicator_score" property="sdIndicatorScore" />
    <result column="qualified" property="qualified" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_user_id" property="updateUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="deleted" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, import_id, xpd_id, org_id, user_id, score_total, sd_indicator_id, sd_indicator_score, 
    qualified, create_user_id, update_user_id, create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_import_indicator_user
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportIndicatorUserPO">
    <!--@mbg.generated-->
    insert into rv_xpd_import_indicator_user (id, import_id, xpd_id, org_id, user_id, score_total, sd_indicator_id, 
      sd_indicator_score, qualified, create_user_id, update_user_id, create_time, 
      update_time, deleted)
    values (#{id}, #{importId}, #{xpdId}, #{orgId}, #{userId}, #{scoreTotal}, #{sdIndicatorId}, 
      #{sdIndicatorScore}, #{qualified}, #{createUserId}, #{updateUserId}, #{createTime}, 
      #{updateTime}, #{deleted})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportIndicatorUserPO">
    <!--@mbg.generated-->
    insert into rv_xpd_import_indicator_user
    (id, import_id, xpd_id, org_id, user_id, score_total, sd_indicator_id, sd_indicator_score, 
      qualified, create_user_id, update_user_id, create_time, update_time, deleted)
    values
    (#{id}, #{importId}, #{xpdId}, #{orgId}, #{userId}, #{scoreTotal}, #{sdIndicatorId}, 
      #{sdIndicatorScore}, #{qualified}, #{createUserId}, #{updateUserId}, #{createTime}, 
      #{updateTime}, #{deleted})
    on duplicate key update 
    id = #{id}, 
    import_id = #{importId}, 
    xpd_id = #{xpdId}, 
    org_id = #{orgId}, 
    user_id = #{userId}, 
    score_total = #{scoreTotal}, 
    sd_indicator_id = #{sdIndicatorId}, 
    sd_indicator_score = #{sdIndicatorScore}, 
    qualified = #{qualified}, 
    create_user_id = #{createUserId}, 
    update_user_id = #{updateUserId}, 
    create_time = #{createTime}, 
    update_time = #{updateTime}, 
    deleted = #{deleted}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportIndicatorUserPO">
    <!--@mbg.generated-->
    insert into rv_xpd_import_indicator_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="importId != null">
        import_id,
      </if>
      <if test="xpdId != null">
        xpd_id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="scoreTotal != null">
        score_total,
      </if>
      <if test="sdIndicatorId != null">
        sd_indicator_id,
      </if>
      <if test="sdIndicatorScore != null">
        sd_indicator_score,
      </if>
      <if test="qualified != null">
        qualified,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="importId != null">
        #{importId},
      </if>
      <if test="xpdId != null">
        #{xpdId},
      </if>
      <if test="orgId != null">
        #{orgId},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="scoreTotal != null">
        #{scoreTotal},
      </if>
      <if test="sdIndicatorId != null">
        #{sdIndicatorId},
      </if>
      <if test="sdIndicatorScore != null">
        #{sdIndicatorScore},
      </if>
      <if test="qualified != null">
        #{qualified},
      </if>
      <if test="createUserId != null">
        #{createUserId},
      </if>
      <if test="updateUserId != null">
        #{updateUserId},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="deleted != null">
        #{deleted},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      <if test="importId != null">
        import_id = #{importId},
      </if>
      <if test="xpdId != null">
        xpd_id = #{xpdId},
      </if>
      <if test="orgId != null">
        org_id = #{orgId},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="scoreTotal != null">
        score_total = #{scoreTotal},
      </if>
      <if test="sdIndicatorId != null">
        sd_indicator_id = #{sdIndicatorId},
      </if>
      <if test="sdIndicatorScore != null">
        sd_indicator_score = #{sdIndicatorScore},
      </if>
      <if test="qualified != null">
        qualified = #{qualified},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
    </trim>
  </insert>

  <select id="queryByUserIds" resultType="com.yxt.talent.rv.infrastructure.repository.xpd.bean.XpdIndicatorResultDto">
    select user_id,sd_indicator_id,sd_indicator_score as score,qualified from rv_xpd_import_indicator_user
    where org_id = #{orgId} and xpd_id = #{xpdId} and import_id = #{importId} and deleted = 0
    and user_id in
    <foreach collection="userIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and sd_indicator_id in
    <foreach collection="indicatorIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectImportAct" parameterType="java.lang.String" resultType="com.yxt.talent.rv.application.xpd.common.dto.ImportIndicatorNumDto">
    <!--@mbg.generated-->
    select import_id importId, count(sd_indicator_id) indicatorNum
    from rv_xpd_import_indicator_user where org_id = #{orgId}
    and xpd_id = #{xpdId} and user_id = #{userId} group by import_id
  </select>

  <select id="selectUserCount" resultType="com.yxt.talent.rv.controller.manage.xpd.actvimpt.dto.XpdImportUserDTO">
    select
    import_id importId, count(distinct(user_id)) userNum
    from rv_xpd_import_indicator_user
    where org_id = #{orgId}
    and xpd_id = #{xpdId}
    and import_id in
    <foreach collection="importIds" item="importId" open="(" separator="," close=")">
      #{importId}
    </foreach>
    and deleted = 0
    group by import_id
  </select>

  <select id="selectByUserIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_import_indicator_user
    where org_id = #{orgId}
    and xpd_id = #{xpdId}
    and import_id = #{importId}
    and user_id in
    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
      #{userId}
    </foreach>
    and deleted = 0
  </select>

  <select id="selectByUserIdsPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_import_indicator_user
    where org_id = #{orgId}
    and xpd_id = #{xpdId}
    and import_id = #{importId}
    and user_id in
    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
      #{userId}
    </foreach>
    and deleted = 0
  </select>
  <insert id="insertBatch">
    insert into rv_xpd_import_indicator_user(id,import_id,xpd_id,
    org_id,user_id,score_total,
    sd_indicator_id,sd_indicator_score,qualified,
    create_user_id,update_user_id,create_time,
    update_time,deleted)
    values
    <foreach collection="xpdImportIndicatorUserPOCollection" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR},#{item.importId,jdbcType=VARCHAR},#{item.xpdId,jdbcType=VARCHAR},
      #{item.orgId,jdbcType=VARCHAR},#{item.userId,jdbcType=VARCHAR},#{item.scoreTotal,jdbcType=DECIMAL},
      #{item.sdIndicatorId,jdbcType=VARCHAR},#{item.sdIndicatorScore,jdbcType=DECIMAL},#{item.qualified,jdbcType=NUMERIC},
      #{item.createUserId,jdbcType=VARCHAR},#{item.updateUserId,jdbcType=VARCHAR},#{item.createTime},
      #{item.updateTime},#{item.deleted,jdbcType=NUMERIC})
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    UPDATE rv_xpd_import_indicator_user
    <set>
      import_id = CASE
      <foreach collection="list" item="item" index="index">
        WHEN id = #{item.id} THEN #{item.importId}
      </foreach>
      END,
      xpd_id = CASE
      <foreach collection="list" item="item" index="index">
        WHEN id = #{item.id} THEN #{item.xpdId}
      </foreach>
      END,
      org_id = CASE
      <foreach collection="list" item="item" index="index">
        WHEN id = #{item.id} THEN #{item.orgId}
      </foreach>
      END,
      user_id = CASE
      <foreach collection="list" item="item" index="index">
        WHEN id = #{item.id} THEN #{item.userId}
      </foreach>
      END,
      sd_indicator_id = CASE
      <foreach collection="list" item="item" index="index">
        WHEN id = #{item.id} THEN #{item.sdIndicatorId}
      </foreach>
      END,
      sd_indicator_score = CASE
      <foreach collection="list" item="item" index="index">
        WHEN id = #{item.id} THEN #{item.sdIndicatorScore}
      </foreach>
      END,
      qualified = CASE
      <foreach collection="list" item="item" index="index">
        WHEN id = #{item.id} THEN #{item.qualified}
      </foreach>
      END,
      create_user_id = CASE
      <foreach collection="list" item="item" index="index">
        WHEN id = #{item.id} THEN #{item.createUserId}
      </foreach>
      END,
      update_user_id = CASE
      <foreach collection="list" item="item" index="index">
        WHEN id = #{item.id} THEN #{item.updateUserId}
      </foreach>
      END,
      deleted = CASE
      <foreach collection="list" item="item" index="index">
        WHEN id = #{item.id} THEN #{item.deleted}
      </foreach>
      END
    </set>
    WHERE id IN
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item.id}
    </foreach>
  </update>

  <update id="deleteByImportIdAndUserId">
    update rv_xpd_import_indicator_user
    set  deleted = 1, update_time = now(), update_user_id = #{operator}
    where org_id = #{orgId} and import_id = #{importId} and user_id = #{userId}
  </update>

  <update id="deleteByXpdId">
    update rv_xpd_import_indicator_user
    set  deleted = 1, update_time = now(), update_user_id = #{operator}
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
  </update>

  <update id="deleteByXpdIdAndImportIds">
    update rv_xpd_import_indicator_user
    set  deleted = 1, update_time = now(), update_user_id = #{operator}
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
    and import_id in
    <foreach collection="importIds" item="importId" open="(" close=")" separator=",">
      #{importId}
    </foreach>
  </update>

  <select id="findDimNum" resultType="com.yxt.talent.rv.controller.manage.xpd.actvimpt.dto.ImportDimNumDTO">
    select a.sd_dim_id dimId, count(*) dimNum from rv_xpd_import a
      left join rv_xpd_import_indicator_user b
      on a.org_id = b.org_id
      and a.id = b.import_id
    where a.org_id = #{orgId}
      and a.xpd_id = #{xpdId} and b.user_id = #{userId}
      and a.deleted = 0 and b.deleted = 0
      and a.import_type = 0
    group by a.sd_dim_id
  </select>
</mapper>