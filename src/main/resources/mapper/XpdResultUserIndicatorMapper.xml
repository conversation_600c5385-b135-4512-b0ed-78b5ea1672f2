<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdResultUserIndicatorMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserIndicatorPO">
        <!--@mbg.generated-->
        <!--@Table rv_xpd_result_user_indicator-->
        <id column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="xpd_id" property="xpdId"/>
        <result column="result_dim_id" property="resultDimId"/>
        <result column="sd_indicator_id" property="sdIndicatorId"/>
        <result column="user_id" property="userId"/>
        <result column="score_value" property="scoreValue"/>
        <result column="qualified" property="qualified"/>
        <result column="perf_summary" property="perfSummary"/>
        <result column="perf_result_id" property="perfResultId"/>
        <result column="result_detail" property="resultDetail"/>
        <result column="calc_batch_no" property="calcBatchNo"/>
        <result column="deleted" property="deleted"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, org_id, xpd_id, result_dim_id, sd_indicator_id, user_id, score_value,
        qualified, perf_summary, perf_result_id, result_detail, calc_batch_no, deleted, create_user_id,
        create_time, update_user_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_result_user_indicator
        where id = #{id}
    </select>
    <insert id="insert"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserIndicatorPO">
        <!--@mbg.generated-->
        insert into rv_xpd_result_user_indicator (id, org_id, xpd_id, result_dim_id, sd_indicator_id, user_id,
        score_value, qualified, perf_summary, perf_result_id, result_detail,
        calc_batch_no, deleted, create_user_id, create_time, update_user_id, update_time
        )
        values (#{id}, #{orgId}, #{xpdId}, #{resultDimId}, #{sdIndicatorId}, #{userId},
        #{scoreValue}, #{qualified}, #{perfSummary}, #{perfResultId}, #{resultDetail},
        #{calcBatchNo}, #{deleted}, #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime}
        )
    </insert>
    <insert id="insertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserIndicatorPO">
        <!--@mbg.generated-->
        insert into rv_xpd_result_user_indicator
        (id, org_id, xpd_id, result_dim_id, sd_indicator_id, user_id, score_value,
        qualified, perf_summary, perf_result_id, result_detail, calc_batch_no, deleted,
        create_user_id, create_time, update_user_id, update_time)
        values
        (#{id}, #{orgId}, #{xpdId}, #{resultDimId}, #{sdIndicatorId}, #{userId},
        #{scoreValue}, #{qualified}, #{perfSummary}, #{perfResultId}, #{resultDetail},
        #{calcBatchNo}, #{deleted}, #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime}
        )
        on duplicate key update
        id = #{id},
        org_id = #{orgId},
        xpd_id = #{xpdId},
        result_dim_id = #{resultDimId},
        sd_indicator_id = #{sdIndicatorId},
        user_id = #{userId},
        score_value = #{scoreValue},
        qualified = #{qualified},
        perf_summary = #{perfSummary},
        perf_result_id = #{perfResultId},
        result_detail = #{resultDetail},
        calc_batch_no = #{calcBatchNo},
        deleted = #{deleted},
        create_user_id = #{createUserId},
        create_time = #{createTime},
        update_user_id = #{updateUserId},
        update_time = #{updateTime}
    </insert>

    <select id="queryIgnoreDelByIndUserIds"
            resultType="com.yxt.talent.rv.infrastructure.repository.xpd.UserResultIdDTO">
        select id,user_id from rv_xpd_result_user_indicator where org_id = #{orgId} and xpd_id = #{xpdId}
        and sd_indicator_id = #{sdIndicatorId} and user_id in
        <foreach close=")" collection="userIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findByIndicatorIds"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_result_user_indicator
        where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
        and sd_indicator_id in
        <foreach collection="indicatorIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="batchUpdateResult">
        <foreach collection="list" item="item" separator=";">
            update rv_xpd_result_user_indicator set
            result_dim_id = #{item.resultDimId},
            score_value = #{item.scoreValue},
            qualified = #{item.qualified},
            result_detail = #{item.resultDetail},
            calc_batch_no = #{item.calcBatchNo},
            perf_result_id = #{item.perfResultId},
            perf_summary = #{item.perfSummary},
            deleted = #{item.deleted},
            update_user_id = #{item.updateUserId},
            update_time = #{item.updateTime} where id = #{item.id}
        </foreach>
    </update>

    <select id="selectByXpdIdAndResultDimIdAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_result_user_indicator where org_id = #{orgId} and xpd_id = #{xpdId}
        and result_dim_id = #{resultDimId} and user_id = #{userId}
    </select>

  <select id="findByIndicatorIdsAndUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_result_user_indicator
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
    and sd_indicator_id in
    <foreach collection="indicatorIds" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and user_id = #{userId}
  </select>
    <select id="findByXpdId"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_result_user_indicator
        where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
    </select>

    <select id="findByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_result_user_indicator
        where org_id = #{orgId}
          and xpd_id = #{xpdId}
          and user_id = #{userId}
          and deleted = 0
    </select>
</mapper>