<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityPerfResultConfMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfResultConfPO">
    <!--@mbg.generated-->
    <!--@Table rv_activity_perf_result_conf-->
    <id column="id" property="id" />
    <result column="actv_perf_id" property="actvPerfId" />
    <result column="org_id" property="orgId" />
    <result column="result_name" property="resultName" />
    <result column="score" property="score" />
    <result column="rule_score" property="ruleScore" />
    <result column="qualified" property="qualified" />
    <result column="rule_conf" property="ruleConf" />
    <result column="order_num" property="orderNum" />
    <result column="rule_display" property="ruleDisplay" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_user_id" property="updateUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="deleted" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, actv_perf_id, org_id, result_name, score, rule_score, qualified, rule_conf, rule_display,order_num,
    create_user_id, update_user_id, create_time, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from rv_activity_perf_result_conf
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfResultConfPO">
    <!--@mbg.generated-->
    insert into rv_activity_perf_result_conf (id, actv_perf_id, org_id, result_name, score, rule_score, qualified,
      rule_conf, rule_display, order_num,create_user_id, update_user_id, create_time, update_time,
      deleted)
    values (#{id}, #{actvPerfId}, #{orgId}, #{resultName}, #{score}, #{ruleScore}, #{qualified},
      #{ruleConf}, #{ruleDisplay}, #{orderNum}, #{createUserId}, #{updateUserId}, #{createTime}, #{updateTime},
      #{deleted})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfResultConfPO">
    <!--@mbg.generated-->
    insert into rv_activity_perf_result_conf
    (id, actv_perf_id, org_id, result_name, score, rule_score, qualified, rule_conf,
      rule_display,order_num, create_user_id, update_user_id, create_time, update_time, deleted
      )
    values
    (#{id}, #{actvPerfId}, #{orgId}, #{resultName}, #{score}, #{ruleScore}, #{qualified},
      #{ruleConf}, #{ruleDisplay}, #{orderNum}, #{createUserId}, #{updateUserId}, #{createTime}, #{updateTime},
      #{deleted})
    on duplicate key update
    id = #{id},
    actv_perf_id = #{actvPerfId},
    org_id = #{orgId},
    result_name = #{resultName},
    score = #{score},
    rule_score = #{ruleScore},
    qualified = #{qualified},
    rule_conf = #{ruleConf},
    rule_display = #{ruleDisplay},
    order_num = #{orderNum},
    create_user_id = #{createUserId},
    update_user_id = #{updateUserId},
    create_time = #{createTime},
    update_time = #{updateTime},
    deleted = #{deleted}
  </insert>

<!--auto generated by MybatisCodeHelper on 2024-12-09-->
  <insert id="insertList">
        INSERT INTO rv_activity_perf_result_conf(
        id,
        actv_perf_id,
        org_id,
        result_name,
        score,
        rule_score,
        qualified,
        rule_conf,
        rule_display,
        order_num,
        create_user_id,
        update_user_id,
        create_time,
        update_time,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.actvPerfId},
            #{element.orgId},
            #{element.resultName},
            #{element.score},
            #{element.ruleScore},
            #{element.qualified},
            #{element.ruleConf},
            #{element.ruleDisplay},
            #{element.orderNum},
            #{element.createUserId},
            #{element.updateUserId},
            #{element.createTime},
            #{element.updateTime},
            #{element.deleted}
            )
        </foreach>
    </insert>

    <select id="selectByActivityId"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_activity_perf_result_conf
        where org_id = #{orgId} and actv_perf_id = #{actvPerfId} and deleted = 0 order by order_num asc
    </select>
    <select id="findByOrgIdAndIds"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_activity_perf_result_conf
        where org_id = #{orgId} and id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and deleted = 0
    </select>

    <update id="deleteByActivityId">
        update rv_activity_perf_result_conf set deleted = 1 where org_id = #{orgId} and actv_perf_id = #{actvPerfId} and deleted = 0
    </update>

</mapper>