<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridLevelMapper">
    <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO">
        <!--@mbg.generated-->
        <!--@Table rv_xpd_grid_level-->
        <id column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="xpd_id" property="xpdId"/>
        <result column="grid_id" property="gridId"/>
        <result column="level_name" property="levelName"/>
        <result column="level_name_i18n" property="levelNameI18n"/>
        <result column="order_index" property="orderIndex"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_id" property="updateUserId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, org_id, xpd_id, grid_id, level_name, level_name_i18n, order_index, deleted, create_time,
        create_user_id, update_time, update_user_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid_level
        where id = #{id}
    </select>
    <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO">
        <!--@mbg.generated-->
        insert into rv_xpd_grid_level (id, org_id, xpd_id, grid_id, level_name, level_name_i18n, order_index,
        deleted, create_time, create_user_id, update_time, update_user_id)
        values (#{id}, #{orgId}, #{xpdId}, #{gridId}, #{levelName}, #{levelNameI18n}, #{orderIndex},
        #{deleted}, #{createTime}, #{createUserId}, #{updateTime}, #{updateUserId})
    </insert>
    <insert id="insertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO">
        <!--@mbg.generated-->
        insert into rv_xpd_grid_level
        (id, org_id, xpd_id, grid_id, level_name, level_name_i18n, order_index, deleted,
        create_time, create_user_id, update_time, update_user_id)
        values
        (#{id}, #{orgId}, #{xpdId}, #{gridId}, #{levelName}, #{levelNameI18n}, #{orderIndex},
        #{deleted}, #{createTime}, #{createUserId}, #{updateTime}, #{updateUserId})
        on duplicate key update
        id = #{id},
        org_id = #{orgId},
        xpd_id = #{xpdId},
        grid_id = #{gridId},
        level_name = #{levelName},
        level_name_i18n = #{levelNameI18n},
        order_index = #{orderIndex},
        deleted = #{deleted},
        create_time = #{createTime},
        create_user_id = #{createUserId},
        update_time = #{updateTime},
        update_user_id = #{updateUserId}
    </insert>

    <select id="listByGridIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid_level
        where org_id = #{orgId}
        <choose>
            <when test="gridIds != null and gridIds.size > 0">
                and grid_id in
                <foreach collection="gridIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and 1=2
            </otherwise>
        </choose>
        and deleted = 0
    </select>

    <select id="listByGridId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid_level
        where org_id = #{orgId}
        and grid_id = #{gridId}
        and deleted = 0
        order by order_index
    </select>

    <select id="listByGridIdDesc" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid_level
        where org_id = #{orgId}
        and grid_id = #{gridId}
        and deleted = 0
        order by order_index desc
    </select>

    <select id="listByGridIdReverse" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid_level
        where org_id = #{orgId}
        and grid_id = #{gridId}
        and deleted = 0
        order by order_index desc
    </select>

    <select id="listByXpdId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid_level
        where org_id = #{orgId}
        and xpd_id = #{xpdId}
        and deleted = 0
        order by order_index
    </select>

    <update id="deleteGridLevel">
        update rv_xpd_grid_level set deleted = 1, update_time = now(), update_user_id = #{userId}
        where org_id = #{orgId} and grid_id = #{gridId}
        and deleted = 0
    </update>
    <insert id="insertBatch">
        insert into rv_xpd_grid_level(id,org_id,xpd_id,
        grid_id,level_name,level_name_i18n,
        order_index,deleted,create_time,
        create_user_id,update_time,update_user_id)
        values
        <foreach collection="coll" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR},#{item.orgId,jdbcType=VARCHAR},#{item.xpdId,jdbcType=VARCHAR},
            #{item.gridId,jdbcType=VARCHAR},#{item.levelName,jdbcType=VARCHAR},#{item.levelNameI18n,jdbcType=VARCHAR},
            #{item.orderIndex,jdbcType=NUMERIC},#{item.deleted,jdbcType=NUMERIC},#{item.createTime},
            #{item.createUserId,jdbcType=VARCHAR},#{item.updateTime},#{item.updateUserId,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE rv_xpd_grid_level
            SET
            org_id = #{item.orgId},
            xpd_id = #{item.xpdId},
            grid_id = #{item.gridId},
            level_name = #{item.levelName},
            level_name_i18n = #{item.levelNameI18n},
            order_index = #{item.orderIndex},
            deleted = #{item.deleted},
            update_time = #{item.updateTime},
            update_user_id = #{item.updateUserId},
            create_time = #{item.createTime},
            create_user_id = #{item.createUserId}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="listByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid_level
        where org_id = #{orgId}
        and deleted = 0
        order by order_index
    </select>

    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into rv_xpd_grid_level
        (id, org_id, xpd_id, grid_id, level_name, level_name_i18n, order_index,
        deleted, create_time, create_user_id, update_time, update_user_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR}, #{item.orgId,jdbcType=CHAR}, #{item.xpdId,jdbcType=CHAR},
            #{item.gridId,jdbcType=VARCHAR}, #{item.levelName,jdbcType=VARCHAR}, #{item.levelNameI18n,jdbcType=VARCHAR},
            #{item.orderIndex,jdbcType=INTEGER}, #{item.deleted,jdbcType=TINYINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.createUserId,jdbcType=CHAR},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateUserId,jdbcType=CHAR})
        </foreach>
    </insert>

    <update id="deleteByXpdId">
        update rv_xpd_grid_level
        set deleted = 1,
        update_time = now(),
        update_user_id = #{userId}
        where org_id = #{orgId}
        and xpd_id = #{xpdId}
        and deleted = 0
    </update>

    <select id="listByXpdIdPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid_level
        where org_id = #{orgId}
        and xpd_id = #{xpdId}
        and deleted = 0
        order by order_index
    </select>
    <select id="selectByIds" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_grid_level
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
</mapper>