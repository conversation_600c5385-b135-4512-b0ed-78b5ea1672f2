<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.RvActivityObjectiveResultMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityObjectiveResultPO">
    <!--@mbg.generated-->
    <!--@Table rv_activity_objective_result-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="actv_id" property="actvId" />
    <result column="user_id" property="userId" />
    <result column="base_actv_result_id" property="baseActvResultId" />
    <result column="objective_id" property="objectiveId" />
    <result column="objective_mode_id" property="objectiveModeId" />
    <result column="objective_type" property="objectiveType" />
    <result column="objective_score" property="objectiveScore" />
    <result column="objective_total_score" property="objectiveTotalScore" />
    <result column="objective_level" property="objectiveLevel" />
    <result column="objective_result" property="objectiveResult" />
    <result column="deleted" property="deleted" />
    <result column="create_time" property="createTime" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_time" property="updateTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="db_archived" property="dbArchived" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, actv_id, user_id, base_actv_result_id, objective_id, objective_mode_id, 
    objective_type, objective_score, objective_total_score, objective_level, objective_result, 
    deleted, create_time, create_user_id, update_time, update_user_id, db_archived
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_activity_objective_result
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityObjectiveResultPO">
    <!--@mbg.generated-->
    insert into rv_activity_objective_result (id, org_id, actv_id, user_id, base_actv_result_id, objective_id, 
      objective_mode_id, objective_type, objective_score, objective_total_score, 
      objective_level, objective_result, deleted, create_time, create_user_id, 
      update_time, update_user_id, db_archived)
    values (#{id}, #{orgId}, #{actvId}, #{userId}, #{baseActvResultId}, #{objectiveId}, 
      #{objectiveModeId}, #{objectiveType}, #{objectiveScore}, #{objectiveTotalScore}, 
      #{objectiveLevel}, #{objectiveResult}, #{deleted}, #{createTime}, #{createUserId}, 
      #{updateTime}, #{updateUserId}, #{dbArchived})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityObjectiveResultPO">
    <!--@mbg.generated-->
    update rv_activity_objective_result
    set org_id = #{orgId},
      actv_id = #{actvId},
      user_id = #{userId},
      base_actv_result_id = #{baseActvResultId},
      objective_id = #{objectiveId},
      objective_mode_id = #{objectiveModeId},
      objective_type = #{objectiveType},
      objective_score = #{objectiveScore},
      objective_total_score = #{objectiveTotalScore},
      objective_level = #{objectiveLevel},
      objective_result = #{objectiveResult},
      deleted = #{deleted},
      create_time = #{createTime},
      create_user_id = #{createUserId},
      update_time = #{updateTime},
      update_user_id = #{updateUserId},
      db_archived = #{dbArchived}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_activity_objective_result
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="actv_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.actvId}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.userId}
        </foreach>
      </trim>
      <trim prefix="base_actv_result_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.baseActvResultId}
        </foreach>
      </trim>
      <trim prefix="objective_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.objectiveId}
        </foreach>
      </trim>
      <trim prefix="objective_mode_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.objectiveModeId}
        </foreach>
      </trim>
      <trim prefix="objective_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.objectiveType}
        </foreach>
      </trim>
      <trim prefix="objective_score = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.objectiveScore}
        </foreach>
      </trim>
      <trim prefix="objective_total_score = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.objectiveTotalScore}
        </foreach>
      </trim>
      <trim prefix="objective_level = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.objectiveLevel}
        </foreach>
      </trim>
      <trim prefix="objective_result = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.objectiveResult}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="db_archived = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.dbArchived}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_activity_objective_result
    (id, org_id, actv_id, user_id, base_actv_result_id, objective_id, objective_mode_id, 
      objective_type, objective_score, objective_total_score, objective_level, objective_result, 
      deleted, create_time, create_user_id, update_time, update_user_id, db_archived)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.actvId}, #{item.userId}, #{item.baseActvResultId}, 
        #{item.objectiveId}, #{item.objectiveModeId}, #{item.objectiveType}, #{item.objectiveScore}, 
        #{item.objectiveTotalScore}, #{item.objectiveLevel}, #{item.objectiveResult}, #{item.deleted}, 
        #{item.createTime}, #{item.createUserId}, #{item.updateTime}, #{item.updateUserId}, 
        #{item.dbArchived})
    </foreach>
  </insert>

  <select id="queryByUserIds" resultType="com.yxt.talent.rv.infrastructure.repository.xpd.bean.XpdIndicatorResultDto">
    select aor.user_id,
    aor.objective_id as sd_indicator_id,
    aor.objective_score as score,
    if(aor.objective_result in (1,3),1,0) as qualified,
    aor.ext as ext_json from rv_activity_objective_result aor
    where aor.org_id = #{orgId} and aor.actv_id = #{actvId} and aor.deleted = 0
    and aor.user_id in
    <foreach collection="userIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and aor.objective_id in
    <foreach collection="objectiveIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>
</mapper>