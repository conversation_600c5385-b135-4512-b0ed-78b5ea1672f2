<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdRuleMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRulePO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_rule-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="calc_type" property="calcType" />
    <result column="result_type" property="resultType" />
    <result column="calc_rule" property="calcRule" />
    <result column="formula" property="formula" />
    <result column="formula_display" property="formulaDisplay" />
    <result column="formula_expression" property="formulaExpression" />
    <result column="formula_exp_code" property="formulaExpCode" />
    <result column="level_type" property="levelType" />
    <result column="level_priority" property="levelPriority" />
    <result column="rule_desc" property="ruleDesc" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, calc_type, result_type, calc_rule, formula, formula_display, formula_expression, formula_exp_code,
    level_type, level_priority, rule_desc, deleted, create_user_id, create_time, update_user_id, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from rv_xpd_rule
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRulePO">
    <!--@mbg.generated-->
    insert into rv_xpd_rule (id, org_id, xpd_id, calc_type, result_type, calc_rule, formula,
      formula_display, formula_expression, formula_exp_code, level_type, level_priority, rule_desc, deleted, create_user_id, create_time,
      update_user_id, update_time)
    values (#{id}, #{orgId}, #{xpdId}, #{calcType}, #{resultType}, #{calcRule}, #{formula},
      #{formulaDisplay}, #{formulaExpression}, #{formulaExpCode}, #{levelType}, #{levelPriority}, #{ruleDesc}, #{deleted}, #{createUserId}, #{createTime},
      #{updateUserId}, #{updateTime})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRulePO">
    <!--@mbg.generated-->
    insert into rv_xpd_rule
    (id, org_id, xpd_id, calc_type, result_type, calc_rule, formula, formula_display, formula_expression, formula_exp_code,
      level_type, level_priority, rule_desc, deleted, create_user_id, create_time, update_user_id, update_time
      )
    values
    (#{id}, #{orgId}, #{xpdId}, #{calcType}, #{resultType}, #{calcRule}, #{formula},
      #{formulaDisplay}, #{formulaExpression}, #{formulaExpCode}, #{levelType}, #{levelPriority}, #{ruleDesc}, #{deleted}, #{createUserId}, #{createTime},
      #{updateUserId}, #{updateTime})
    on duplicate key update
    id = #{id},
    org_id = #{orgId},
    xpd_id = #{xpdId},
    calc_type = #{calcType},
    result_type = #{resultType},
    calc_rule = #{calcRule},
    formula = #{formula},
    formula_display = #{formulaDisplay},
    formula_expression = #{formulaExpression},
    formula_exp_code = #{formulaExpCode},
    level_type = #{levelType},
    level_priority = #{levelPriority},
    rule_desc = #{ruleDesc},
    deleted = #{deleted},
    create_user_id = #{createUserId},
    create_time = #{createTime},
    update_user_id = #{updateUserId},
    update_time = #{updateTime}
  </insert>

  <select id="getByXpdId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_rule
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0 limit 1
  </select>

  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_rule
    where id = #{id}
      and deleted = 0
  </select>

  <update id="deleteByXpdId">
    update rv_xpd_rule
    set deleted        = 1,
        update_user_id = #{userId},
        update_time    = sysdate(3)
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
  </update>
</mapper>