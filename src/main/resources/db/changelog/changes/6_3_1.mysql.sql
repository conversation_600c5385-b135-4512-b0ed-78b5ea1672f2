-- 校准会
create table rv_calimeet
(
    id              char(36)         not null comment '主键',
    org_id          char(36)         not null comment '机构id',
    xpd_id          char(36)         not null comment '盘点项目id, 指向rv_xpd.id',
    calimeet_status tinyint          not null default 0 comment '校准会状态(0-未开始，1-进行中，2-已结束)',
    calimeet_name   varchar(500)     not null comment '校准任务名称',
    calimeet_mode   tinyint          not null default 0 comment '组织形式(0-在线校准，1-线下校准)',
    calimeet_type   tinyint          not null default 0 comment '校准方式(0-维度分层结果，1-维度结果，2-指标结果)',
    start_time      datetime(3)      not null comment '开始时间',
    end_time        datetime(3)      null comment '结束时间',
    show_ratio      tinyint          not null default 0 comment '是否开启比例控制(0-否，1-是)',
    record          text comment '会议记录',
    deleted         tinyint unsigned not null default 0 comment '是否删除(0-否,1-是)',
    create_user_id  char(36)         not null comment '创建人主键',
    create_time     datetime(3)      not null comment '创建时间',
    update_user_id  char(36)         not null comment '更新人主键',
    update_time     datetime(3)      not null comment '更新时间',
    db_create_time  datetime(3)      not null default current_timestamp(3) null comment '数据创建时间(数据库专用，禁止用于业务)',
    db_update_time  datetime(3)      not null default current_timestamp(3) null on update current_timestamp(3) comment '数据修改时间(数据库专用，禁止用于业务)'
) comment '校准会';


-- 附件表【已有表，无需创建】
# create table rv_appendix
# (
#     id             char(36)     not null comment '主键',
#     org_id         char(36)     not null comment '机构id',
#     create_user_id char(36)     not null comment '创建人主键',
#     create_time    datetime(3)  not null comment '创建时间',
#     update_user_id char(36)     not null comment '更新人主键',
#     update_time    datetime(3)  not null comment '更新时间',
#     app_name       varchar(100) not null comment '附件名称',
#     app_url        varchar(255) not null comment '附件链接',
#     app_source     tinyint      not null default '0' comment '附件来源(1-校准会)',
#     app_source_id  char(36)              default null comment '附件来源主键Id',
#     db_create_time datetime(3)  not null default current_timestamp(3) comment '数据创建时间(数据库专用，禁止用于业务)',
#     db_update_time datetime(3)  not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用，禁止用于业务)',
#     primary key (id)
# ) engine = InnoDB comment ='附件表';


-- 校准会-干系人员表
create table rv_calimeet_participants
(
    id             char(36)         not null comment '主键',
    org_id         char(36)         not null comment '机构id',
    calimeet_id    char(36)         not null comment '会议id',
    user_id        char(36)         not null comment '干系人id',
    user_type      tinyint          not null default 0 comment '干系人类型(1-组织者，2-校准人)',
    cali_status tinyint not null default 0 comment '校准人是否完成校准任务(0-未完成，1-已完成)',
    deleted        tinyint unsigned not null default 0 comment '是否删除(0-否,1-是)',
    create_user_id char(36)         not null comment '创建人主键',
    create_time    datetime(3)      not null comment '创建时间',
    update_user_id char(36)         not null comment '更新人主键',
    update_time    datetime(3)      not null comment '更新时间',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用，禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用，禁止用于业务)',
    primary key (id),
    key idx_oid_cmid_uid (org_id, calimeet_id, user_id)
) engine = INNODB comment ='校准会-干系人员表';


-- 校准会-被校准人员表
create table rv_calimeet_user
(
    id             char(36)         not null comment '主键',
    org_id         char(36)         not null comment '机构id',
    calimeet_id    char(36)         not null comment '校准会id',
    user_id        char(36)         not null comment '人员id',
    cali_status    tinyint          not null default 0 comment '校准状态(0-未校准，1-已校准，2-无需缴准)',
    latest_record_id char(36) null comment '指向最新一条校准记录，rv_calimeet_record.id',
    deleted        tinyint unsigned not null default 0 comment '是否删除',
    create_user_id char(36)         not null comment '创建人主键',
    create_time    datetime(3)      not null comment '创建时间',
    update_user_id char(36)         not null comment '更新人主键',
    update_time    datetime(3)      not null comment '更新时间',
    db_create_time datetime(3)      not null default current_timestamp(3) null comment '数据创建时间(数据库专用，禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) null on update current_timestamp(3) comment '数据修改时间(数据库专用，禁止用于业务)',
    primary key (id),
    key idx_oid_cmid_uid_lrid (org_id, calimeet_id, user_id, latest_record_id)
) comment '校准会-被校准人员表';


-- 校准会-校准记录表
create table rv_calimeet_record
(
    id             char(36)         not null comment '主键',
    org_id         char(36)         not null comment '机构id',
    calimeet_id    char(36)         not null comment '校准会id',
    user_id        char(36)         not null comment '人员id',
    suggestion     text comment '发展建议',
    reason         text comment '校准原因',
    cali_details   json null comment '校准详情',
    result_details json null comment '维度结果',
    deleted        tinyint unsigned not null default 0 comment '是否删除',
    create_user_id char(36)         not null comment '创建人主键',
    create_time    datetime(3)      not null comment '创建时间',
    update_user_id char(36)         not null comment '更新人主键',
    update_time    datetime(3)      not null comment '更新时间',
    db_create_time datetime(3)      not null default current_timestamp(3) null comment '数据创建时间(数据库专用，禁止用于业务)',
    db_update_time datetime(3)      not null default current_timestamp(3) null on update current_timestamp(3) comment '数据修改时间(数据库专用，禁止用于业务)',
    primary key (id),
    key idx_oid_cmid_uid_utime (org_id, calimeet_id, user_id, update_time)
) comment '校准会-校准记录表';


-- 校准会-校准记录-维度组变更记录表
create table rv_calimeet_record_item
(
    id                  char(36)         not null comment 'id',
    org_id              char(36)         not null comment '机构id',
    calimeet_id         char(36)         not null default '' comment '校准会id',
    user_id             char(36)         not null default '' comment '被校准人id',
    calimeet_record_id  char(36)         not null default '' comment '指向校准记录表，rv_calimeet_record.id',
    dim_comb_id         char(36)         not null default '' comment '维度组合id',
    original_cell_index int unsigned     null comment '原落位宫格编号',
    cell_index          int unsigned     null comment '现落位宫格编号',
    cali_shift          int unsigned     null     default 0 comment '校准幅度',
    deleted             tinyint unsigned not null default 0 comment '是否删除',
    create_time         datetime(3)      not null comment '创建时间',
    create_user_id      char(36)         not null comment '创建人id',
    update_time         datetime(3)      not null comment '更新时间',
    update_user_id      char(36)         not null comment '更新人id',
    db_create_time      datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用,禁止用于业务)',
    db_update_time      datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用,禁止用于业务)',
    primary key (id),
    key idx_oid_cmid_uid (org_id, calimeet_id, user_id),
    key idx_oid_crid_dcid (org_id, calimeet_record_id),
    key idx_oid_cs (org_id, cali_shift)
) comment = '校准会-校准记录-维度组变更记录表';

-- 盘点用户维度组结果
create table rv_xpd_result_user_dimcomb
(
    id                  char(36)         not null comment '主键id',
    org_id              char(36)         not null default '' comment '机构id',
    xpd_id              char(36)         not null comment '盘点项目id',
    user_id             char(36)         not null default '' comment '用户id',
    dim_comb_id         char(36)         not null comment '维度组id',
    cell_index          int unsigned     not null comment '现落位宫格编号,',
    cell_id             char(36)         not null comment '现落位宫格id',
    deleted             tinyint unsigned not null default 0 comment '是否删除:0-未删除,1-已删除',
    create_user_id      char(36)         not null comment '创建人',
    create_time         datetime(3)      not null comment '创建时间',
    update_user_id      char(36)         not null comment '更新人',
    update_time         datetime(3)      not null comment '更新时间',
    db_create_time      datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用)',
    db_update_time      datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用)',
    calc_batch_no       int              not null default 0 comment '执行计算批次号',
    cali_flag           tinyint          not null default 0 comment '是否被校准过，0-否 1-是',
    original_snap       json             null comment '被校准结果覆盖之前的数据快照',
    primary key (id),
    key idx_oid_xid_uid_dcid (org_id, xpd_id, user_id, dim_comb_id)
) engine = INNODB comment ='盘点用户维度组结果';

-- 盘点用户校准结果的变更记录（用于审计和追溯，暂不用于业务）
create table rv_xpd_result_changelog
(
    id             char(36)         not null comment '主键id',
    org_id         char(36)         not null default '' comment '机构id',
    xpd_id         char(36)         not null comment '盘点项目id',
    user_id        char(36)         not null default '' comment '用户id',
    change_source  tinyint          not null default 0 comment '变更来源(0-校准会，1-盘点计算)',
    change_type    tinyint          not null default 0 comment '变更类型(0-项目结果表<rv_xpd_result_user>，1-维度组结果表<rv_xpd_result_user_dimcomb>，2-维度结果表<rv_xpd_result_user_dim>，3-指标结果表<rv_xpd_result_user_indicator>)',
    subject_id     char(36)         not null default '' comment '变更的主体id, 根据变更类型不同，指向不同的表主键：rv_xpd_result_user，rv_xpd_result_user_dimcomb，rv_xpd_result_user_dim，rv_xpd_result_user_indicator',
    source_id      char(36)         not null default '' comment '变更的来源id, 目前仅指向校准会：rv_calimeet.id',
    changelog      json             comment '变更记录',
    deleted        tinyint unsigned not null default 0 comment '是否删除:0-未删除,1-已删除',
    create_user_id char(36)         not null comment '创建人',
    create_time    datetime(3)      not null comment '创建时间',
    update_user_id char(36)         not null comment '更新人',
    update_time    datetime(3)      not null comment '更新时间',
    db_create_time datetime(3)      not null default current_timestamp(3) comment '数据创建时间(数据库专用)',
    db_update_time datetime(3)      not null default current_timestamp(3) on update current_timestamp(3) comment '数据修改时间(数据库专用)',
    primary key (id),
    key idx_oid_xid_uid_cs_sid_del (org_id, xpd_id, user_id, change_source, deleted)
) engine = INNODB comment ='盘点用户维度组结果';

-- 盘点用户结果
alter table sprv.rv_xpd_result_user
    add column cali_flag tinyint default 0 comment '是否被校准过，0-否 1-是',
    add column original_snap json null comment '被校准结果覆盖之前的原始的计算出来的数据快照';

-- 盘点用户维度结果
alter table sprv.rv_xpd_result_user_dim
    add column cali_flag tinyint default 0 comment '是否被校准过，0-否 1-是',
    add column original_snap json null comment '被校准结果覆盖之前的原始的计算出来的数据快照';

-- 盘点用户指标结果
alter table sprv.rv_xpd_result_user_indicator
    add column cali_flag tinyint default 0 comment '是否被校准过，0-否 1-是',
    add column original_snap json null comment '被校准结果覆盖之前的原始的计算出来的数据快照';