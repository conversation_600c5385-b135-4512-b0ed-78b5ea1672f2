package com.yxt.talent.rv;

import com.yxt.common.exception.ApiException;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.application.activity.PerfActivityService;
import com.yxt.talent.rv.application.activity.dto.PerfActivityDTO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfPO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * 以启动spring容器的方式进行测试
 */
@Transactional
@SpringBootTest
@AutoConfigureMockMvc
@Rollback(value = true)
@ActiveProfiles("native")
public class BaseSpringBootTest extends BaseTest { // NOSONAR

    @Autowired
    private PerfActivityService perfActivityService;

    @Test
    public void testInfo(){
//        String s = "99999999999999969b73866-c0ab-4659-9148-f653dfaaa3d169b73866-c0ab-4659-9148-f653dfaaa3d169b73866-c0ab-4659-9148-f653dfaaa3d169b73866-c0ab-4659-9148-f653dfaaa3d169b73866-c0ab-4659-9148-f653dfaaa3d169b73（复制）";
//        System.out.println(convertString(s, 200));
//
        String formData = "{\"name\": \"绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长绩效测试文字超长\", \"modelId\": \"f136595a-d0af-4ece-b1d5-fc82d2c915c2\", \"aomActId\": \"\", \"confList\": [{\"weight\": 100, \"periodId\": \"478c6d4d-854b-44df-8b6f-12dd0ab8b876\"}], \"evalType\": 2, \"indicator\": \"大局观念\", \"periodIds\": \"478c6d4d-854b-44df-8b6f-12dd0ab8b876\", \"description\": \"\", \"indicatorId\": \"83d8370c-e14b-409d-8c72-62b3c85a927a\", \"evalTimeType\": 1, \"scoreQualified\": 1}";
        PerfActivityDTO perfActivityDTO = BeanHelper.json2Bean(formData, PerfActivityDTO.class);
        if (perfActivityDTO == null){
            throw new ApiException(ExceptionKeys.ACTIVITY_FORM_DATA_ERROR);
        }
        perfActivityDTO.setName(convertString(perfActivityDTO.getName(), 200));
        //                perfActivityDTO.setAomActId(activity.getActvId());
        String orgId = "cc21f03c-003d-467f-9c06-91c562008fa9";
        perfActivityService.createPerfActivity(orgId, "c532b4d3-f206-4804-86d8-2ee8e490be68", perfActivityDTO);
    }

    public String convertString(String s, int size){
        // 获取字符串长度
        int length = s.length();

        // 判断字符串长度是否大于或等于200
        if (length >= size) {
            // 截取前200个字符
            String result = s.substring(0, size);
            System.out.println("截取后的字符串（前200个字符）：" + result);
        } else {
            // 字符串长度小于200，直接输出原字符串
            System.out.println("字符串长度小于200，直接输出原字符串：" + s);
        }
        return s;
    }
}
